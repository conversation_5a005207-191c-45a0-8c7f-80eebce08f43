package com.datatech.slgzt.dao.model.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 资源开通工单基础信息
 * StandardWorkOrderDO
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/3/13
 */

@Data
@TableName("WOC_STANDARD_WORK_ORDER")
public class StandardWorkOrderDO {

    @TableField("ID")
    private String id;

    /**
     * 订单标题
     */
    @TableField("ORDER_TITLE")
    private String orderTitle;

    /**
     * 订单编号
     */
    @TableField("ORDER_CODE")
    private String orderCode;

    /**
     * 订单类型
     */
    @TableField("ORDER_TYPE")
    private String orderType;

    /**
     * 计费号
     */
    @TableField("BILL_ID")
    private String billId;

    /**
     * 客户编码
     */
    @TableField("CUSTOM_NO")
    private String customNo;

    /**
     * 订单状态
     */
    @TableField("ORDER_STATUS")
    private String orderStatus;

    /**
     * 创建者id
     */
    @TableField("CREATED_BY")
    private Long createdBy;

    /**
     * 修改者id
     */
    @TableField("UPDATED_BY")
    private Long updatedBy;

    /**
     * 三级云领导ID
     */
    @TableField("CLOUD_LEADER_ID")
    private Long cloudLeaderId;

    /**
     * 二级业务部门领导ID
     */
    @TableField("BUSI_DEPART_LEADER_ID")
    private Long busiDepartLeaderId;

    /**
     * 三级业务部门领导ID
     */
    @TableField("LEVEL_THREE_LEADER_ID")
    private Long levelThreeLeaderId;

    /**
     * 二级云资源领导id
     */
    @TableField("SECOND_LEVEL_LEADER_ID")
    private Long secondLevelLeaderId;

    /**
     * 流程实例ID
     */
    @TableField("ACTIVITI_ID")
    private String activitiId;

    /**
     * 流程模板KEY
     */
    @TableField("ACTIVITE_KEY")
    private String activiteKey;

    /**
     * 订单描述
     */
    @TableField("ORDER_DESC")
    private String orderDesc;

    /**
     * 平台云编码
     */
    @TableField("DOMAIN_CODE")
    private String domainCode;

    /**
     * 云类型分类
     */
    @TableField("CATALOGUE_DOMAIN_CODE")
    private String catalogueDomainCode;

    /**
     * 云类型分类
     */
    @TableField("START_ROLE_CODE")
    private String startRoleCode;

    /**
     * 模块ID
     */
    @TableField("MODULE_ID")
    private Long moduleId;

    /**
     * 厂家名称
     */
    @TableField("MANUFACTURER")
    private String manufacturer;

    /**
     * 厂家联系人
     */
    @TableField("MANUFACTURER_CONTACTS")
    private String manufacturerContacts;

    /**
     * 厂家联系电话
     */
    @TableField("MANUFACTURER_MOBILE")
    private String manufacturerMobile;

    /**
     * 业务系统id
     */
    @TableField("BUSI_SYSTEM_ID")
    private Long busiSystemId;

    /**
     * 资源回收时存在，工单映射归档工单所属商品id集，多个时逗号拼接
     */
    @TableField("RECOVERY_GOODS_IDS")
    private String recoveryGoodsIds;

    /**
     * 商品类型，资源类型：ecs 云主机、evs 云硬盘、gcs GPU云主机、obs 对象存储、slb 负载均衡、nat NAT网关；网络类型：vpc VPC、network 网络
     */
    @TableField("RECOVERY_TYPE")
    private String recoveryType;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("MODIFY_TIME")
    private LocalDateTime modifyTime;

    /**
     * 订单状态 0 删除 1 正常
     */
    @TableField("STATUS")
    private Integer STATUS;


    /**
     * 流程当前节点code
     */
    @TableField("CURRENT_NODE_CODE")
    private String currentNodeCode;

    /**
     * 租户id
     */
    @TableField("TENANT_ID")
    private Long tenantId;

    /**
     * 租户名称
     */
    @TableField("TENANT_NAME")
    private String tenantName;

    //业务系统名称
    @TableField("BUSINESS_SYSTEM_NAME")
    private String businessSystemName;


    //创建用户名称
    @TableField("CREATED_USER_NAME")
    private String createdUserName;

    /**
     * 当前流程节点名称
     */
    @TableField(value = "CURRENT_NODE_NAME")
    private String currentNodeName;

    /**
     * 工单开始时间
     */
    @TableField(value = "WORK_ORDER_START_TIME")
    private LocalDateTime workOrderStartTime;

    /**
     * 工单结束时间-完结时间
     */
    @TableField(value = "WORK_ORDER_END_TIME")
    private LocalDateTime workOrderEndTime;

    /**
     * 工单当前节点开始时间
     */
    @TableField(value = "CURRENT_NODE_START_TIME")
    private LocalDateTime currentNodeStartTime;

    @TableField("RESOURCE_APPLY_FILE")
    private String resourceApplyFile;

    @TableField("ECS_RESOURCE_LIST")
    private String cloudEcsResourceList;

    @TableField("REDIS_RESOURCE_LIST")
    private String redisModelList;

    @TableField("MYSQL_RESOURCE_LIST")
    private String mysqlModelList;

    @TableField("POSTGRESQL_RESOURCE_LIST")
    private String postgreSqlModelList;

    @TableField("GCS_RESOURCE_LIST")
    private String cpuEcsResourceList;

    @TableField("EVS_RESOURCE_LIST")
    private String evsModelList;

    @TableField("EIP_RESOURCE_LIST")
    private String eipModelList;

    @TableField("SLB_RESOURCE_LIST")
    private String slbModelList;

    @TableField("NAT_RESOURCE_LIST")
    private String natGatwayModelList;

    @TableField("OBS_RESOURCE_LIST")
    private String obsModelList;

    // TODO 数据库目前没有加载这个字段
    @TableField("AUDIT_LOG_LIST")
    private String auditLogList;

    // 云类型
    @TableField("DOMAIN_NAME")
    private String domainName;

    // 云平台
    @TableField("CATALOGUE_DOMAIN_NAME")
    private String catalogueDomainName;

    /**
     * 局方负责人
     */
    @TableField("BUREAU_USER_NAME")
    private String bureauUserName;

    @TableField("DEPARTMENT_NAME")
    private String departmentName;

    @TableField("MODULE_NAME")
    private String moduleName;

    /**
     * 二级业务部门领导名称
     */
    @TableField("BUSINESS_DEPART_LEADER_NAME")
    private String businessDepartLeaderName;

    /**
     * 三级业务部门领导名称
     */
    @TableField("LEVEL_THREE_LEADER_NAME")
    private String levelThreeLeaderName;

    //是否线下开通
    @TableField("IS_OFFLINE")
    private Boolean isOffline;

}

