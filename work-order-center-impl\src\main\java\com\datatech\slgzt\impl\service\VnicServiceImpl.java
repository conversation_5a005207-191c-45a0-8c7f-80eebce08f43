package com.datatech.slgzt.impl.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.model.dto.NetcardCreateOpm;
import com.datatech.slgzt.model.dto.NetcardDeleteOpm;
import com.datatech.slgzt.model.dto.NetcardOperateOpm;
import com.datatech.slgzt.model.dto.VnicDTO;
import com.datatech.slgzt.model.vo.callback.TaskVO;
import com.datatech.slgzt.service.VnicService;
import com.datatech.slgzt.utils.HttpClientUtil;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class VnicServiceImpl implements VnicService {

    @Value("${http.resourceCenterUrl}")
    private String resourceCenterApiUrl;

    @Override
    public String createNetcard(NetcardCreateOpm opm) {
        String resourceId = null;
        String url = resourceCenterApiUrl + "/v1/cloud/resourcecenter/netcard/create";
        Map<String, Object> params = new HashMap<>();
        params.put("tenantId", opm.getTenantId());
        params.put("regionCode", opm.getRegionCode());
        params.put("orderId", opm.getOrderId());
        params.put("name", opm.getName());
        if (!StringUtils.isEmpty(opm.getDescription())) {
            params.put("description", opm.getDescription());
        }
        params.put("subnetId", opm.getSubnetId());
        params.put("azCode", opm.getAzCode());
        params.put("type", opm.getType());
        params.put("ipv6SubnetId", opm.getIpv6SubnetId());
        if (!StringUtils.isEmpty(opm.getNetName())) {
            params.put("netName", opm.getNetName());
        }
        log.info("发送创建虚拟网卡请求: {}", JSON.toJSONString(params));
        Mapper response = OkHttps.sync(url)
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(params))
                .post()
                .getBody()
                .toMapper();
        log.info("创建虚拟网卡响应: {}", response.toString());
        String successStr = response.getString("success");
        Precondition.checkArgument("1".equals(successStr) || "true".equalsIgnoreCase(successStr), "创建虚拟网卡失败: " + response.getString("message"));
        // 处理结果数据
        String entity = response.getString("entity");
        if (!StringUtils.isEmpty(entity)) {
            TaskVO taskVO = JSON.parseObject(entity, TaskVO.class);
            resourceId = taskVO.getResourceId();
        }
        return resourceId;
    }

    @Override
    public String deleteNetcard(NetcardDeleteOpm opm) {
        String url = resourceCenterApiUrl + "/v1/cloud/resourcecenter/netcard/delete";
        Map<String, Object> params = new HashMap<>();
        params.put("tenantId", opm.getTenantId());
        params.put("regionCode", opm.getRegionCode());
        params.put("instanceId", opm.getInstanceId());
        params.put("orderId", opm.getOrderId());
        params.put("azCode", opm.getAzCode());
        log.info("发送删除虚拟网卡请求: {}", JSON.toJSONString(params));
        Mapper response = OkHttps.sync(url)
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(params))
                .post()
                .getBody()
                .toMapper();
        log.info("删除虚拟网卡响应: {}", response.toString());
        String successStr = response.getString("success");
        Precondition.checkArgument("1".equals(successStr) || "true".equalsIgnoreCase(successStr), "删除虚拟网卡失败: " + response.getString("message"));
        Mapper mapper = response.getMapper("entity");
        return mapper.getString("id");
    }

    @Override
    public Boolean operateNetcard(NetcardOperateOpm opm) {
        Boolean flag = false;
        String url = "";
        if ("BIND".equals(opm.getOperateType())) {
            url = resourceCenterApiUrl + "/v1/cloud/resourcecenter/netcard/bind";
        } else if ("UNBIND".equals(opm.getOperateType())) {
            url = resourceCenterApiUrl + "/v1/cloud/resourcecenter/netcard/unbind";
        }
        Map<String, Object> params = new HashMap<>();
        params.put("tenantId", opm.getTenantId());
        params.put("regionCode", opm.getRegionCode());
        params.put("instanceId", opm.getInstanceId());
        params.put("orderId", opm.getOrderId());
        params.put("azCode", opm.getAzCode());
        params.put("serverId", opm.getServerId());
        log.info("发送虚拟网卡操作请求: {}", JSON.toJSONString(params));
        Map<String, Object> response = HttpClientUtil.post(url, JSON.toJSONString(params), getHeaders());
        Integer code = (Integer) response.get("code");
        String json = (String) response.get("json");
        log.info("操作虚拟网卡响应: {}", response);
        JSONObject jsonObject = JSON.parseObject(json);
        Precondition.checkArgument(200 == code, "操作虚拟网卡失败: " + jsonObject.getString("message"));
        // 处理结果数据
        String entity = jsonObject.getString("entity");
        if (!StringUtils.isEmpty(entity)) {
            TaskVO taskVO = JSON.parseObject(entity, TaskVO.class);
            flag = taskVO.getStatus().equals("SUCCESS");
        }
        return flag;
    }

    private Map<String, String> getHeaders() {
        Map<String, String> header = new HashMap<>();
        header.put("RemoteUser", "BusinessCenter");
        header.put("Content-Type", "application/json");
        return header;
    }
} 