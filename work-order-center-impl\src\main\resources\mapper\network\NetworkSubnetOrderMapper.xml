<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datatech.slgzt.dao.mapper.network.NetworkSubnetOrderMapper">

    <resultMap type="com.datatech.slgzt.dao.model.network.NetworkSubnetOrder" id="BaseResultMap">
        <result property="id" column="ID"/>
        <result property="createdTime" column="CREATED_TIME"/>
        <result property="updatedTime" column="UPDATED_TIME"/>
        <result property="subnetName" column="SUBNET_NAME"/>
        <result property="networkId" column="NETWORK_ID"/>
        <result property="cidr" column="CIDR"/>
        <result property="status" column="STATUS"/>
        <result property="instanceId" column="INSTANCE_ID"/>
        <result property="ipVersion" column="IP_VERSION"/>
        <result property="resourceId" column="RESOURCE_ID"/>
        <result property="netmask" column="NETMASK"/>
        <result property="deleted" column="DELETED"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="message" column="MESSAGE"/>
        <result property="gateway" column="GATEWAY"/>
        <result property="recoveryStatus" column="RECOVERY_STATUS"/>
        <result property="level2InstanceId" column="LEVEL2_INSTANCE_ID"/>
        <result property="uuid" column="UUID"/>
    </resultMap>

    <select id="selectCreateSubnetRcDtByNetworkId" resultType="com.datatech.slgzt.model.dto.network.CreateNetworkSubnetRcDTO">
        select n.REGION_CODE as regionCode,
               N.ACCOUNT as billId,
               s.SUBNET_NAME as name,
               s.CIDR , s.IP_VERSION as ipVersionStr,
               s.NETWORK_ID as netGlobalId,
               n.SYSTEM_SOURCE as systemSource,
               s.id as orderId,
               s.GATEWAY as gatewayIp,
               s.id as globalId
        from WOC_NETWORK_SUBNET_ORDER s
                 left join WOC_NETWORK_ORDER n on s.NETWORK_ID = n.ID
        where n.ID = #{networkId} and s.STATUS = 'PENDING'
    </select>

    <update id="updateStatusById" parameterType="com.datatech.slgzt.model.dto.network.StatusDTO">
        UPDATE WOC_NETWORK_SUBNET_ORDER
        SET STATUS = #{status}
        <if test="message != null and message != ''">
            , MESSAGE = #{message}
        </if>
        <if test="resourceId != null and resourceId != ''">
            , RESOURCE_ID = #{resourceId}
        </if>
        WHERE ID = #{id} AND STATUS = #{currentStatus}
    </update>

    <update id="updateStatusByNetworkId" parameterType="com.datatech.slgzt.model.dto.network.StatusDTO">
        UPDATE WOC_NETWORK_SUBNET_ORDER
        SET STATUS = #{status}
        <if test="message != null and message != ''">
            , MESSAGE = #{message}
        </if>
        <if test="resourceId != null and resourceId != ''">
            , RESOURCE_ID = #{resourceId}
        </if>
        WHERE NETWORK_ID = #{id} AND STATUS = #{currentStatus}
    </update>

    <insert id="batchInsertNetworkSubnet">
        INSERT ALL
        <foreach collection="list" item="item">
            INTO WOC_NETWORK_SUBNET_ORDER (ID, CREATED_TIME, SUBNET_NAME,NETWORK_ID,DELETED,
            CIDR,STATUS,INSTANCE_ID,IP_VERSION,MESSAGE,NETMASK,
            DESCRIPTION,GATEWAY,LEVEL2_INSTANCE_ID,UUID,ALLOCATION_POOLS)
            VALUES (#{item.id},#{item.createdTime},#{item.subnetName},#{item.networkId},#{item.deleted},
            #{item.cidr},#{item.status},#{item.instanceId},#{item.ipVersion},#{item.message},#{item.netmask},
            #{item.description},#{item.gateway},#{item.level2InstanceId},#{item.uuid},#{item.allocationPools})
        </foreach>
        SELECT 1 FROM DUAL
    </insert>

    <select id="selectByNetworkId" resultType="com.datatech.slgzt.dao.model.network.NetworkSubnetOrder">
        select s.*
        from WOC_NETWORK_SUBNET_ORDER s
        where NETWORK_ID = #{networkId}
    </select>

    <select id="selectByNetworkIdSuccess" resultType="com.datatech.slgzt.dao.model.network.NetworkSubnetOrder">
        select s.*
        from WOC_NETWORK_SUBNET_ORDER s
        where NETWORK_ID = #{networkId}
        and STATUS = 'SUCCESS' AND DELETED = 1
    </select>

    <select id="selectSubnetByNetworkId" resultType="com.datatech.slgzt.dao.model.network.SubnetCommonDo">
        select s.id, s.subnet_name, s.network_id, s.instance_id, s.cidr, s.level2_instance_id,v.REGION_CODE,v.TENANT_ID
        from WOC_NETWORK_SUBNET_ORDER s
        left join WOC_NETWORK_ORDER v on v.ID = s.NETWORK_ID
        where s.NETWORK_ID in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectByIds" resultType="com.datatech.slgzt.dao.model.network.NetworkSubnetOrder">
        SELECT *
        FROM WOC_NETWORK_SUBNET_ORDER
        WHERE ID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateRecoveryStatusById">
        update
        WOC_NETWORK_SUBNET_ORDER
        set
        UPDATED_TIME = SYSDATE,
        RECOVERY_STATUS = #{recoveryStatus},
        DELETED = #{deleted}
        <if test="message != null and message != ''">
            , MESSAGE = #{message}
        </if>
        WHERE
        ID = #{id}
    </update>

    <update id="updateRecoveryStatus">
        update
        WOC_NETWORK_SUBNET_ORDER
        set
        RECOVERY_STATUS = #{recoveryStatus},
        DELETED = #{deleted}
        WHERE
        ID = #{id}
    </update>
    <select id="selectCommonById" resultType="com.datatech.slgzt.dao.model.network.SubnetCommonDo">
        select id, subnet_name, network_id, instance_id, cidr, level2_instance_id
        from WOC_NETWORK_SUBNET_ORDER
        WHERE ID = #{id}
    </select>

    <select id="selectCountByNetworkIdSuccess" resultType="integer">
        select count(1) from WOC_NETWORK_SUBNET_ORDER where NETWORK_ID = #{networkId} and STATUS = 'SUCCESS' AND DELETED = 1
    </select>


</mapper>