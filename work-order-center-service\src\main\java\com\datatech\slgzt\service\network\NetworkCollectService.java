package com.datatech.slgzt.service.network;



import com.datatech.slgzt.enums.ip.IpLevelEnum;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.network.IpListRc;
import com.datatech.slgzt.model.dto.network.NetworkConfirmDTO;
import com.datatech.slgzt.model.vo.network.ip.*;
import com.datatech.slgzt.page.PageResp;

import java.util.List;

public interface NetworkCollectService {

//    List<I3_IPVO> querySegmentI3(String cloud, String relatedPool, IpTypeEnum type, String vpn, IPVersionEnum ipVersion, String prefix, Integer mask, String instanceId);
//
//    List<I2_IPVO> querySegmentI2(String cloud, String relatedPool, IpTypeEnum type, String vpn, IPVersionEnum ipVersion, String instanceId);


    CommonResult divideSubnet(DivideSubnetRc divideSubnetRc);

    List<IpCheckRep> ipCheck(IpCheckRc ipCheckRc);

    PageResp<IPVO> ipList(IpListRc ipListRc);

    PageResp<I3_IPPage> ip3List(IpListRc ipListRc);

    CommonResult ipUpdateStatus(IpUpdateStatusVO ipUpdateStatusRc);

    PageResp<VlanResp> vlanList(String regionCode, String type, String vlan,Integer pageNum, Integer pageSize);

    CommonResult vlanUpdateStatus(VlanUpdateStatusRc vlanUpdateStatusRc);

    CommonResult networkCancel(String instanceId);
}
