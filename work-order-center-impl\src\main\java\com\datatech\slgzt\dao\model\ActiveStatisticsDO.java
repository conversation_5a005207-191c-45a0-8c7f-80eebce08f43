package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 活跃统计DO
 */
@Data
@TableName("WOC_ACTIVE_STATISTICS")
public class ActiveStatisticsDO{

    @TableId(value = "id", type = IdType.ID_WORKER)
    @TableField("ID")
    private String id;
    /**
     * 统计日期
     */
    @TableField("STAT_DATE")
    private LocalDate statDate;

    /**
     * 统计时间（格式：yyyyMMddHH）
     */
    @TableField("STAT_TIME")
    private String statTime;

    /**
     * 活跃用户数
     */
    @TableField("ACTIVE_USER_COUNT")
    private Long activeUserCount;

    /**
     * 用户登录数
     */
    @TableField("USER_LOGIN_COUNT")
    private Long userLoginCount;

    /**
     * 点击量
     */
    @TableField("CLICK_COUNT")
    private Long clickCount;

    /**
     * API访问量
     */
    @TableField("API_ACCESS_COUNT")
    private Long apiAccessCount;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 活跃用户数（字符串格式）bitset
     */
    @TableField("ACTIVE_USER_COUNT_V")
    private String activeUserCountV;
}