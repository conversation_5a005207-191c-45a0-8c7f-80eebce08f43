package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.CmdbIP3StatusDTO;
import com.datatech.slgzt.model.query.NetworkCmdbQuery;

import java.util.List;


public interface CmdbIP3StatusManager {
    void insert(String instanceId);

    /**
     * 更新子网开通状态
     *
     * @param instanceId 实例id
     * @param subnetOpenStatus 子网开通状态，-2取消，-1:开通失败，0:无状态，1:开通成功
     * @param oldSubnetOpenStatus 子网开通状态，null表示不考虑
     */
    void updateSubnetOpenStatus(String instanceId, Integer subnetOpenStatus, Integer oldSubnetOpenStatus);

    CmdbIP3StatusDTO selectByInstanceId(String instanceId);

    void deleteByInstanceId(String instanceId);

    void cancelByInstanceId(String instanceId);

    List<CmdbIP3StatusDTO> selectList(NetworkCmdbQuery query);
}