package com.datatech.slgzt.model.dto.network;

import com.datatech.slgzt.model.AllocationPool;
import lombok.Data;

import java.util.List;

@Data
public class CreateNetworkSubnetRcDTO {
    private String regionCode;
    private String billId;
    private String name;
    private String cidr;
    private int ipVersion;
    private String ipVersionStr;
    private String netGlobalId;
    private String systemSource;
    private String globalId;
    private String orderId;
    private String gatewayIp;
    private List<AllocationPool> allocationPools;
}
