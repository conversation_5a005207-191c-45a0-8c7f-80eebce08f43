<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datatech.slgzt.dao.mapper.VmMapper">

    <select id="getByIdList" resultType="com.datatech.slgzt.model.dto.VirtualMachineDTO">
        SELECT vm.id as id,vm.NAME as name,vm.STATUS as status,vm.TYPE as type,vm.INSTANCE_UUID as instanceUuid,vm.RESOURCE_ID as instanceId,
        vm.RESOURCE_ID as resourceId, rc.CMDB_NAME as cmdbName,r.CODE as regionCode,rc.CLOUD,i.OS_TYPE as osType,i.name as imagesName,r.NAME as regionName,
        pla.PLATFORM_TYPE,fl.RAM as ram,fl.VCPUS as vcpus
        FROM SLGZT.MC_VM_T vm
        LEFT JOIN SLGZT.MC_REGION_T r on r.ID = vm.REGION_ID
        LEFT JOIN SLGZT.MC_IMAGES_T i ON i.id = vm.IMAGE_ID
        LEFT JOIN SLGZT.MC_PLATFORM_T pla ON r.cloud_platform_id = pla.id and pla.deleted = 1
        LEFT JOIN SLGZT.MC_FLAVOR_T fl ON vm.FLAVOR_ID = fl.id and fl.deleted = 1
        LEFT JOIN SLGZT.MC_REGION_CMDB rc ON r.CODE = rc.CODE
        WHERE  vm.DELETED = 1
        and vm.ID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getById" resultType="com.datatech.slgzt.model.dto.VirtualMachineDTO">
        SELECT vm.id as id,vm.NAME as name,vm.STATUS as status,vm.TYPE as type,vm.INSTANCE_UUID as instanceUuid,vm.RESOURCE_ID as instanceId,
        vm.RESOURCE_ID as resourceId, rc.CMDB_NAME as cmdbName,r.CODE as regionCode,rc.CLOUD,i.OS_TYPE as osType,i.name as imagesName,r.NAME as regionName,
        pla.PLATFORM_TYPE,fl.RAM as ram,fl.VCPUS as vcpus
        FROM SLGZT.MC_VM_T vm
        LEFT JOIN SLGZT.MC_REGION_T r on r.ID = vm.REGION_ID
        LEFT JOIN SLGZT.MC_IMAGES_T i ON i.id = vm.IMAGE_ID
        LEFT JOIN SLGZT.MC_PLATFORM_T pla ON r.cloud_platform_id = pla.id and pla.deleted = 1
        LEFT JOIN SLGZT.MC_FLAVOR_T fl ON vm.FLAVOR_ID = fl.id and fl.deleted = 1
        LEFT JOIN SLGZT.MC_REGION_CMDB rc ON r.CODE = rc.CODE
        WHERE  vm.DELETED = 1
        and vm.ID = #{id}
    </select>

    <select id="getIpAddress"  resultType="com.datatech.slgzt.model.dto.IpAddress">
        SELECT i.id,i.IP,i.NAME,i.DEVICE_ID as deviceId,i.TYPE, i.DEVICE_TYPE as deviceType,n.GLOBAL_ID as networkId,s.GLOBAL_ID as subnetId
        FROM SLGZT.MC_IP_ADDRESS_T i
        left join SLGZT.MC_NETWORKS_T n on n.ID = i.NETWORK_ID
        left join SLGZT.MC_SUBNETS_T s on s.ID = i.SUBNET_ID
        where
        i.DELETED = 1
        <if test="deviceId != null and deviceId != ''">
            AND i.DEVICE_ID = #{deviceId}
        </if>
        <if test="deviceType != null and deviceType != ''">
            AND i.DEVICE_TYPE = #{deviceType}
        </if>
    </select>

    <select id="getVolume"  resultType="com.datatech.slgzt.model.dto.Volume">
        SELECT v.ID,v.NAME,v.VOLUME_SIZE as volumeSize
        FROM SLGZT.MC_VOLUME_T v
        left join  SLGZT.MC_VOLUME_ATTACH_VM_T va on va.VOLUME_ID = v.id
        where v.DELETED = 1 and va.DELETED = 1 and va.VM_ID = #{vmId}
    </select>

    <select id="getCmdbTenatId"  resultType="java.lang.String">
        select CMDB_ID from SLGZT.CMP_TENANT
        where ID = #{id}
    </select>

    <select id="getCmdbSystemId"  resultType="java.lang.String">
        select m.CMDB_ID from CMP_APP_MODULE_CMDB_T m
        left join CMP_APP_T a on m.APP_ID = a.ID
        where a.TENANT_ID = #{tenantId} and m.REGION_CODE = #{regionCode}
    </select>

    <select id="getCmdbSystemIdByModuleId"  resultType="java.lang.String">
        select m.CMDB_ID from CMP_APP_MODULE_CMDB_T m
        left join CMP_APP_T a on m.APP_ID = a.ID
        where m.APP_MODULE_ID = #{moduleId} and m.REGION_CODE = #{regionCode}
    </select>

    <select id="getPoolById"  resultType="com.datatech.slgzt.model.dto.VirtualMachineDTO">
        select c.CLOUD as cloud,c.BUCKET_POOL as bucketPool,c.CMDB_NAME as cmdbName
        from SLGZT.MC_REGION_CMDB c
        left join MC_REGION_T r on c.CODE = r.CODE
        where r.ID = #{id}
    </select>

    <select id="getVpcByOrderId"  resultType="com.datatech.slgzt.model.vo.callback.TaskVO">
        select * from SLGZT.MC_TASK_T
        where RESOURCE_TYPE = 'VPC'
            AND ORDER_ID = #{orderId}

    </select>

    <select id="getRegionCmdb"  resultType="com.datatech.slgzt.model.dto.RegionCmdbDTO">
        select CODE,NAME,CMDB_NAME as cmdbName,CLOUD,BUCKET_POOL as bucketPool from SLGZT.MC_REGION_CMDB
        where CODE = #{regionCode}
    </select>

    <select id="getByDeviceId"  resultType="String">
        select PROJECT_ID from SLGZT.MC_VM_T
        where ID = #{id}
    </select>

    <select id="getOsVersionById" resultType="string">
        select name from MC_IMAGES_T where id = #{id}
    </select>

    <select id="getCustomTenants" resultType="com.datatech.slgzt.model.dto.CustomTenantDTO">
        select
            c.ID as CUSTOM_ID, c.CUSTOM_NAME, t.ID AS TENANT_ID, t.NAME as TENANT_NAME, c.CONTACT_NAME,c.CONTACT_MOBILE
        from
            CMP_TENANT t
                inner join CCMP_CUSTOM c on t.CUSTOM_ID = c.ID
        where
            t.TENANT_TYPE = 1
          and t.STATUS = 1
          and c.STATUS = 1
          <if test="id != null">
              and c.CREATED_BY = #{id}
          </if>
    </select>

    <select id="getCustomById" resultType="com.datatech.slgzt.model.dto.CustomDTO">
        select c.CUSTOM_NO, c.CUSTOM_NAME, c.CONTACT_NAME,c.CONTACT_MOBILE,c.SAFE_EMAIL as EMAIL,c.CREATED_TIME,
            (SELECT LISTAGG (BILL_ID, ',') from CMP_TENANT t where t.TENANT_TYPE = 1 and t.STATUS = 1 AND t.CUSTOM_ID = c.ID) as BILL_ID,
            (SELECT LISTAGG (ID, ',') from CMP_TENANT t where t.TENANT_TYPE = 1 and t.STATUS = 1 AND t.CUSTOM_ID = c.ID) as TENANT_ID
        from
            CCMP_CUSTOM c
        where c.ID = #{customId} and c.STATUS = 1
    </select>

    <select id="getCustomList" resultType="com.datatech.slgzt.model.dto.CustomDTO">
        SELECT * FROM (
        SELECT
        c.ID as CUSTOM_ID,
        c.CUSTOM_NO,
        c.CUSTOM_NAME,
        c.CONTACT_NAME,
        c.CONTACT_MOBILE,
        c.SAFE_EMAIL as EMAIL,
        c.CREATED_TIME,
        (SELECT LISTAGG(BILL_ID, ',') FROM CMP_TENANT t
        WHERE t.TENANT_TYPE = 1 AND t.STATUS = 1 AND t.CUSTOM_ID = c.ID) AS BILL_ID
        FROM
        CCMP_CUSTOM c
        WHERE
        c.STATUS = 1
        <if test="query.customIds != null and query.customIds.size() > 0">
            AND c.ID IN
            <foreach item='item' collection='query.customIds' open='(' separator=',' close=')'>
                #{item}
            </foreach>
        </if>
        ) temp
        WHERE 1=1
        <if test="query.customNo != null and query.customNo != ''">
            AND temp.CUSTOM_NO LIKE CONCAT('%', #{query.customNo}, '%')
        </if>
        <if test="query.billId != null and query.billId != ''">
            AND EXISTS (
            SELECT 1 FROM CMP_TENANT t
            WHERE t.CUSTOM_ID = temp.CUSTOM_ID
            AND t.TENANT_TYPE = 1
            AND t.STATUS = 1
            AND t.BILL_ID LIKE CONCAT('%', #{query.billId}, '%')
            )
        </if>
        <if test="query.email != null and query.email != ''">
            AND temp.EMAIL LIKE CONCAT('%', #{query.email}, '%')
        </if>
        <if test="query.contactMobile != null and query.contactMobile != ''">
            AND temp.CONTACT_MOBILE LIKE CONCAT('%', #{query.contactMobile}, '%')
        </if>
        <if test="query.customName != null and query.customName != ''">
            AND temp.CUSTOM_NAME LIKE CONCAT('%', #{query.customName}, '%')
        </if>
        <if test="query.contactName != null and query.contactName != ''">
            AND temp.CONTACT_NAME LIKE CONCAT('%', #{query.contactName}, '%')
        </if>
        <if test="query.createTimeStart != null">
            AND temp.CREATED_TIME &gt;= #{query.createTimeStart}
        </if>
        <if test="query.createTimeEnd != null">
            AND temp.CREATED_TIME &lt;= #{query.createTimeEnd}
        </if>
        ORDER BY temp.CREATED_TIME DESC
    </select>

    <select id="getIpAddresses"  resultType="com.datatech.slgzt.model.dto.IpAddress">
        SELECT i.ID,i.IP,i.NAME,i.DEVICE_ID,i.TYPE, i.DEVICE_TYPE, s.GLOBAL_ID AS SUBNET_ID, s.NAME AS SUBNET_NAME
        FROM SLGZT.MC_IP_ADDRESS_T i LEFT JOIN MC_SUBNETS_T s on i.SUBNET_ID = s.ID
        where
        i.DELETED = 1
        <if test="deviceId != null and deviceId != ''">
            AND i.DEVICE_ID = #{deviceId}
        </if>
        <if test="deviceType != null and deviceType != ''">
            AND i.DEVICE_TYPE = #{deviceType}
        </if>
    </select>

</mapper>
