package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 虚拟网卡DO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("WOC_VNIC")
public class VnicDO extends BaseDO {

    /**
     * 虚拟网卡ID
     */
    @TableField("VNIC_ID")
    private String vnicId;

    /**
     * 虚拟网卡名称
     */
    @TableField("VNIC_NAME")
    private String vnicName;

    /**
     * 业务系统名称
     */
    @TableField("BUSINESS_SYSTEM_NAME")
    private String businessSystemName;

    /**
     * 业务系统Id
     */
    @TableField("BUSINESS_SYSTEM_ID")
    private String businessSystemId;

    /**
     * 云类型名称
     */
    @TableField("CATALOGUE_DOMAIN_NAME")
    private String catalogueDomainName;

    /**
     * 云类型编码
     */
    @TableField("CATALOGUE_DOMAIN_CODE")
    private String catalogueDomainCode;

    /**
     * 云平台名称
     */
    @TableField("DOMAIN_NAME")
    private String domainName;

    /**
     * 云平台编码
     */
    @TableField("DOMAIN_CODE")
    private String domainCode;

    /**
     * 资源池名称
     */
    @TableField("REGION_NAME")
    private String regionName;

    /**
     * 资源池code
     */
    @TableField("REGION_CODE")
    private String regionCode;

    /**
     * 资源池ID
     */
    @TableField("REGION_ID")
    private String regionId;

    /**
     * 可用区名称
     */
    @TableField("AZ_NAME")
    private String azName;

    /**
     * 可用区code
     */
    @TableField("AZ_CODE")
    private String azCode;

    /**
     * 可用区ID
     */
    @TableField("AZ_ID")
    private String azId;

    /**
     * VPC名称
     */
    @TableField("VPC_NAME")
    private String vpcName;

    /**
     * VPC ID
     */
    @TableField("VPC_ID")
    private String vpcId;

    /**
     * 子网名称
     */
    @TableField("SUBNET_NAME")
    private String subnetName;

    /**
     * 子网ID
     */
    @TableField("SUBNET_ID")
    private String subnetId;

    /**
     * IP地址
     */
    @TableField("IP_ADDRESS")
    private String ipAddress;

    /**
     * ipv6子网ID
     */
    @TableField("IPV6_SUBNET_ID")
    private String ipv6SubnetId;

    /**
     * ipv6子网名称
     */
    @TableField("IPV6_SUBNET_NAME")
    private String ipv6SubnetName;

    /**
     * ipv6IP地址
     */
    @TableField("IPV6_IP_ADDRESS")
    private String ipV6IpAddress;

    /**
     * 云主机名称
     */
    @TableField("VM_NAME")
    private String vmName;

    /**
     * 云主机ID
     */
    @TableField("VM_ID")
    private String vmId;

    /**
     * 描述
     */
    @TableField("DESCRIPTION")
    private String description;

    /**
     * 租户id
     */
    @TableField("TENANT_ID")
    private Long tenantId;

    /**
     * 来源
     */
    @TableField("SOURCE_TYPE")
    private String sourceType;

    @TableField(exist = false)
    private String tenantName;
} 