package com.datatech.slgzt.model.dto.network;

import com.datatech.slgzt.model.AllocationPool;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SubnetDTO implements Serializable {

    private String subnetName;
    /**
     * 网络网段
     */
    private String cidr;

    /**
     * 网络描述
     */
    private String description;

    private String ipVersion;

    private String instanceId;
    private String level2InstanceId;

    private String uuid;

    private List<AllocationPool> allocationPools;
}
