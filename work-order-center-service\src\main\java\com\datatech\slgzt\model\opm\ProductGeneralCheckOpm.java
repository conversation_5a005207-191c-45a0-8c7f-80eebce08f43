package com.datatech.slgzt.model.opm;

import com.datatech.slgzt.model.ProductCapacityCheckModel;
import com.datatech.slgzt.model.nostander.*;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月06日 09:47:10
 */
@Data
public class ProductGeneralCheckOpm {


    /**
     * 架构师审核必传 云平台code
     */
    private String domainCode;


    /**
     * Ecs申请资源列表的json
     *
     * @see CloudEcsResourceModel //ecs资源模型
     */
    private List<CloudEcsResourceModel> ecsModelList;

    /**
     * Cpu申请资源列表的json,gcs
     */
    private List<CpuEcsResourceModel> gcsModelList;

    private List<EcsModel> mysqlModelList;

    private List<EcsModel> postgreSqlModelList;

    private List<MysqlV2Model> mysqlV2ModelList;

    private List<EcsModel> redisModelList;

    /**
     * evs申请资源列表的json
     */
    private List<EvsModel> evsModelList;

    /**
     * eip申请资源列表的json
     */
    private List<EipModel> eipModelList;

    /**
     * nat资源申请json
     */
    private List<NatGatwayModel> natModelList;

    /**
     * slb资源申请json
     */
    private List<SlbModel> slbModelList;

    /**
     * obs资源申请json
     */
    private List<ObsModel> obsModelList;

    /**
     * 容器资源配额申请json
     */
    private List<CQModel> cqModelList;

    /**
     * 备份策略申请json
     */
    private List<BackupModel> backupModelList;

    /**
     * vpn资源申请json
     */
    private List<VpnModel> vpnModelList;

    /**
     * nas资源申请json
     */
    private List<NasModel> nasModelList;

    private List<KafkaModel> kafkaModelList;

    private List<EsModel> esModelList;

    private List<FlinkModel> flinkModelList;

    private List<BldRedisModel> bldRedisModelList;

    private List<String> errorMessages =new ArrayList<>();

    private Map<String, ProductCapacityCheckModel> azCheckModelMap = new HashMap<>();

    private Map<String, ProductCapacityCheckModel> regionCheckModelMap = new HashMap<>();

    /**
     * 是否检查slb存量校验
     * 默认为false，不检查slb存量校验
     */
    private Boolean checkSlbNum = false;

    /**
     * 是否检查开通数量的校验
     * 默认为false，不检查开通数量的校验
     */
    private Boolean checkOpenNum = false;


}
