package com.datatech.slgzt.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.convert.VirtualIpManagerConvert;
import com.datatech.slgzt.dao.VirtualIpDAO;
import com.datatech.slgzt.dao.mapper.network.NetworkSubnetOrderMapper;
import com.datatech.slgzt.dao.model.VirtualIpDO;
import com.datatech.slgzt.dao.model.network.NetworkSubnetOrder;
import com.datatech.slgzt.enums.DomainCodeEnum;
import com.datatech.slgzt.enums.ip.IPVersionEnum;
import com.datatech.slgzt.manager.VirtualIpManager;
import com.datatech.slgzt.model.dto.VirtualIpDTO;
import com.datatech.slgzt.model.AllocationPool;
import com.datatech.slgzt.model.dto.network.AvailableIpDTO;
import com.datatech.slgzt.model.query.VirtualIpQuery;
import com.datatech.slgzt.model.vo.callback.TaskVO;
import com.datatech.slgzt.utils.*;
import com.datatech.slgzt.warpper.PageWarppers;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class VirtualIpManagerImpl implements VirtualIpManager {
    @Resource
    private VirtualIpDAO virtualIpDAO;
    @Resource
    private VirtualIpManagerConvert convert;
    @Resource
    private NetworkSubnetOrderMapper networkSubnetOrderMapper;

    @Value("${http.resourceCenterUrl}")
    private String resourceCenterApiUrl;

    @Override
    @Transactional
    public void add(VirtualIpDTO dto) {
        VirtualIpDO virtualIpDO = convert.dto2do(dto);
        virtualIpDAO.insert(virtualIpDO);
        //调用资源中心创建虚拟ip（目前支持vmware）
        if (!DomainCodeEnum.PLF_PROV_MOC_ZJ_VMWARE.getCode().equals(dto.getDomainCode())) {
            return;
        }
        Map<String, Object> param = new HashMap<>();
        param.put("subnetId", dto.getSubnetId());
        param.put("vipName", dto.getVipName());
        param.put("vip", dto.getIpAddress());
        param.put("gId", UuidUtil.getGid("vip"));
        log.info("发送创建虚拟ip请求: {}", JSON.toJSONString(param));
        Mapper response = OkHttps.sync(resourceCenterApiUrl + "/v1/cloud/resourcecenter/vip/create")
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(param))
                .post()
                .getBody()
                .toMapper();
        log.info("创建虚拟ip响应: {}", response.toString());
        String successStr = response.getString("success");
        Precondition.checkArgument("1".equals(successStr) || "true".equalsIgnoreCase(successStr), "创建虚拟ip失败: " + response.getString("message"));
        // 处理结果数据
        String entity = response.getString("entity");
        if (!StringUtils.isEmpty(entity)) {
            TaskVO taskVO = JSON.parseObject(entity, TaskVO.class);
            virtualIpDO.setGid(taskVO.getResourceId());
            virtualIpDAO.update(virtualIpDO);
        }
    }

    @Override
    public void update(VirtualIpDTO dto) {
        VirtualIpDO entity = convert.dto2do(dto);
        virtualIpDAO.update(entity);
    }

    @Override
    @Transactional
    public void delete(String id) {
        VirtualIpDTO virtualIpDTO = this.getById(id);
        virtualIpDAO.delete(id);
        //调用资源中心删除虚拟ip（目前支持vmware）
        if (!DomainCodeEnum.PLF_PROV_MOC_ZJ_VMWARE.getCode().equals(virtualIpDTO.getDomainCode())) {
            return;
        }
        String gid = virtualIpDTO.getGid();
        Map<String, Object> param = new HashMap<>();
        param.put("vipId", gid);
        log.info("发送删除虚拟ip请求: {}", gid);
        Mapper response = OkHttps.sync(resourceCenterApiUrl + "/v1/cloud/resourcecenter/vip/delete")
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(param))
                .post()
                .getBody()
                .toMapper();
        log.info("删除虚拟ip响应: {}", response.toString());
        String successStr = response.getString("success");
        Precondition.checkArgument("1".equals(successStr) || "true".equalsIgnoreCase(successStr), "创建虚拟ip失败: " + response.getString("message"));
    }

    @Override
    public void clearEip(String id) {
        virtualIpDAO.clearEip(id);
    }

    @Override
    public VirtualIpDTO getById(String id) {
        VirtualIpDO entity = virtualIpDAO.getById(id);
        return convert.do2dto(entity);
    }

    @Override
    public PageResult<VirtualIpDTO> page(VirtualIpQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<VirtualIpDO> list = virtualIpDAO.list(query);
        return PageWarppers.box(new PageInfo<>(list), convert::do2dto);
    }


    /**
     * 获取已经使用的Ip列表
     *
     * @param subnetId
     * @param
     */
    @Override
    public List<String> getIpListBySubnetId(String subnetId) {
        List<String> ipList = new ArrayList<>();
        //查询自己数据库看看已经创建的虚拟IP
        List<VirtualIpDO> virtualIpDOS = virtualIpDAO.listBySubnetId(subnetId);
        if (ObjNullUtils.isNotNull(virtualIpDOS)) {
            for (VirtualIpDO virtualIpDO : virtualIpDOS) {
                ipList.add(virtualIpDO.getIpAddress());
            }
        }
        return ipList;
    }

    /**
     * 获取未使用的Ip列表
     */
    @Override
    public List<String> getUnusedIpList(String subnetId, String cidr, String keyword) {
        String subnetDeviceId = virtualIpDAO.getSubnetId(subnetId);
        if (ObjNullUtils.isNull(subnetDeviceId)) {
            return Collections.emptyList();
        }

        String gateway = GatewayCalculatorUtil.getGateway(cidr);
        // 获取当前已使用的IP列表
        List<String> usedIpList = virtualIpDAO.getIpListBySubnetId(subnetDeviceId);
        usedIpList.add(gateway);
        //查询自己数据库看看已经创建的虚拟IP
        List<VirtualIpDO> virtualIpDOS = virtualIpDAO.listBySubnetId(subnetId);
        if (ObjNullUtils.isNotNull(virtualIpDOS)) {
            for (VirtualIpDO virtualIpDO : virtualIpDOS) {
                usedIpList.add(virtualIpDO.getIpAddress());
            }
        }

        // 基于CIDR生成所有可能的IP
        List<String> allIpList = generateIpsFromCidr(cidr);

        // 过滤出未使用的IP
        List<String> unusedIpList = new ArrayList<>();
        for (String ip : allIpList) {
            if (!usedIpList.contains(ip)) {
                // 如果有关键字筛选条件，只添加包含关键字的IP地址
                if (ObjNullUtils.isNull(keyword) || ip.contains(keyword)) {
                    unusedIpList.add(ip);
                }
            }
        }

        return unusedIpList;
    }



//    /**
//     * 获取未使用的Ip列表（支持IPv4和IPv6）
//     */
//    @Override
//    public List<String> getUnusedIpList(String subnetId, String cidr, String keyword) {
//        String subnetDeviceId = virtualIpDAO.getSubnetId(subnetId);
//        if (ObjNullUtils.isNull(subnetDeviceId)) {
//            return Collections.emptyList();
//        }
//
//        try {
//            String gateway = GatewayCalculatorUtil.getGateway(cidr);
//            // 获取当前已使用的IP列表
//            List<String> usedIpList = virtualIpDAO.getIpListBySubnetId(subnetDeviceId);
//            if (ObjNullUtils.isNotNull(gateway)) {
//                usedIpList.add(gateway);
//            }
//
//            //查询自己数据库看看已经创建的虚拟IP
//            List<VirtualIpDO> virtualIpDOS = virtualIpDAO.listBySubnetId(subnetId);
//            if (ObjNullUtils.isNotNull(virtualIpDOS)) {
//                for (VirtualIpDO virtualIpDO : virtualIpDOS) {
//                    usedIpList.add(virtualIpDO.getIpAddress());
//                }
//            }
//
//            // 使用IpUtils生成可用IP（自动检测IPv4/IPv6）
//            // 对于获取未使用IP列表，我们生成较多的IP供选择
//            int generateCount = 1000; // 默认生成1000个可用IP
//
//            // 如果是IPv6，由于地址空间巨大，限制生成数量
//            if (IpUtils.isIPv6Cidr(cidr)) {
//                generateCount = 100; // IPv6限制为100个
//            }
//
//            List<String> availableIps = IpUtils.generateAvailableIps(cidr, usedIpList, null, generateCount);
//
//            // 过滤出包含关键字的IP
//            List<String> filteredIpList = new ArrayList<>();
//            for (String ip : availableIps) {
//                // 如果有关键字筛选条件，只添加包含关键字的IP地址
//                if (ObjNullUtils.isNull(keyword) || ip.contains(keyword)) {
//                    filteredIpList.add(ip);
//                }
//            }
//
//            return filteredIpList;
//        } catch (Exception e) {
//            log.error("获取未使用IP列表失败，subnetId: {}, cidr: {}, error: {}", subnetId, cidr, e.getMessage());
//            return Collections.emptyList();
//        }
//    }

    /**
     * 根据CIDR生成IP地址列表
     * 例如: ***********/24 会生成 *********** ~ *************
     */
    private List<String> generateIpsFromCidr(String cidr) {
        List<String> ipList = new ArrayList<>();
        try {
            String[] parts = cidr.split("/");
            if (parts.length != 2) {
                return ipList;
            }

            String ipPart = parts[0];
            int prefixLength = Integer.parseInt(parts[1]);

            String[] octets = ipPart.split("\\.");
            if (octets.length != 4) {
                return ipList;
            }

            // 计算网络ID和广播地址
            int[] ipBytes = new int[4];
            for (int i = 0; i < 4; i++) {
                ipBytes[i] = Integer.parseInt(octets[i]);
            }

            // 计算子网可用IP数量
            long ipCount = (long) Math.pow(2, 32 - prefixLength) - 2; // 减去网络地址和广播地址
            if (ipCount <= 0) {
                return ipList;
            }

            // 生成IP地址列表（跳过网络地址）
            for (int i = 1; i <= ipCount; i++) {
                int[] currentIp = calculateIp(ipBytes, i);
                ipList.add(currentIp[0] + "." + currentIp[1] + "." + currentIp[2] + "." + currentIp[3]);
            }
        } catch (Exception e) {
            // 处理异常
        }
        return ipList;
    }

    /**
     * 计算起始IP偏移后的IP地址
     */
    private int[] calculateIp(int[] baseIp, int offset) {
        int[] result = Arrays.copyOf(baseIp, 4);
        int carry = offset;

        for (int i = 3; i >= 0; i--) {
            int val = result[i] + carry;
            result[i] = val % 256;
            carry = val / 256;
            if (carry == 0) break;
        }

        return result;
    }

    /**
     * 获取可用IP列表（支持IPv4和IPv6）
     *
     * @param subnetId 子网ID
     * @param filterIps 需要过滤的IP列表
     * @param count 需要生成的IP数量
     * @return 可用IP信息
     */
    @Override
    public AvailableIpDTO getAvailableIp(String subnetId, List<String> filterIps, Integer count) {
        log.info("获取可用IP列表，subnetId: {}, filterIps: {}, count: {}", subnetId, filterIps, count);
        AvailableIpDTO result = new AvailableIpDTO();

        // 查询子网信息
        NetworkSubnetOrder subnetOrder = networkSubnetOrderMapper.selectById(subnetId);
        Precondition.checkArgument(subnetOrder, "子网不存在");

        // 获取CIDR和IP版本
        String cidr = subnetOrder.getCidr();
        String ipVersion = subnetOrder.getIpVersion();
        result.setIpVersion(ipVersion);

        // 获取网关
        String gateway = subnetOrder.getGateway();

        // 获取已使用的IP列表
        List<String> usedIpList = getUsedIpList(subnetId);

        // 添加网关到过滤列表
        List<String> allFilterIps = new ArrayList<>();
        if (ObjNullUtils.isNotNull(filterIps)) {
            if (IPVersionEnum.IPV6.getDesc().equals(ipVersion)) {
                for (String filterIp : filterIps) {
                    allFilterIps.add(IpUtils.compressIpv6Address(filterIp));
                }
            } else {
                allFilterIps.addAll(filterIps);
            }
        }
        if(IPVersionEnum.IPV6.getDesc().equals(ipVersion)){
            gateway = IpUtils.compressIpv6Address(gateway);
        }
        if (ObjNullUtils.isNotNull(gateway)) {
            allFilterIps.add(gateway);
        }
        List<AllocationPool> allocationPools = null;
        if(subnetOrder.getAllocationPools()!=null) {
            allocationPools = JSONObject.parseArray(subnetOrder.getAllocationPools(), AllocationPool.class);
        }
        // 使用IpUtils工具类生成可用IP（自动检测IPv4/IPv6）
        List<String> availableIps = IpUtils.generateAvailableIps(cidr, allocationPools, usedIpList, allFilterIps, count);
        result.setAvailableIps(availableIps);
        log.info("获取可用IP列表完成，IP版本: {}, 可用IP数量: {}", ipVersion, availableIps.size());

        return result;
    }

    /**
     * 校验IP是否可用
     *
     * @param subnetId 子网ID
     * @param ipAddress IP地址
     * @return 错误信息，如果为null表示可用
     */
    @Override
    public String checkIpAvailable(String subnetId, String ipAddress) {
        log.info("校验IP是否可用，subnetId: {}, ipAddress: {}", subnetId, ipAddress);
        return checkIpAvailable(subnetId, ipAddress, null);
    }

    /**
     * 校验IP是否可用
     * 批量校验
     *
     * @param subnetId 子网ID
     * @param ipAddresses IP地址列表
     * @return 错误信息，如果为null表示可用
     */
    @Override
    public String checkIpAvailableBatch(String subnetId, List<String> ipAddresses) {
        log.info("批量校验IP是否可用，subnetId: {}, ipAddresses: {}", subnetId, ipAddresses);
        List<String> usedIpList = getUsedIpList(subnetId);
        for (String ipAddress : ipAddresses) {
            String result = checkIpAvailable(subnetId, ipAddress, usedIpList);
            if (result != null) {
                return result;
            }
            usedIpList.add(ipAddress);
        }
        return null;
    }

    /**
     * 获取子网下已使用的IP列表
     *
     * @param subnetId 子网ID
     * @return 已使用的IP列表
     */
    @Override
    public List<String> getUsedIpList(String subnetId) {
        // 获取当前已使用的IP列表，包括子网下所有设备的IP地址。
        String subnetDeviceId = virtualIpDAO.getSubnetId(subnetId);
        Precondition.checkArgument(subnetDeviceId, "子网不存在");
        List<String> usedIpList = virtualIpDAO.getIpListBySubnetId(subnetDeviceId);

        // 查询自己数据库看看已经创建的虚拟IP
        List<VirtualIpDO> virtualIpDOS = virtualIpDAO.listBySubnetId(subnetId);
        if (ObjNullUtils.isNotNull(virtualIpDOS)) {
            for (VirtualIpDO virtualIpDO : virtualIpDOS) {
                usedIpList.add(virtualIpDO.getIpAddress());
            }
        } else {
            log.info("子网{}下没有虚拟IP", subnetId);
        }
        return usedIpList;
    }


    /**
     * 校验IP是否可用（支持IPv4和IPv6）
     *
     * @param subnetId 子网ID
     * @param ipAddress IP地址
     * @param usedIpList 已使用的IP列表，如果为null则会查询
     * @return 错误信息，如果为null表示可用
     */
    private String checkIpAvailable(String subnetId, String ipAddress, List<String> usedIpList) {
        // 查询子网信息
        NetworkSubnetOrder subnetOrder = networkSubnetOrderMapper.selectById(subnetId);
        Precondition.checkArgument(subnetOrder, "子网不存在");

        // 获取CIDR和IP版本
        String cidr = subnetOrder.getCidr();
        String ipVersion = subnetOrder.getIpVersion();

        // 检查IP地址格式是否正确
        int detectedVersion = IpUtils.getIpVersion(ipAddress);
        if (detectedVersion == 0) {
            return String.format("IP地址格式不正确: %s", ipAddress);
        }

        // 检查IP版本是否匹配子网版本
        boolean isIpv4 = detectedVersion == 4;
        boolean isIpv6 = detectedVersion == 6;
        if(isIpv6){
            ipAddress = IpUtils.compressIpv6Address(ipAddress);
        }
        if ((isIpv4 && !"IPv4".equals(ipVersion)) || (isIpv6 && !"IPv6".equals(ipVersion))) {
            return String.format("IP地址版本不匹配，子网版本: %s，IP地址版本: %s",
                ipVersion, isIpv4 ? "IPv4" : "IPv6");
        }

        // 检查是否是网关地址
        String gateway = subnetOrder.getGateway();
        if(isIpv6){
            gateway = IpUtils.compressIpv6Address(gateway);
        }
        if (ipAddress.equals(gateway)) {
            return String.format("IP:%s 不可设置成 子网:%s 的网关地址", ipAddress, cidr);
        }

        try {
            String errMsg = isIpv4 ? checkIpv4Available(ipAddress, cidr) : checkIpv6Available(ipAddress, cidr);
            if (errMsg != null) {
                return errMsg;
            }
        } catch (Exception e) {
            log.error("校验IP可用性时发生错误，IP: {}, CIDR: {}, error: {}", ipAddress, cidr, e.getMessage());
            return String.format("校验IP地址时发生错误: %s", e.getMessage());
        }

        // 检查是否已被使用
        if (usedIpList == null) {
            usedIpList = getUsedIpList(subnetId);
        }
        if (usedIpList.contains(ipAddress)) {
            return String.format("IP:%s 已被其他设备使用，请选择其他可用地址", ipAddress);
        }

        return null;
    }

    /**
     * 校验IPv4地址是否可用
     *
     * @param ipAddress IPv4地址
     * @param cidr IPv4 CIDR
     * @return 错误信息，如果为null表示可用
     */
    private String checkIpv4Available(String ipAddress, String cidr) {
        // 检查是否是广播地址
        long broadcastLong = IpUtils.getBroadcastLong(cidr);
        String broadcastAddress = IpUtils.longToIpv4(broadcastLong);
        if (ipAddress.equals(broadcastAddress)) {
            return String.format("IP:%s 不可设置成 子网:%s 的广播地址", ipAddress, cidr);
        }

        // 检查是否是网络地址
        long networkLong = IpUtils.getNetworkLong(cidr);
        String networkAddress = IpUtils.longToIpv4(networkLong);
        if (ipAddress.equals(networkAddress)) {
            return String.format("IP:%s 不可设置成 子网:%s 的网络地址", ipAddress, cidr);
        }

        // 检查是否在子网范围内
        if (!IpUtils.isIpInCidr(ipAddress, cidr)) {
            return String.format("IP:%s 不在子网:%s 的有效范围内，请选择 %s 到 %s 之间的地址",
                    ipAddress, cidr,
                    IpUtils.longToIpv4(networkLong + 1),
                    IpUtils.longToIpv4(broadcastLong - 1));
        }

        return null;
    }

    /**
     * 校验IPv6地址是否可用
     *
     * @param ipAddress IPv6地址
     * @param cidr IPv6 CIDR
     * @return 错误信息，如果为null表示可用
     */
    private String checkIpv6Available(String ipAddress, String cidr) {
        // 检查是否是网络地址
        String networkAddress = IpUtils.getIpv6NetworkAddress(cidr);
        if (ipAddress.equals(networkAddress)) {
            return String.format("IP:%s 不可设置成 子网:%s 的网络地址", ipAddress, cidr);
        }

        // 检查是否在子网范围内
        if (!IpUtils.isIpv6InCidr(ipAddress, cidr)) {
            String maxAddress = IpUtils.getIpv6MaxAddress(cidr);
            return String.format("IP:%s 不在子网:%s 的有效范围内，请选择 %s 到 %s 之间的地址",
                    ipAddress, cidr, networkAddress, maxAddress);
        }

        return null;
    }
}