package com.datatech.slgzt.utils;

import lombok.SneakyThrows;
import org.roaringbitmap.longlong.Roaring64NavigableMap;

import java.io.*;
import java.util.Base64;

public class BitMapUtil {


    // Roaring64NavigableMap → Base64
    @SneakyThrows
    public static String roaringToBase64(Roaring64NavigableMap rb) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        DataOutputStream dos = new DataOutputStream(baos);
        rb.runOptimize(); // 范围压缩
        rb.serialize(dos);
        dos.close();
        return Base64.getEncoder().encodeToString(baos.toByteArray());
    }

    // Base64 → Roaring64NavigableMap
    @SneakyThrows
    public static Roaring64NavigableMap base64ToRoaring(String base64)  {
        byte[] data = Base64.getDecoder().decode(base64);
        ByteArrayInputStream bais = new ByteArrayInputStream(data);
        DataInputStream dis = new DataInputStream(bais);
        Roaring64NavigableMap rb = new Roaring64NavigableMap();
        rb.deserialize(dis);
        return rb;
    }

    public static void main(String[] args) throws IOException {

        Roaring64NavigableMap rb64 = new Roaring64NavigableMap();
        rb64.add(1);
        rb64.add(100);
        rb64.add(1_000_000);
        rb64.add(1_000_000);
        // rb64.add(2_000_000_000);
        // rb64.add(108825);
        // rb64.add(2108825);
        rb64.add(100000108825L);
        rb64.add(100000108826L);
        rb64.add(200000108826L);
        rb64.add(900000125370L);
        rb64.add(800000000001L);
        rb64.add(800000000002L);
        Roaring64NavigableMap rb641 = new Roaring64NavigableMap();
        rb641.add(1);
        rb641.add(100);
        rb641.add(1_000_000);
        rb641.add(1_000_000);
        // rb64.add(2_000_000_000);
        // rb64.add(108825);
        // rb64.add(2108825);
        rb641.add(100000108825L);
        rb641.add(100000108826L);
        rb641.add(200000108826L);
        rb641.add(900000125370L);
        rb641.add(800000000001L);
        rb641.add(800000200002L);
        for (int i = 0; i <10000 ; i++) {
            rb641.add(i);
        }
        rb64.or(rb641);
        // 转成 Base64 存储
        String base64 = roaringToBase64(rb64);
        System.out.println("Base64 长度: " + base64.length());
        System.out.println("Base64 内容: " + base64);

        // 还原
        Roaring64NavigableMap restored = base64ToRoaring(base64);
        System.out.println("还原后restored: " + restored);
        System.out.println("还原后是否包含 100: " + restored.contains(100));
        System.out.println("还原后是否包含 101: " + restored.contains(101));
        System.out.println("还原后 ID 数量: " + restored.getIntCardinality());


        System.out.println(base64ToRoaring("AAAAAAEAAAAXOjAAAAEAAAB4SAAAEAAAAMrV"));
    }
}
