package com.datatech.slgzt.model.resourcce;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * @Author: liu<PERSON><PERSON>an
 * @Date: 2025/3/19
 */

@Data
public class ResourceShowInfoDTO {

    /**
     * 云主机、gpu资源概览
     */
    private ExtendEcsResource ecs;

    private ExtendEcsResource gcs;

    /**
     * 对象存储资源概览
     */
    private ExtendObsResource obs;

    /**
     * 云硬盘资源概览
     */
    private ExtendEvsResource evs;

    /**
     * 云硬盘资源概览
     */
    private ExtendSlbResource slb;

    /**
     * nat网关资源概览
     */
    private ExtendNatResource nat;

    /**
     * 容器资源配额概览
     */
    private ExtendCQResource cq;

    /**
     * 容器资源配额概览
     */
    private ExtendEcsResource mysql;

    private ExtendEcsResource postgreSql;

    private ExtendEcsResource redis;

    private ExtendBackupResource backup;

    private ExtendVpnResource vpn;

    private ExtendNasResource nas;

    @JsonProperty("pm")
    private ExtendPhysicalMachineResource physicalMachine;

    private ExtendFlinkResource flink;

    private ExtendKafkaResource kafka;

    private ExtendEsResource es;

    private ExtendBldRedisResource bldRedis;
}

