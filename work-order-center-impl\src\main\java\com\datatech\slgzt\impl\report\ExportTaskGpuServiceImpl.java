package com.datatech.slgzt.impl.report;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.component.FtpConfig;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.*;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.opm.ExportTaskOpm;
import com.datatech.slgzt.model.query.DeviceInfoQuery;
import com.datatech.slgzt.model.query.DeviceMetricQuery;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.model.report.GpuReportExcelDTO;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.service.ExportTaskService;
import com.datatech.slgzt.utils.FTPUtil;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.StreamUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * GPU报表导出服务实现
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@Service
public class ExportTaskGpuServiceImpl implements ExportTaskService {

    ExecutorService executor = Executors.newFixedThreadPool(2);

    @Resource
    private ExportTaskManager exportTaskManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private DeviceGpuInfoManager deviceGpuInfoManager;

    @Resource
    private DeviceVirtualInfoManager deviceVirtualInfoManager;

    @Resource
    private DeviceCardMetricsManager deviceCardMetricsManager;

    @Resource
    private FtpConfig ftpConfig;

    private static final String EXPORT_PATH = "export/gpu/";
    private static final String BUSINESS_FILE_NAME = "GPU_REPORT";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    @Override
    public void export(ExportTaskOpm opm) {
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        // 创建导出任务记录
        ExportTaskDTO taskDTO = new ExportTaskDTO();
        taskDTO.setReportName(opm.getReportName());
        taskDTO.setBusinessType(getReportType());
        taskDTO.setStatType(opm.getStatType());
        taskDTO.setStartTime(opm.getStartTime());
        taskDTO.setEndTime(opm.getEndTime());
        taskDTO.setCreator(currentUser.getUserName());
        taskDTO.setQueryCondition(opm.getQueryCondition());
        taskDTO.setStatus(0); // 0-生成中
        taskDTO.setCreateTime(LocalDateTime.now());
        taskDTO.setExportFields(JSON.toJSONString(opm.getExportFields())); // 保存导出字段列表
        String taskId = exportTaskManager.createTask(taskDTO);
        taskDTO.setId(taskId);

        executor.execute(() -> {
            // 生成Excel文件
            String fileName = generateExcelFileName();
            String filePath = EXPORT_PATH + fileName;
            // 设置任务的文件名
            taskDTO.setFileName(fileName);
            // 确保导出目录存在
            File exportDir = new File(EXPORT_PATH);
            if (!exportDir.exists()) {
                exportDir.mkdirs();
            }

            try {
                // 使用GpuReportExcelDTO进行GPU报表导出
                ExcelWriter excelWriter = EasyExcel.write(filePath, GpuReportExcelDTO.class)
                                                   .includeColumnFiledNames(opm.getExportFields())
                                                   .build();
                WriteSheet writeSheet = EasyExcel.writerSheet("GPU资源使用情况").build();

                List<GpuReportExcelDTO> allExportData = new ArrayList<>();

                // 1. 查询pm产品类型的资源详情（裸金属）
                List<ResourceDetailDTO> pmResources = getPmResources(opm);
                List<ResourceDetailDTO> gcsResources = getGcsResources(opm);

                if (ObjNullUtils.isNotNull(pmResources)) {
                    // 2. 通过IP查询GPU设备信息和指标数据
                    List<GpuReportExcelDTO> gpuData = getGpuDataByIp(pmResources, opm);
                    allExportData.addAll(gpuData);
                }
                if (ObjNullUtils.isNotNull(gcsResources)) {
                    // 2. 通过IP查询GPU设备信息和指标数据
                    List<GpuReportExcelDTO> gcsGpuData = getGscGpuDataByIp(gcsResources, opm);
                    allExportData.addAll(gcsGpuData);
                }

                // 按照数据时间倒序排列
                allExportData.sort((a, b) -> b.getDataTime().compareTo(a.getDataTime()));
                // 写入Excel
                excelWriter.write(allExportData, writeSheet);
                excelWriter.finish();

                // FTP上传
                uploadToFtp(filePath, taskDTO);

                // 更新任务状态为完成
                taskDTO.setStatus(1); // 1-完成
                exportTaskManager.updateTask(taskDTO);
                log.info("GPU export completed successfully. File path: {}", filePath);

            } catch (Exception e) {
                log.error("GPU export failed", e);
                // 更新任务状态为失败
                taskDTO.setStatus(2); // 2-失败
                exportTaskManager.updateTask(taskDTO);
            }
        });
    }

    /**
     * 获取pm产品类型的资源详情
     */
    private List<ResourceDetailDTO> getPmResources(ExportTaskOpm opm) {
        ResourceDetailQuery query = new ResourceDetailQuery();
        query.setType(ProductTypeEnum.PHYSICAL_MACHINE.getCode());
        return resourceDetailManager.list(query);
    }

    /**
     * 获取gcs产品类型的资源详情
     */
    private List<ResourceDetailDTO> getGcsResources(ExportTaskOpm opm) {
        ResourceDetailQuery query = new ResourceDetailQuery();
        query.setType(ProductTypeEnum.GCS.getCode());
        return resourceDetailManager.list(query);
    }

    /**
     * 通过IP查询GPU数据
     */
    private List<GpuReportExcelDTO> getGpuDataByIp(List<ResourceDetailDTO> pmResources, ExportTaskOpm opm) {
        List<GpuReportExcelDTO> result = new ArrayList<>();

        // 按IP分组查询GPU设备信息
        for (ResourceDetailDTO pmResource : pmResources) {
            String ip = pmResource.getIp();
            if (ObjNullUtils.isNull(ip)) {
                continue;
            }
            try {
                // 通过IP查询GPU设备信息（参考ZsMapController#queryDeviceMetricsByIp的逻辑）
                List<DeviceGpuInfoDTO> deviceGpuInfoDTOS = deviceGpuInfoManager.selectDeviceGpuInfoList(
                        new DeviceInfoQuery().setDncIp(ip)
                );

                if (ObjNullUtils.isNull(deviceGpuInfoDTOS)) {
                    continue;
                }
                // 过滤掉deviceId为空的数据
                deviceGpuInfoDTOS = deviceGpuInfoDTOS.stream()
                                                     .filter(Objects::nonNull)
                                                     .filter(device -> ObjNullUtils.isNotNull(device.getDeviceId()))
                                                     .collect(Collectors.toList());

                if (ObjNullUtils.isNull(deviceGpuInfoDTOS)) {
                    continue;
                }
                // 提取设备ID列表
                List<String> deviceIdList = new ArrayList<>();
                for (DeviceGpuInfoDTO deviceGpuInfoDTO : deviceGpuInfoDTOS) {
                    String deviceId = deviceGpuInfoDTO.getDeviceId();
                    if (deviceId.contains(",")) {
                        deviceIdList.addAll(Arrays.asList(deviceId.split(",")));
                    } else {
                        deviceIdList.add(deviceId);
                    }
                }
                //把deviceGpuInfoDTOS 整理成deviceId2Ip的map 后需要要用
                Map<String, String> deviceId2Ip = StreamUtils.toMap(deviceGpuInfoDTOS, DeviceGpuInfoDTO::getDeviceId, DeviceGpuInfoDTO::getDcnNetAddr);
                // 使用数据库层面聚合查询（性能优化）
                if (ObjNullUtils.isNotNull(deviceIdList) && !deviceIdList.isEmpty()) {
                    List<DeviceCardMetricsDTO> aggregatedData = deviceCardMetricsManager.queryGpuMetricsAggregated(
                            deviceIdList,
                            new DeviceMetricQuery()
                                    .setStartTime(opm.getStartTime())
                                    .setEndTime(opm.getEndTime())
                                    .setStatType(opm.getStatType())
                    );

                    // 按IP（PM资源）进行二次聚合：将同一IP下的多个GPU聚合为一条记录
                    List<GpuReportExcelDTO> exportData = convertAndAggregateByIp(aggregatedData, pmResource, deviceId2Ip);
                    result.addAll(exportData);
                }

            } catch (Exception e) {
                log.error("查询IP {} 的GPU数据失败", ip, e);
            }
        }

        return result;
    }


    /**
     * 通过IP查询GPU数据
     */
    private List<GpuReportExcelDTO> getGscGpuDataByIp(List<ResourceDetailDTO> gcsResources, ExportTaskOpm opm) {
        List<GpuReportExcelDTO> result = new ArrayList<>();

        // 按IP分组查询GPU设备信息
        for (ResourceDetailDTO gcsResource : gcsResources) {
            String ip = gcsResource.getIp();
            if (ObjNullUtils.isNull(ip)) {
                continue;
            }
            //IP是逗号隔开的拆分掉
            List<String> ipList = Arrays.asList(ip.split(","));
            // 遍历每个IP进行查询
            for (String ipStr : ipList) {
                // 通过IP查询GPU设备信息（参考ZsMapController#queryDeviceMetricsByIp的逻辑）
                List<DeviceVirtualInfoDTO> deviceGpuInfoDTOS = deviceVirtualInfoManager.selectDeviceVirtualInfoList(
                        new DeviceInfoQuery().setDncIp(ipStr)
                );

                if (ObjNullUtils.isNull(deviceGpuInfoDTOS)) {
                    continue;
                }
                // 过滤掉deviceId为空的数据
                deviceGpuInfoDTOS = deviceGpuInfoDTOS.stream()
                                                     .filter(Objects::nonNull)
                                                     .filter(device -> ObjNullUtils.isNotNull(device.getDeviceId()))
                                                     .collect(Collectors.toList());


                if (ObjNullUtils.isNull(deviceGpuInfoDTOS)) {
                    continue;
                }
                // 提取设备ID列表
                List<String> deviceIdList = new ArrayList<>();
                for (DeviceVirtualInfoDTO deviceGpuInfoDTO : deviceGpuInfoDTOS) {
                    deviceIdList.add(deviceGpuInfoDTO.getDeviceId());
                }
                //把deviceGpuInfoDTOS 整理成deviceId2Ip的map 后需要要用
                Map<String, String> deviceId2Ip = StreamUtils.toMap(deviceGpuInfoDTOS, DeviceVirtualInfoDTO::getDeviceId, DeviceVirtualInfoDTO::getDeviceIp);
                // 使用数据库层面聚合查询（性能优化）
                if (ObjNullUtils.isNotNull(deviceIdList) && !deviceIdList.isEmpty()) {
                    List<DeviceCardMetricsDTO> aggregatedData = deviceCardMetricsManager.queryGpuMetricsAggregated(
                            deviceIdList,
                            new DeviceMetricQuery()
                                    .setStartTime(opm.getStartTime())
                                    .setEndTime(opm.getEndTime())
                                    .setStatType(opm.getStatType())
                    );

                    // 按IP（PM资源）进行二次聚合：将同一IP下的多个GPU聚合为一条记录
                    List<GpuReportExcelDTO> exportData = convertAndAggregateByIp(aggregatedData, gcsResource,deviceId2Ip);
                    result.addAll(exportData);
                }
            }
            try {




            } catch (Exception e) {
                log.error("查询IP {} 的GPU数据失败", ip, e);
            }
        }

        return result;
    }



    /**
     * 按IP进行二次聚合：将同一IP下的多个GPU设备聚合为一条记录
     */
    private List<GpuReportExcelDTO> convertAndAggregateByIp(List<DeviceCardMetricsDTO> aggregatedData,
                                                           ResourceDetailDTO pmResource, Map<String, String> deviceId2Ip ) {
        // 按时间分组（同一个IP下可能有多个GPU设备，需要按时间聚合）
        Map<String, List<DeviceCardMetricsDTO>> groupedByTime = aggregatedData.stream()
                .collect(Collectors.groupingBy(DeviceCardMetricsDTO::getGpuTime));

        List<GpuReportExcelDTO> result = new ArrayList<>();

        for (Map.Entry<String, List<DeviceCardMetricsDTO>> entry : groupedByTime.entrySet()) {
            String timeKey = entry.getKey();
            List<DeviceCardMetricsDTO> deviceDataList = entry.getValue();

            if (ObjNullUtils.isNull(deviceDataList) || deviceDataList.isEmpty()) {
                continue;
            }
            // 聚合GPU设备信息：取第一个设备的基础信息
            DeviceCardMetricsDTO firstDevice = deviceDataList.get(0);

            GpuReportExcelDTO excelDTO = new GpuReportExcelDTO();

            // 设置时间
            excelDTO.setDataTime(timeKey);

            // 设置PM资源相关信息（从PM资源获取，不是从GPU设备）
            excelDTO.setRegionId(pmResource.getResourcePoolId());
            excelDTO.setRegionName(pmResource.getResourcePoolName());
            excelDTO.setBusinessSystemName(pmResource.getBusinessSysName());
            excelDTO.setDeviceName(pmResource.getDeviceName());
            excelDTO.setApplyUserName(pmResource.getApplyUserName());
            excelDTO.setDeviceIp(deviceId2Ip.get(firstDevice.getDeviceId()));
            // 设置云平台信息
            excelDTO.setDomainName(pmResource.getDomainName());
            excelDTO.setDeviceType(firstDevice.getDeviceType());
            excelDTO.setModelName(firstDevice.getModelName());
            // 聚合指标数据：计算平均利用率
            double avgGpuUtil = deviceDataList.stream()
                    .filter(data -> data.getGpuUtilPercent() != null)
                    .mapToDouble(DeviceCardMetricsDTO::getGpuUtilPercent)
                    .average()
                    .orElse(0.0);
            excelDTO.setGpuUtilPercent(BigDecimal.valueOf(avgGpuUtil).setScale(2, RoundingMode.HALF_UP));

            double avgMemUtil = deviceDataList.stream()
                    .filter(data -> data.getMemUtilpercent() != null)
                    .mapToDouble(DeviceCardMetricsDTO::getMemUtilpercent)
                    .average()
                    .orElse(0.0);
            excelDTO.setMemUtilPercent(BigDecimal.valueOf(avgMemUtil).setScale(2, RoundingMode.HALF_UP));

            // 计算平均温度
            double avgTemp = deviceDataList.stream()
                    .filter(data -> data.getDevGpuTemp() != null)
                    .mapToDouble(DeviceCardMetricsDTO::getDevGpuTemp)
                    .average()
                    .orElse(0.0);
            excelDTO.setTemperature(BigDecimal.valueOf(avgTemp).setScale(2, RoundingMode.HALF_UP));

            result.add(excelDTO);
        }

        return result;
    }

    /**
     * 上传文件到FTP
     */
    private void uploadToFtp(String filePath, ExportTaskDTO taskDTO) {
        try {
            // 创建FTP工具类实例
            FTPUtil ftpUtil = new FTPUtil(ftpConfig.getIp(), ftpConfig.getPort(), ftpConfig.getUser(), ftpConfig.getPass());
            // 构建远程FTP路径
            String remotePath = ftpConfig.getBasePath() + new SimpleDateFormat("yyyy-MM-dd/").format(new Date());
            // 上传文件到FTP
            File localFile = new File(filePath);
            boolean uploadResult = ftpUtil.uploadFile(remotePath, localFile);

            if (uploadResult) {
                // 上传成功,返回FTP上的完整路径
                String ftpFilePath = remotePath + localFile.getName();
                // 更新任务记录中的文件路径和文件名
                taskDTO.setFilePath(ftpFilePath);
                taskDTO.setFileName(localFile.getName());
                exportTaskManager.updateTask(taskDTO);
                log.info("GPU report file uploaded to FTP successfully: {}", ftpFilePath);
            } else {
                log.error("Failed to upload GPU report file to FTP server");
                throw new RuntimeException("Failed to upload GPU report file to FTP server");
            }
        } catch (IOException e) {
            log.error("Error uploading GPU report file to FTP: ", e);
            throw new RuntimeException("Error uploading GPU report file to FTP: " + e.getMessage());
        }
    }

    private String generateExcelFileName() {
        String timestamp = LocalDateTime.now().format(DATE_FORMATTER);
        return String.format("%s_%s.xlsx", BUSINESS_FILE_NAME, timestamp);
    }

    @Override
    public String getReportType() {
        return "GPU_REPORT";
    }
} 