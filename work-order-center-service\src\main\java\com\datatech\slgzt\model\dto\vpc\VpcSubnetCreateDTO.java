package com.datatech.slgzt.model.dto.vpc;

import com.datatech.slgzt.model.dto.network.AllocationPoolDTO;
import lombok.Data;

import java.util.List;

@Data
public class VpcSubnetCreateDTO {

    private String id;
    private String vpcId;

    private String regionCode;

    private Long tenantId;

    private String tenantName;

    /**
     * 子网可用区编码
     */
    private String azCode;

    private String functionalModule;

    /**
     * 子网名称
     */
    private String subnetName;


    /**
     * 开始ip
     */
    private String startIp;

    /**
     * 子网掩码
     */
    private String netmask;

    private String level2InstanceId;
    private String instanceId;

    private String uuid;

    private List<AllocationPoolDTO> allocationPools;
}
