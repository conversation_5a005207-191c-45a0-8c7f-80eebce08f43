package com.datatech.slgzt.impl.service.standard.param;

import com.datatech.slgzt.enums.ProductAttrCodeEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.ip.IPVersionEnum;
import com.datatech.slgzt.model.layout.ResOpenReqModel;
import com.datatech.slgzt.model.nostander.EipModel;
import com.datatech.slgzt.model.opm.ResOpenOpm;
import com.datatech.slgzt.service.standard.ResOpenParamStrategyService;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 联合开通ecs eip部分
 *
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月07日 17:14:29
 */
@Service
public class EipResOpenParamParamStrategyServiceImpl implements ResOpenParamStrategyService {


    @Override
    public List<ResOpenReqModel.ProductOrder> assembleParam(ResOpenOpm opm) {
        //获取租户id
        Long tenantId = opm.getTenantId();
        ArrayList<ResOpenReqModel.ProductOrder> productOrders = Lists.newArrayList();
        //-----------------3.弹性公网部分------------------------------------------------
        //取出弹性公网的数据 每个都要创建
        List<EipModel> eipModelList = opm.getEipModelList();
        for (EipModel eipModel : eipModelList) {
            ResOpenReqModel.ProductOrder eipProductOrder = new ResOpenReqModel.ProductOrder();
            eipProductOrder.setProductOrderId(eipModel.getId().toString());
            eipProductOrder.setProductOrderType("EIP_CREATE");
            eipProductOrder.setProductType(ProductTypeEnum.EIP.getCode());
            eipProductOrder.setSubOrderId(opm.getSubOrderId());
            eipProductOrder.setGId(eipModel.getGId());
            ResOpenReqModel.Attrs evsAttrs = new ResOpenReqModel.Attrs();
            evsAttrs.setBandwidth(eipModel.getBandwidth());
            evsAttrs.setName(eipModel.getEipName());
            evsAttrs.setEipName(eipModel.getEipName());
            evsAttrs.setAzCode(eipModel.getAzCode());
            evsAttrs.setGId(eipModel.getGId());
            evsAttrs.setEcsResourceId(eipModel.getVmId());
            //EIP是否存在绑定操作
            if (IPVersionEnum.IPV6.getDesc().equals(eipModel.getIpVersion())) {
                // deviceID 开通 ipv6 时必填，开通v4不能填
//                Precondition.checkArgument(eipModel.getVmId(), "IPv6的弹性公网必须绑定虚拟机");
                evsAttrs.setDeviceId(eipModel.getVmId());
            } else if (StringUtils.isNotBlank(eipModel.getVmId())) {
                eipProductOrder.setOperations(getOperations(eipModel));
            }
//            evsAttrs.setDeviceType(opm.getDeviceType());
            eipProductOrder.setAttrs(evsAttrs);
            productOrders.add(eipProductOrder);
        }
        return productOrders;
    }

    //云硬盘挂载操作参数构建
    private static List<ResOpenReqModel.Operations> getOperations(EipModel eipModel) {
        List<ResOpenReqModel.Operations> newOpertionList = new ArrayList<>();
        ResOpenReqModel.OperationData operationData = new ResOpenReqModel.OperationData();
        operationData.setOperationId(eipModel.getVmId());
        List<ResOpenReqModel.OperationData> operationDataList = new ArrayList<>();
        operationDataList.add(operationData);
        ResOpenReqModel.Operations operations = new ResOpenReqModel.Operations();
        operations.setOperation(ProductAttrCodeEnum.EIP_BIND.getCode());
        operations.setOperationDataList(operationDataList);
        newOpertionList.add(operations);
        return newOpertionList;
    }

    /**
     * 注册strategy
     */
    @Override
    public ProductTypeEnum register() {
        return ProductTypeEnum.EIP;
    }
}
