package com.datatech.slgzt.impl.network;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.datatech.slgzt.dao.mapper.VmMapper;
import com.datatech.slgzt.dao.mapper.network.NetworkOrderMapper;
import com.datatech.slgzt.enums.ip.IPVersionEnum;
import com.datatech.slgzt.enums.ip.IpLevelEnum;
import com.datatech.slgzt.enums.ip.IpStatusEnum;
import com.datatech.slgzt.enums.ip.ResourceTypeEnum;
import com.datatech.slgzt.handle.CmdbComponentClient;
import com.datatech.slgzt.manager.CmdbIP3StatusManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.cmdb.CmdbCommonRep;
import com.datatech.slgzt.model.cmdb.QueryCmdbRep;
import com.datatech.slgzt.model.dto.RegionCmdbDTO;
import com.datatech.slgzt.model.dto.network.IPv6Network;
import com.datatech.slgzt.model.dto.network.IpListRc;
import com.datatech.slgzt.model.vo.network.ip.*;
import com.datatech.slgzt.page.PageResp;
import com.datatech.slgzt.service.CmdbIP3StatusService;
import com.datatech.slgzt.service.CmdbOpenApi;
import com.datatech.slgzt.service.network.NetworkCollectService;
import com.google.common.collect.Lists;
import inet.ipaddr.IPAddress;
import inet.ipaddr.IPAddressString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.util.SubnetUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.net.InetAddress;
import java.util.*;

@Slf4j
@Service
public class NetworkServiceImpl implements NetworkCollectService {
    @Resource
    CmdbOpenApi cmdbOpenApi;

    @Resource
    private CmdbComponentClient cmdbClient;

    @Resource
    private VmMapper vmMapper;

    @Resource
    private NetworkOrderMapper networkOrderMapper;

    @Resource
    private CmdbIP3StatusManager cmdbIP3StatusManager;
    @Resource
    private CmdbIP3StatusService cmdbIP3StatusService;

    public List<I3_IPVO> querySegmentI3(IpListRc ipListRc) {

        Map<String, Object> map = new HashMap<>();
        Map<String, Object> map1 = new HashMap<>();
        List<Object> list = new ArrayList<>();

        if (StringUtils.isNotEmpty(ipListRc.getRelatedPool())) map.put("RELATED_POOL", ipListRc.getRelatedPool());
        if (ObjectUtil.isNotEmpty(ipListRc.getType())) map.put("TYPE", ipListRc.getType());
        if (StringUtils.isNotEmpty(ipListRc.getVpn())) map.put("vpn", ipListRc.getVpn());
        if (IPVersionEnum.getByDesc(ipListRc.getIpVersion()) != null) map.put("family_type", ipListRc.getIpVersion());
        if (StringUtils.isNotEmpty(ipListRc.getCloud())) {
            map.put("CLOUD", ipListRc.getCloud());
        }
        if (StringUtils.isNotEmpty(ipListRc.getPrefix())) {
            map.put("prefix", ipListRc.getPrefix());
        }
        if (null != ipListRc.getMask()) {
            map.put("mask", ipListRc.getMask());
        }
        if (StringUtils.isNotEmpty(ipListRc.getInstanceId())) {
            map.put("instanceId", ipListRc.getInstanceId());
        }
        if ("私网地址".equals(ipListRc.getNetworkPlane())){
            map.put("RELATED_POOL", ipListRc.getRelatedPool());
        }
        list.add(map);
        map1.put("$and", list);
        Map<String, Object> map2 = new HashMap<>();
        map2.put("query", map1);
        map2.put("page", 1);
        String queryParam = JSONObject.toJSONString(map2, SerializerFeature.WriteMapNullValue);
        log.info("三级IP查询参数：{}", queryParam);
        List<I3_IPVO> resources = Lists.newArrayList();
        CmdbCommonRep<QueryCmdbRep<JSONObject>> total = cmdbOpenApi.queryCmdbData("I3_IP", queryParam);
        long totalPage = getTotalPage(total.getData().getTotal(), 3000);
        log.info("++++++++totalPage:{}", totalPage);
        for (long i = 1; i <= totalPage; i++) {
            map2.put("page", i);
            map2.put("page_size", 3000);
            String req = JSONObject.toJSONString(map2, SerializerFeature.WriteMapNullValue);
            log.info("三级IP查询参数：{}", req);
            CmdbCommonRep<QueryCmdbRep<JSONObject>> rep = cmdbOpenApi.queryCmdbData("I3_IP", req);
            List<I3_IPVO> i3IpVos = JSONArray.parseArray(rep.getData().getList().toString(), I3_IPVO.class);
            resources.addAll(i3IpVos);
        }
        return resources;
    }


    public PageResp<IPVO> querySegmentI2(IpListRc ipListRc) {

        Map<String, Object> map = new HashMap<>();
        Map<String, Object> map1 = new HashMap<>();
        List<Object> list = new ArrayList<>();
        if (StringUtils.isNotEmpty(ipListRc.getRelatedPool())) map.put("RELATED_POOL", ipListRc.getRelatedPool());
        if (ObjectUtil.isNotEmpty(ipListRc.getType())) map.put("TYPE", ipListRc.getType());
        if (StringUtils.isNotEmpty(ipListRc.getVpn())) {
            Map<String, Object> vpnMap = new HashMap<>();
            vpnMap.put("$like", "%" + ipListRc.getVpn() + "%");
            map.put("vpn", vpnMap);
        }
        if (IPVersionEnum.getByDesc(ipListRc.getIpVersion()) != null) map.put("family_type", ipListRc.getIpVersion());
        if (StringUtils.isNotEmpty(ipListRc.getCloud())) {
            map.put("CLOUD", ipListRc.getCloud());
        }
        if (StringUtils.isNotEmpty(ipListRc.getPrefix())) {
            Map<String, Object> prefixMap = new HashMap<>();
            prefixMap.put("$like", "%" + ipListRc.getPrefix() + "%");
            map.put("prefix", prefixMap);
        }
        if (null != ipListRc.getMask()) {
            map.put("mask", ipListRc.getMask());
        }
        if (StringUtils.isNotEmpty(ipListRc.getInstanceId())) {
            map.put("instanceId", ipListRc.getInstanceId());
        }
        list.add(map);
        map1.put("$and", list);
        Map<String, Object> map2 = new HashMap<>();
        map2.put("query", map1);
        Integer pageNum = ipListRc.getPageNum() == null ? 1 : ipListRc.getPageNum();
        Integer pageSize = ipListRc.getPageSize() == null ? 10 : ipListRc.getPageSize();
        map2.put("page", pageNum);
        map2.put("page_size", pageSize);
        String queryParam = JSONObject.toJSONString(map2, SerializerFeature.WriteMapNullValue);
        log.info("二级IP查询参数：{}", queryParam);
        List<I2_IPVO> resources = Lists.newArrayList();
        CmdbCommonRep<QueryCmdbRep<JSONObject>> rep = cmdbOpenApi.queryCmdbData("I2_IP", queryParam);
        List<I2_IPVO> i2IpVos = JSONArray.parseArray(rep.getData().getList().toString(), I2_IPVO.class);
        List<IPVO> ipvos = new ArrayList<>();
        for (I2_IPVO i2_ipvo : i2IpVos) {
            IPVO ipvo = new IPVO();
            BeanUtils.copyProperties(i2_ipvo, ipvo);
            ipvo.setIpVersion(i2_ipvo.getFamilyType());
            ipvos.add(ipvo);
        }
        return new PageResp<>(ipvos, pageNum, pageSize, rep.getData().getTotal());

    }

    public PageResp<IPVO> queryIp3ByIp2(IpListRc ipListRc) {
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> map1 = new HashMap<>();
        List<Object> list = new ArrayList<>();

        if (StringUtils.isNotEmpty(ipListRc.getRelatedPool())) map.put("RELATED_POOL", ipListRc.getRelatedPool());
        if (ObjectUtil.isNotEmpty(ipListRc.getType())) map.put("TYPE", ipListRc.getType());
        if (IPVersionEnum.getByDesc(ipListRc.getIpVersion()) != null) map.put("family_type", ipListRc.getIpVersion());
        if (StringUtils.isNotEmpty(ipListRc.getCloud())) {
            map.put("CLOUD", ipListRc.getCloud());
        }
        if (StringUtils.isNotEmpty(ipListRc.getInstanceId())) {
            map.put("instanceId", ipListRc.getInstanceId());
        }
        list.add(map);
        map1.put("$and", list);
        Map<String, Object> map2 = new HashMap<>();
        map2.put("query", map1);
        Integer pageNum = ipListRc.getPageNum() == null ? 1 : ipListRc.getPageNum();
        Integer pageSize = ipListRc.getPageSize() == null ? 10 : ipListRc.getPageSize();
        map2.put("page", pageNum);
        map2.put("page_size", pageSize);
        Map<String, Object> map3 = new HashMap<>();
        map3.put("I3_IP", true);
        map2.put("fields", map3);
        String queryParam = JSONObject.toJSONString(map2, SerializerFeature.WriteMapNullValue);
        log.info("三级IP查询参数：{}", queryParam);
        CmdbCommonRep<QueryCmdbRep<JSONObject>> rep = cmdbOpenApi.queryCmdbData("I2_IP", queryParam);
        List<I2_FIELDS> i2IpVos = JSONArray.parseArray(rep.getData().getList().toString(), I2_FIELDS.class);
        List<IPVO> ipvos = new ArrayList<>();
        for (I3_IPVO i3Ipvo : i2IpVos.get(0).getI3_IP()) {
            if ( (StringUtils.isNotEmpty(ipListRc.getPrefix()) && !i3Ipvo.getPrefix().contains(ipListRc.getPrefix())) ||
                    (StringUtils.isNotEmpty(ipListRc.getVpn()) && !i3Ipvo.getVpn().contains(ipListRc.getVpn())) ||
                    (null != ipListRc.getMask() && !i3Ipvo.getMask().equals(ipListRc.getMask().toString()))) {
                continue;
            }
            //获取全部状态则不需校验
            if(null != ipListRc.getStatus() && !IpStatusEnum.ALL.equals(ipListRc.getStatus())){
                if(IpStatusEnum.UNDISTRIBUTED.equals(ipListRc.getStatus()) && !IpStatusEnum.UNDISTRIBUTED.getDesc().equals(i3Ipvo.getState())){
                    continue;
                }
                if(IpStatusEnum.ASSIGNED.equals(ipListRc.getStatus()) && !IpStatusEnum.ASSIGNED.getDesc().equals(i3Ipvo.getState())){
                    continue;
                }
            }
            IPVO ipvo = new IPVO();
            BeanUtils.copyProperties(i3Ipvo, ipvo);
            ipvo.setIpVersion(i3Ipvo.getFamilyType());
            ipvos.add(ipvo);
        }
        int total = ipvos.size();
        int start = (pageNum - 1) * pageSize;
        int end = Math.min(start + pageSize, total);
        List<IPVO> pagedIpvos = ipvos.subList(start, end);
        return new PageResp(pagedIpvos, pageNum, pageSize, total);
    }

    @Override
    public CommonResult divideSubnet(DivideSubnetRc divideSubnetRc) {
        log.info("子网切割：{}", JSONObject.toJSONString(divideSubnetRc));
        RegionCmdbDTO regionCmdb = vmMapper.getRegionCmdb(divideSubnetRc.getRegionCode());
        divideSubnetRc.setCloud(regionCmdb.getCloud());
        IpListRc ipListRc = new IpListRc();
        ipListRc.setCloud(divideSubnetRc.getCloud());
        ipListRc.setInstanceId(divideSubnetRc.getInstanceId());
        PageResp<IPVO> i2IpVoList = querySegmentI2(ipListRc);
        String instanceId = null;
        if (!i2IpVoList.getRecords().isEmpty()) {
            IPVO i2IpVo = i2IpVoList.getRecords().get(0);
            for (DivideSubnetRc.Subnet subnet : divideSubnetRc.getSubnets()) {
                Map requestMap = new HashMap();
                requestMap.put("CLOUD", i2IpVo.getCloud());
                requestMap.put("RELATED_POOL", i2IpVo.getRelatedPool());
                requestMap.put("family_type", i2IpVo.getIpVersion());
                requestMap.put("TYPE", i2IpVo.getType());
                requestMap.put("PURPOSE", i2IpVo.getPurpose());
                requestMap.put("prefix", subnet.getPrefix());
                requestMap.put("mask", subnet.getMask());
                requestMap.put("state", "未分配");
                requestMap.put("vpn", subnet.getVpn());

                String queryParam = JSONObject.toJSONString(requestMap);
                log.info("新增cmdb实例类型：{}，参数：{}", "I3_IP", queryParam);
                CmdbCommonRep commonRep = cmdbClient.insertCmdbData("I3_IP", requestMap);
                log.info("新增cmdb实例出参：{}", commonRep);
                if (!commonRep.getCode().equals("0")) {
                    log.info("新增cmdb实例失败");
                    return CommonResult.failure(ResourceTypeEnum.SUBNET.getDesc() + "-分割失败:" + commonRep.getMessage());
                }
                I3_IPVO i3IpVo = JSONObject.parseObject(JSONObject.toJSONString(commonRep.getData()), I3_IPVO.class);
                cmdbIP3StatusManager.insert(i3IpVo.getInstanceId());
                instanceId = i3IpVo.getInstanceId();
                addCmdbRelation("I3_IP", "I2_IP", Lists.newArrayList(i3IpVo.getInstanceId()), Lists.newArrayList(i2IpVo.getInstanceId()));
            }
        }
        HashMap<String, String> data = new HashMap<>();
        data.put("instanceId", instanceId);
        return CommonResult.success(data);
    }

    @Override
    public List<IpCheckRep> ipCheck(IpCheckRc ipCheckRc) {
        log.info("IP校验：{}", JSONObject.toJSONString(ipCheckRc));
        RegionCmdbDTO regionCmdb = vmMapper.getRegionCmdb(ipCheckRc.getRegionCode());
        ipCheckRc.setCloud(regionCmdb.getCloud());
        //status true 未注册 false 已注册
        List<IpCheckRep> ipCheckReps = new ArrayList<>();
        if(ipCheckRc.getSubnets().size()>1 && hasOverlap(ipCheckRc.getSubnets())){
            for(String subnet : ipCheckRc.getSubnets()){
                IpCheckRep ipCheckRep = new IpCheckRep();
                ipCheckRep.setSubnet(subnet);
                ipCheckRep.setStatus(false);
                ipCheckRep.setMessage("传入的三级网段IP存在冲突");
                ipCheckReps.add(ipCheckRep);
            }
            return ipCheckReps;
        }
        IpListRc ipListRc = new IpListRc();
        ipListRc.setCloud(ipCheckRc.getCloud());
        ipListRc.setInstanceId(ipCheckRc.getInstanceId());
        PageResp<IPVO> i2IpVoList = querySegmentI2(ipListRc);
        IPVO i2IpVo = new IPVO();
        if (!i2IpVoList.getRecords().isEmpty()) {
            i2IpVo = i2IpVoList.getRecords().get(0);
        }
        for (String subnet : ipCheckRc.getSubnets()) {
            IpCheckRep ipCheckRep = new IpCheckRep();
            ipCheckRep.setSubnet(subnet);
            String prefix = subnet.split("/")[0];
            String mask = subnet.split("/")[1];
            IpListRc i3Req = new IpListRc();
            i3Req.setRelatedPool(regionCmdb.getCmdbName());
            i3Req.setNetworkPlane(ipCheckRc.getNetworkPlane());
            i3Req.setCloud(ipCheckRc.getCloud());
            i3Req.setPrefix(prefix);
            i3Req.setMask(Integer.valueOf(mask));
            List<I3_IPVO> i3IpVoList = querySegmentI3(i3Req);
            if (!i3IpVoList.isEmpty()) {
                ipCheckRep.setStatus(false);
                ipCheckRep.setMessage("已在CMDB注册");
            }else{
                //校验是否在二级网段内
                if(isSubnetOf(subnet, i2IpVo.getPrefix() + "/" + i2IpVo.getMask())){
                    //校验新划分的三级网段是否在已注册的三级网段内
                    ipCheckRep.setStatus(true);
                    ipCheckRep.setMessage("");
                    //已存在的三级网段
                    IpListRc queryReq = new IpListRc();
                    queryReq.setCloud(ipCheckRc.getCloud());
                    queryReq.setIpVersion(i2IpVo.getIpVersion());
                    queryReq.setInstanceId(i2IpVo.getInstanceId());
                    queryReq.setStatus(IpStatusEnum.ALL);
                    queryReq.setPageNum(1);
                    queryReq.setPageSize(3000);
                    PageResp<IPVO> ipvos = queryIp3ByIp2(queryReq);
                    for(IPVO i3 : ipvos.getRecords()){
                        List<String> cidrList = new ArrayList<>();
                        cidrList.add(i3.getPrefix() + "/" + i3.getMask());
                        cidrList.add(subnet);
                        if(hasOverlap(cidrList)){
                            ipCheckRep.setStatus(false);
                            ipCheckRep.setMessage("与存量三级网段冲突");
                            break;
                        }
                    }
                }else{
                    ipCheckRep.setStatus(false);
                    ipCheckRep.setMessage("不存在于所选二级网段内");
                }

            }
            ipCheckReps.add(ipCheckRep);
        }

        return ipCheckReps;
    }

    public static boolean hasOverlap(List<String> cidrList) {
        if (cidrList == null || cidrList.size() < 2) {
            return false; // 不足两个CIDR无法进行比较
        }

        boolean isIPv6 = cidrList.get(0).contains(":");
        if (isIPv6) {
            return isOverlapIpv6(cidrList);
        } else {
            return isOverlapIpv4(cidrList);
        }
    }
    public static boolean isOverlapIpv4(List<String> cidrList) {
        for (int i = 0; i < cidrList.size(); i++) {
            IPAddress blockI = new IPAddressString(cidrList.get(i)).getAddress();
            for (int j = i + 1; j < cidrList.size(); j++) {
                IPAddress blockJ = new IPAddressString(cidrList.get(j)).getAddress();

                // 获取两个网络的范围
                IPAddress lowerBoundBlockI = blockI.getLower();
                IPAddress upperBoundBlockI = blockI.getUpper();
                IPAddress lowerBoundBlockJ = blockJ.getLower();
                IPAddress upperBoundBlockJ = blockJ.getUpper();

                // 检查是否有重叠
                if (lowerBoundBlockI.compareTo(upperBoundBlockJ) <= 0 &&
                        lowerBoundBlockJ.compareTo(upperBoundBlockI) <= 0) {
                    return true;
                }
            }
        }
        return false;
    }
    public static boolean isOverlapIpv6(List<String> cidrList) {
        List<IPv6Network> networks = new ArrayList<>();
        for (String cidr : cidrList) {
            networks.add(new IPv6Network(cidr));
        }

        for (int i = 0; i < networks.size(); i++) {
            for (int j = i + 1; j < networks.size(); j++) {
                if (networks.get(i).overlaps(networks.get(j))) {
                    return true;
                }
            }
        }
        return false;
    }

    private static long ipToLong(String ipAddress) {
        long result = 0;
        for (String part : ipAddress.split("\\.")) {
            result = result * 256 + Integer.parseInt(part);
        }
        return result;
    }

    @Override
    public PageResp<IPVO> ipList(IpListRc ipListRc) {
        RegionCmdbDTO regionCmdb = vmMapper.getRegionCmdb(ipListRc.getRegionCode());
        ipListRc.setCloud(regionCmdb.getCloud());
        ipListRc.setRelatedPool(regionCmdb.getCmdbName());
        PageResp<IPVO> ipvos = null;
        if (ipListRc.getIpLevel().equals(IpLevelEnum.IP2)) {
            ipvos = querySegmentI2(ipListRc);
        } else {
            //查询三级IP只查未分配的
            ipListRc.setStatus(IpStatusEnum.UNDISTRIBUTED);
            ipvos = queryIp3ByIp2(ipListRc);
        }
        return ipvos;
    }

    @Override
    public PageResp<I3_IPPage> ip3List(IpListRc ipListRc) {
        RegionCmdbDTO regionCmdb = vmMapper.getRegionCmdb(ipListRc.getRegionCode());
        ipListRc.setCloud(regionCmdb.getCloud());
        ipListRc.setRelatedPool(regionCmdb.getCmdbName());
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> map1 = new HashMap<>();
        List<Object> list = new ArrayList<>();

        if (StringUtils.isNotEmpty(ipListRc.getRelatedPool())) map.put("RELATED_POOL", ipListRc.getRelatedPool());
        if (StringUtils.isNotEmpty(ipListRc.getVpn())) {
            Map<String, Object> vpnMap = new HashMap<>();
            vpnMap.put("$like", "%" + ipListRc.getVpn() + "%");
            map.put("vpn", vpnMap);
        }
        if (IPVersionEnum.getByDesc(ipListRc.getIpVersion()) != null) map.put("family_type", ipListRc.getIpVersion());
        if (StringUtils.isNotEmpty(ipListRc.getCloud())) {
            map.put("CLOUD", ipListRc.getCloud());
        }
        if (StringUtils.isNotEmpty(ipListRc.getPrefix())) {
            Map<String, Object> prefixMap = new HashMap<>();
            prefixMap.put("$like", "%" + ipListRc.getPrefix() + "%");
            map.put("prefix", prefixMap);
        }
        if (null != ipListRc.getMask()) {
            map.put("mask", ipListRc.getMask());
        }
        if (StringUtils.isNotEmpty(ipListRc.getInstanceId())) {
            map.put("instanceId", ipListRc.getInstanceId());
        }
        if ("私网地址".equals(ipListRc.getNetworkPlane())){
            map.put("RELATED_POOL", ipListRc.getRelatedPool());
        }
        String state = ipListRc.getState();
        if (Objects.nonNull(state)){
            if ("已划分".equals(state)) {
                map.put("state", "已分配");
            } else if ("未划分".equals(state)) {
                map.put("state", "未分配");
            }
        }
        list.add(map);
        map1.put("$and", list);
        Map<String, Object> map2 = new HashMap<>();
        map2.put("query", map1);
        map2.put("page", ipListRc.getPageNum());
        map2.put("page_size", ipListRc.getPageSize());
        String queryParam = JSONObject.toJSONString(map2, SerializerFeature.WriteMapNullValue);
        log.info("三级IP查询参数：{}", queryParam);
        CmdbCommonRep<QueryCmdbRep<JSONObject>> rep = cmdbOpenApi.queryCmdbData("I3_IP", queryParam);
        List<I3_IPPage> i3IpVos = JSONArray.parseArray(rep.getData().getList().toString(), I3_IPPage.class);
        i3IpVos.forEach(i3 -> {
            String isState = i3.getState();
            if ("已分配".equals(isState)) {
                i3.setState("已划分");
            } else if ("未分配".equals(isState)) {
                i3.setState("未划分");
            }
            if (Objects.nonNull(i3.getPrefix()) && Objects.nonNull(i3.getMask())) {
                i3.setPrefix(i3.getPrefix() + "/" + i3.getMask());
            }
        });
        return new PageResp<>(i3IpVos, ipListRc.getPageNum(), ipListRc.getPageSize(), rep.getData().getTotal());
    }

    @Override
    public CommonResult ipUpdateStatus(IpUpdateStatusVO ipUpdateStatusRc) {
        log.info("IP更新状态：{}", JSONObject.toJSONString(ipUpdateStatusRc));
        Map<String, Object> map = new HashMap<>();
        if ("0".equals(ipUpdateStatusRc.getStatus())) {
//            map.put("allocated","否");
            map.put("state", "未分配");
        } else if ("1".equals(ipUpdateStatusRc.getStatus())) {
//            map.put("allocated","是");
            map.put("state", "已分配");
        } else {
            log.error("ipUpdateStatus 状态参数错误");
            return CommonResult.failure("状态参数错误");
        }
        for (String instanceId : ipUpdateStatusRc.getInstanceId()){
            CmdbCommonRep<QueryCmdbRep<JSONObject>> resp = cmdbOpenApi.instance(ipUpdateStatusRc.getIpVersion().getDesc(), instanceId, map);
            if (!resp.getCode().equals("0")) {
                log.error("ipUpdateStatus 更新IP状态失败");
                return CommonResult.failure("更新IP状态失败");
            }
        }

        log.info("ipUpdateStatus 更新IP状态成功");
        return CommonResult.success();
    }

    @Override
    public PageResp<VlanResp> vlanList(String regionCode, String type, String vlan, Integer pageNum, Integer pageSize) {
        RegionCmdbDTO regionCmdb = vmMapper.getRegionCmdb(regionCode);
        String cloud = regionCmdb.getCloud();
        String relatedPool = regionCmdb.getCmdbName();
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> map1 = new HashMap<>();
        Map<String, Object> map2 = new HashMap<>();
        Map<String, Object> map3 = new HashMap<>();
        Map<String, Object> map4 = new HashMap<>();
        Map<String, Object> map5 = new HashMap<>();
        Map<String, Object> map6 = new HashMap<>();
        Map<String, Object> map7 = new HashMap<>();
        Map<String, Object> map8 = new HashMap<>();
        List<Object> list1 = new ArrayList<>();
        List<Object> list2 = new ArrayList<>();
        List<Object> list3 = new ArrayList<>();
        map.put("state", "未分配");
        map1.put("state", "已预埋");
        list1.add(map);
        list1.add(map1);
        map2.put("$or", list1);
        if (StringUtils.isNotEmpty(type)) {
            Map<String, Object> typeMap = new HashMap<>();
            typeMap.put("$like", "%" + type + "%");
            map3.put("type", typeMap);
            list2.add(map3);
        }
//        else {
//            map3.put("type", "管理面-业务");
//            map4.put("type", "业务面-业务");
//            map5.put("type", "业务面-QinQ外层");
//            list2.add(map3);
//            list2.add(map4);
//            list2.add(map5);
//        }
        map6.put("$or", list2);
        list3.add(map2);
        list3.add(map6);
        if (StringUtils.isNotEmpty(vlan)) {
            map8.put("value", vlan);
        }
        if (StringUtils.isNotEmpty(cloud)) {
            map8.put("CLOUD", cloud);
        }
        if (StringUtils.isNotEmpty(relatedPool)) {
            map8.put("RELATED_POOL", relatedPool);
        }
        if (!map8.isEmpty()) {
            list3.add(map8);
        }
        map7.put("$and", list3);
        Map<String, Object> map9 = new HashMap<>();
        map9.put("query", map7);
        map9.put("page", pageNum == null ? 1 : pageNum);
        map9.put("page_size", pageSize == null ? 10 : pageSize);
        String queryParam = JSONObject.toJSONString(map9);
        log.info("VLAN预查询参数：{}", queryParam);
        CmdbCommonRep<QueryCmdbRep<JSONObject>> rep = cmdbOpenApi.queryCmdbData("VLAN", queryParam);
        List<VlanVO> vlanVOS = JSONArray.parseArray(rep.getData().getList().toString(), VlanVO.class);
        List<VlanResp> vlanResps = Lists.newArrayList();
        for (VlanVO vlanVO : vlanVOS) {
            VlanResp vlanResp = new VlanResp();
            BeanUtils.copyProperties(vlanVO, vlanResp);
            vlanResp.setVlan(vlanVO.getValue());
            vlanResps.add(vlanResp);
        }
        return new PageResp<>(vlanResps, pageNum == null ? 1 : pageNum, pageSize == null ? 10 : pageSize, rep.getData().getTotal());
    }

    @Override
    public CommonResult vlanUpdateStatus(VlanUpdateStatusRc vlanUpdateStatusRc) {
        log.info("VLAN更新状态：{}", JSONObject.toJSONString(vlanUpdateStatusRc));
        Map<String, Object> map = new HashMap<>();
        if ("0".equals(vlanUpdateStatusRc.getStatus())) {
            map.put("state", "未分配");
        } else if ("1".equals(vlanUpdateStatusRc.getStatus())) {
            map.put("state", "已分配");
        } else {
            return CommonResult.failure("状态参数错误");
        }
        CmdbCommonRep<QueryCmdbRep<JSONObject>> resp = cmdbOpenApi.instance("VLAN", vlanUpdateStatusRc.getInstanceId(), map);
        if (!resp.getCode().equals("0")) {
            return CommonResult.failure("更新VLAN状态失败");
        }
        return CommonResult.success();
    }

    @Override
    public CommonResult networkCancel(String instanceId) {
        cmdbIP3StatusService.cancelByInstanceId(instanceId);
        return CommonResult.success();
    }

    /**
     * @param total 总数据量
     * @param size  每页数据
     */
    private long getTotalPage(long total, long size) {
        long pages = total / size;
        if (total % size != 0) {
            pages++;
        }
        return pages;
    }

    public static boolean isSubnetOf(String subnet, String supernet) {
        try {
            if (subnet.contains(":") && supernet.contains(":")) {
                // IPv6 comparison
                BigInteger subnetStart = new BigInteger(1, InetAddress.getByName(subnet.split("/")[0]).getAddress());
                BigInteger subnetEnd = subnetStart.add(BigInteger.ONE.shiftLeft(128 - Integer.parseInt(subnet.split("/")[1])).subtract(BigInteger.ONE));
                BigInteger supernetStart = new BigInteger(1, InetAddress.getByName(supernet.split("/")[0]).getAddress());
                BigInteger supernetEnd = supernetStart.add(BigInteger.ONE.shiftLeft(128 - Integer.parseInt(supernet.split("/")[1])).subtract(BigInteger.ONE));
                return subnetStart.compareTo(supernetStart) >= 0 && subnetEnd.compareTo(supernetEnd) <= 0;
            } else {
                SubnetUtils subnetUtils = new SubnetUtils(subnet);
                subnetUtils.setInclusiveHostCount(true); // 包含网络地址和广播地址
                SubnetUtils.SubnetInfo subnetInfo = subnetUtils.getInfo();

                SubnetUtils supernetUtils = new SubnetUtils(supernet);
                supernetUtils.setInclusiveHostCount(true); // 包含网络地址和广播地址
                SubnetUtils.SubnetInfo supernetInfo = supernetUtils.getInfo();

                // 获取子网的网络地址和广播地址
                long subnetNetworkAddress = ipToLong(subnetInfo.getNetworkAddress());
                long subnetBroadcastAddress = ipToLong(subnetInfo.getBroadcastAddress());

                // 获取父网段的网络地址和广播地址
                long supernetNetworkAddress = ipToLong(supernetInfo.getNetworkAddress());
                long supernetBroadcastAddress = ipToLong(supernetInfo.getBroadcastAddress());

                // 检查子网的网络地址是否大于等于父网段的网络地址
                // 并且子网的广播地址是否小于等于父网段的广播地址
                return subnetNetworkAddress >= supernetNetworkAddress &&
                        subnetBroadcastAddress <= supernetBroadcastAddress;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public void addCmdbRelation(String type, String relationType, List<String> instanceId, List<String> relationInstanceId) {
        log.info("新增cmdb关系类型：{}，实例ID：{}，关系类型：{}，关系实例ID：{}", type, instanceId, relationType, relationInstanceId);
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("instance_ids", instanceId);
        requestMap.put("related_instance_ids", relationInstanceId);
        CmdbCommonRep<Map<String, Object>> commonRep = cmdbClient.addRelation(type, relationType, requestMap);
        log.info("新增cmdb关系出参：{}", commonRep);
        if (commonRep.getCode().equals("0")) {
            log.info("新增cmdb关系成功");
        } else {
            log.info("新增cmdb关系失败");
        }
    }

}
