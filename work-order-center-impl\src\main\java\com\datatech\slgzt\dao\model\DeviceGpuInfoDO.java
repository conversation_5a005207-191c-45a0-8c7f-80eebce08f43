package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("WOC_DEVICE_PHYSICAL")
public class DeviceGpuInfoDO  {

    @TableId(value = "ID", type = IdType.ID_WORKER)
    private Long id;

//====================基本信息=======================
    /**
     * 云类型名称
     */
    @TableField("CATALOGUE_DOMAIN_NAME")
    private String catalogueDomainName;

    /**
     * 云类型编码
     */
    @TableField("CATALOGUE_DOMAIN_CODE")
    private String catalogueDomainCode;

    /**
     * 云平台名称
     */
    @TableField("DOMAIN_NAME")
    private String domainName;

    /**
     * 云平台编码
     */
    @TableField("DOMAIN_CODE")
    private String domainCode;

    /**
     * 资源池id
     * 当工单是线下开通的时候，资源池id是空的
     */
    @TableField("REGION_ID")
    private Long regionId;

    /**
     * 资源池Code
     */
    @TableField("REGION_CODE")
    private String regionCode;

    /**
     * 资源池名称
     */
    @TableField("REGION_NAME")
    private String regionName;


    /**
     * 业务系统ID
     */
    @TableField("BUSINESS_SYSTEM_ID")
    private Long businessSystemId;

    /**
     * 业务系统名称
     */
    @TableField("BUSINESS_SYSTEM_NAME")
    private String businessSystemName;


//====================设备基本信息=======================
    /**
     * GPU/NPU卡序列号/uuid
     */
    @TableField("DEVICE_ID")
    private String deviceId;

    /**
     * GPU/NPU卡型号
     */
    @TableField("DEVICE_TYPE")
    private String deviceType;

    /**
     * 显卡索引
     */
    @TableField("DEVICE_INDEX")
    private Integer deviceIndex;

    /**
     * 显卡名称
     */
    @TableField("MODEL_NAME")
    private String modelName;

    /**
     * 显卡节点IP
     */
    @TableField("DRIVER_IP")
    private String deviceIp;


    @TableField("MEMORY")
    private Integer memory;



    /**
     *  DCN地址
     */
    @TableField("DCN_NET_ADDR")
    private String dcnNetAddr;

    /**
     *  虚机/物理机私网地址
     */
    @TableField("PRIVATE_NET_ADDR")
    private String privateNetAddr;

    /**
     * 设备分配状态
     */
    @TableField("IN_USED")
    private String inUsed;

    /**
     * vmid
     */
    @TableField("VM_ID")
    private String vmId;
    /**
     * 区域编码
     */
    @TableField("AREA_CODE")
    private String areaCode;



    @TableField("CREATED_AT")
    private LocalDateTime createdAt;


    //最新的指标数据 json
    @TableField("LAST_PERIOD")
    private String lastPeriod;

    //来源类型
    @TableField("SOURCE_TYPE")
    private String sourceType;


    //切片状态
    @TableField("SLICE_STATUS")
    private String sliceStatus;


    @TableField("DEPT_NAME")
    private String deptName;

    @TableField("SUB_MODEL_NAME")
    private String subModelName;

    //采集状态
    @TableField("COLLECT_STATUS")
    private String collectStatus;

    //运行状态
    @TableField("RUN_STATUS")
    private String runStatus;


}
