package com.datatech.slgzt.model.dto;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 活跃统计DTO
 */
@Data
public class ActiveStatisticsDTO {
    
    /**
     * ID
     */
    private String id;
    
    /**
     * 统计日期
     */
    private LocalDate statDate;

    /**
     * 统计时间（格式：yyyyMMddHH）
     */
    private String statTime;
    
    /**
     * 活跃用户数
     */
    private Long activeUserCount;

    /**
     * 活跃用户数（字符串格式）bitset
     */
    private String activeUserCountV;

    /**
     * 用户登录数
     */
    private Long userLoginCount;

    /**
     * 点击量
     */
    private Long clickCount;
    
    /**
     * API访问量
     */
    private Long apiAccessCount;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;
}