package com.datatech.slgzt.model.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 虚拟网卡DTO
 */
@Data
public class VnicDTO {
    
    /**
     * ID
     */
    private String id;
    
    /**
     * 虚拟网卡ID
     */
    private String vnicId;
    
    /**
     * 虚拟网卡名称
     */
    private String vnicName;
    
    /**
     * 业务系统名称
     */
    private String businessSystemName;
    
    /**
     * 业务系统Id
     */
    private String businessSystemId;
    
    /**
     * 云类型名称
     */
    private String catalogueDomainName;
    
    /**
     * 云类型编码
     */
    private String catalogueDomainCode;
    
    /**
     * 云平台名称
     */
    private String domainName;
    
    /**
     * 云平台编码
     */
    private String domainCode;
    
    /**
     * 资源池名称
     */
    private String regionName;

    /**
     * 资源池code
     */
    private String regionCode;
    
    /**
     * 资源池ID
     */
    private String regionId;
    
    /**
     * 可用区名称
     */
    private String azName;

    /**
     * 可用区code
     */
    private String azCode;
    
    /**
     * 可用区ID
     */
    private String azId;
    
    /**
     * VPC名称
     */
    private String vpcName;
    
    /**
     * VPC ID
     */
    private String vpcId;
    
    /**
     * 子网名称
     */
    private String subnetName;
    
    /**
     * 子网ID
     */
    private String subnetId;
    
    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * ipv6子网ID
     */
    private String ipv6SubnetId;

    /**
     * ipv6子网名称
     */
    private String ipv6SubnetName;

    /**
     * ipv6IP地址
     */
    private String ipV6IpAddress;
    
    /**
     * 云主机名称
     */
    private String vmName;
    
    /**
     * 云主机ID
     */
    private String vmId;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 描述
     */
    private String description;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 来源
     */
    private String sourceType;

    /**
     * 租户名称
     */
    private String tenantName;
} 