package com.datatech.slgzt.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datatech.slgzt.dao.model.DeviceGpuInfoDO;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface DeviceGpuInfoMapper extends BaseMapper<DeviceGpuInfoDO> {


    @Select("SELECT DISTINCT model_name FROM WOC_DEVICE_PHYSICAL")
    List<String> groupModelName();

    @Select("SELECT DISTINCT DEPT_NAME FROM WOC_DEVICE_PHYSICAL WHERE DEPT_NAME IS NOT NULL")
    List<String> groupDeptName();

    @Select("SELECT DISTINCT BUSINESS_SYSTEM_NAME FROM WOC_DEVICE_PHYSICAL WHERE BUSINESS_SYSTEM_NAME IS NOT NULL")
    List<String> groupBusinessSystemName();
}
