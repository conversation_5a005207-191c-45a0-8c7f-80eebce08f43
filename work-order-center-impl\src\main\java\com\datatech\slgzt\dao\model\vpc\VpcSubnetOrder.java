package com.datatech.slgzt.dao.model.vpc;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("WOC_VPC_SUBNET_ORDER")
public class VpcSubnetOrder implements Serializable {
    private static final long serialVersionUID = 599388800911149645L;

    @TableId(value = "ID", type = IdType.INPUT)
    private String id;

    @TableField("CREATED_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdTime;

    @TableField("UPDATED_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedTime;

    @TableField("SUBNET_NAME")
    private String subnetName;
    @TableField("VPC_ID")
    private String vpcId;
    @TableField("AZ_CODE")
    private String azCode;
    @TableField("DNS_NAMES")
    private String dnsNames;

    @TableField("CIDR")
    private String cidr;
    @TableField("START_IP")
    private String startIp;
    @TableField("END_IP")
    private String endIp;
    @TableField("DELETED")
    private Integer deleted;
    @TableField("NETMASK")
    private String netmask;
    @TableField("DESCRIPTION")
    private String description;

    @TableField("RECOVERY_STATUS")
    private Integer recoveryStatus;

    @TableField("MESSAGE")
    private String message;

    @TableField("LEVEL2_INSTANCE_ID")
    private String level2InstanceId;
    @TableField("INSTANCE_ID")
    private String instanceId;
    @TableField("STATUS")
    private String status;
    @TableField("UUID")
    private String uuid;
    @TableField("IPV6_ENABLE")
    private Boolean ipv6Enable;

    @TableField("ALLOCATION_POOLS")
    private String allocationPools;
}
