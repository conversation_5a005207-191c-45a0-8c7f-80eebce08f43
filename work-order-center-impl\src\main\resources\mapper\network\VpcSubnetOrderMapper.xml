<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datatech.slgzt.dao.mapper.network.VpcSubnetOrderMapper">

    <resultMap type="com.datatech.slgzt.dao.model.vpc.VpcSubnetOrder" id="BaseResultMap">
        <result property="id" column="ID"/>
        <result property="createdTime" column="CREATED_TIME"/>
        <result property="updatedTime" column="UPDATED_TIME"/>
        <result property="subnetName" column="SUBNET_NAME"/>
        <result property="vpcId" column="VPC_ID"/>
        <result property="azCode" column="AZ_CODE"/>
        <result property="dnsNames" column="DNS_NAMES"/>
        <result property="cidr" column="CIDR"/>
        <result property="startIp" column="START_IP"/>
        <result property="endIp" column="END_IP"/>
        <result property="deleted" column="DELETED"/>
        <result property="netmask" column="NETMASK"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="recoveryStatus" column="RECOVERY_STATUS"/>
        <result property="message" column="MESSAGE"/>
        <result property="level2InstanceId" column="LEVEL2_INSTANCE_ID"/>
        <result property="instanceId" column="INSTANCE_ID"/>
        <result property="status" column="STATUS"/>
        <result property="uuid" column="UUID"/>
    </resultMap>


    <insert id="batchInsertVpcSubnet">
        INSERT ALL
        <foreach collection="list" item="item">
            INTO WOC_VPC_SUBNET_ORDER (ID, CREATED_TIME, SUBNET_NAME,VPC_ID,AZ_CODE,DNS_NAMES,CIDR,START_IP,END_IP,DELETED,NETMASK,
            LEVEL2_INSTANCE_ID,INSTANCE_ID,DESCRIPTION,STATUS,UUID,ALLOCATION_POOLS)
            VALUES (#{item.id},#{item.createdTime},#{item.subnetName},#{item.vpcId},#{item.azCode},#{item.dnsNames},
            #{item.cidr},#{item.startIp},#{item.endIp},#{item.deleted},#{item.netmask},
            #{item.level2InstanceId},#{item.instanceId},#{item.description},#{item.status},#{item.uuid},#{item.allocationPools})
        </foreach>
        SELECT 1 FROM DUAL
    </insert>


    <delete id="deleteByVpcId">
        DELETE FROM WOC_VPC_SUBNET_ORDER WHERE VPC_ID = #{vpcId}
    </delete>

    <select id="selectByIds" resultType="com.datatech.slgzt.dao.model.vpc.VpcSubnetOrder">
        SELECT *
        FROM WOC_VPC_SUBNET_ORDER
        WHERE ID in
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>

    <select id="selectByVpcId" resultType="com.datatech.slgzt.dao.model.vpc.VpcSubnetOrder">
        select * from WOC_VPC_SUBNET_ORDER where VPC_ID = #{vpcId}
    </select>

    <select id="selectByVpcIdSuccess" resultType="com.datatech.slgzt.dao.model.vpc.VpcSubnetOrder">
        select * from WOC_VPC_SUBNET_ORDER where VPC_ID = #{vpcId} and STATUS = 'SUCCESS' AND DELETED = 1
    </select>

    <update id="updateRecoveryStatusById">
        update
        WOC_VPC_SUBNET_ORDER
        set
        UPDATED_TIME = SYSDATE,
        RECOVERY_STATUS = #{recoveryStatus},
        DELETED = #{deleted}
        <if test="message != null and message != ''">
            , MESSAGE = #{message}
        </if>
        WHERE
        ID = #{id}
    </update>

    <update id="updateRecoveryStatus">
        update
        WOC_VPC_SUBNET_ORDER
        set
        RECOVERY_STATUS = #{recoveryStatus},
        DELETED = #{deleted}
        WHERE
        ID = #{id}
    </update>

    <select id="selectCommonById" resultType="com.datatech.slgzt.dao.model.network.SubnetCommonDo">
        SELECT id, subnet_name, vpc_id as networkId, cidr, level2_instance_id, instance_id
        FROM WOC_VPC_SUBNET_ORDER
        WHERE ID = #{id}
    </select>

    <select id="selectSubnetByVpcId" resultType="com.datatech.slgzt.dao.model.network.SubnetCommonDo">
        select s.id, s.subnet_name, s.vpc_id as networkId, s.cidr, s.level2_instance_id, s.instance_id,v.REGION_CODE
        from WOC_VPC_SUBNET_ORDER s
        left join WOC_VPC_ORDER v on v.ID = s.VPC_ID
        where s.vpc_id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateStatusById" parameterType="com.datatech.slgzt.model.dto.network.StatusDTO">
        UPDATE WOC_VPC_SUBNET_ORDER
        SET STATUS = #{status}
        <if test="message != null and message != ''">
            , MESSAGE = #{message}
        </if>
        WHERE ID = #{id} AND STATUS = #{currentStatus}
    </update>

    <update id="updateStatusByVpcId" parameterType="com.datatech.slgzt.model.dto.network.StatusDTO">
        UPDATE WOC_VPC_SUBNET_ORDER
        SET STATUS = #{status}
        WHERE VPC_ID = #{id}
    </update>

    <select id="selectCountByVpcIdSuccess" resultType="integer">
        select count(1) from WOC_VPC_SUBNET_ORDER where VPC_ID = #{vpcId} and STATUS = 'SUCCESS' AND DELETED = 1
    </select>
</mapper>