package com.datatech.slgzt.model.opm;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月24日 21:49:24
 */
@Data
public class RecoveryWorkOrderCreateOpm {

    // 工单id
    private String id;

    //工单标题
    private String orderTitle;

    //所属部门名称
    private String departmentName;

    /**
     * 厂家名称
     */
    private String manufacturer;

    /**
     * 厂家联系人
     */
    private String manufacturerContacts;

    /**
     * 厂家联系电话
     */
    private String manufacturerMobile;

    /**
     * 三级业务部门领导名称
     */
    private String levelThreeLeaderName;

    /**
     * 三级业务部门领导Id
     */
    private Long levelThreeLeaderId;

    /**
     * 业务系统id
     */
    private Long businessSystemId;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

    /**
     * 模块ID
     */
    private Long moduleId;

    /*
     * 所属业务模块 to
     */
    private String moduleName;

    /**
     * 订单描述
     */
    private String orderDesc;


    private String billId;

    private String customNo;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 业务系统编码
     */
    private String businessSystemCode;


    //这里的id都给资源表的goodsOrderId好了

    //回收ecsId列表
    private List<Long> ecsIdList;

    //回收gcsId列表
    private List<Long> gcsIdList;

    //回收mysqlId列表
    private List<Long> mysqlIdList;

    //回收postgreSqlId列表
    private List<Long> postgreSqlIdList;

    //回收redisId列表
    private List<Long> redisIdList;

    //回收evs列表
    private List<Long> evsIdList;

    //回收eip列表
    private List<Long> eipIdList;

    //回收nat列表
    private List<Long> natIdList;

    //回收obs列表
    private List<Long> obsIdList;

    //回收slb列表
    private List<Long> slbIdList;

    //vpc网络回收id列表
    private List<String> vpcIdList;

    //网络回收id列表
    private List<String> networkIdList;

    //回收云备份列表
    private List<Long> backupIdList;

    //回收vpn列表
    private List<Long> vpnIdList;

    //回收nas列表
    private List<Long> nasIdList;

    //回收裸金属列表
    private List<Long> pmIdList;

    //回收kafka列表
    private List<Long> kafkaIdList;

    //回收flink列表
    private List<Long> flinkIdList;

    //回收es列表
    private List<Long> esIdList;

    //回收宝兰德redis列表
    private List<Long> bldRedisIdList;

    //同步回收列表
    private List<Long> syncRecoveryIdList;

    //草稿判断
    private Boolean canDraft=false;

    /**
     * 创建者id
     */
    private Long createdBy;

    /**
     * 创建人名称
     */
    private String createdUserName;

    //创建的用户id
    private Long createUserId;

}
