package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("WOC_CMDB_IP3_STATUS")
public class CmdbIP3StatusDO {

    /**
     * cmdb实例id
     */
    // 主键
    @TableId(value = "INSTANCE_ID", type = IdType.INPUT)
    private String instanceId;

    /**
     * -2取消，-1:开通失败，0:无状态，1:开通成功
     */
    @TableField("SUBNET_OPEN_STATUS")
    private Integer subnetOpenStatus;

    /**
     * 0:无效，1:有效
     */
    private Integer status;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("UPDATE_TIME")
    private LocalDateTime updateTime;

    /**
     * 删除时间
     */
    @TableField("DELETE_TIME")
    private LocalDateTime deleteTime;
}
