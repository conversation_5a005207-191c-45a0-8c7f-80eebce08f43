package com.datatech.slgzt.impl.cmdb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.dao.CorporateOrderProductDAO;
import com.datatech.slgzt.dao.StandardWorkOrderProductDAO;
import com.datatech.slgzt.dao.mapper.VmMapper;
import com.datatech.slgzt.dao.mapper.network.NetworkIpAddressMapper;
import com.datatech.slgzt.dao.mapper.network.NetworkOrderMapper;
import com.datatech.slgzt.dao.mapper.network.NetworkSubnetOrderMapper;
import com.datatech.slgzt.dao.model.CorporateOrderProductDO;
import com.datatech.slgzt.dao.model.network.NetworkIpAdress;
import com.datatech.slgzt.dao.model.network.NetworkOrder;
import com.datatech.slgzt.dao.model.network.NetworkSubnetOrder;
import com.datatech.slgzt.dao.model.network.SubnetCommonDo;
import com.datatech.slgzt.dao.model.order.StandardWorkOrderProductDO;
import com.datatech.slgzt.enums.*;
import com.datatech.slgzt.enums.ip.IpLevelEnum;
import com.datatech.slgzt.exception.BusinessException;
import com.datatech.slgzt.handle.CmdbComponentClient;
import com.datatech.slgzt.handle.network.NetworkHandle;
import com.datatech.slgzt.manager.ChangeWorkOrderProductManager;
import com.datatech.slgzt.manager.RegionManager;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.model.change.ChangeEvsModel;
import com.datatech.slgzt.model.cmdb.CmdbCommonRep;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.nostander.ObsModel;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.model.vo.network.ip.IpUpdateStatusVO;
import com.datatech.slgzt.service.cmdb.CmdbReportService;
import com.datatech.slgzt.service.network.NetworkCollectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CmdbReportServiceImpl implements CmdbReportService {

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private VmMapper vmMapper;

    @Resource
    private CmdbComponentClient cmdbComponentClient;

    @Resource
    private StandardWorkOrderProductDAO standardWorkOrderProductDAO;
    @Resource
    private CorporateOrderProductDAO corporateOrderProductDAO;


    @Resource
    private NetworkSubnetOrderMapper networkSubnetOrderMapper;

    @Resource
    private NetworkIpAddressMapper networkIpAddressMapper;
    @Resource
    private NetworkHandle networkHandle;

    @Resource
    private NetworkCollectService networkService;

    @Autowired
    private NetworkOrderMapper networkOrderMapper;

    @Resource
    private RegionManager regionManager;

    @Resource
    private ChangeWorkOrderProductManager changeProductManager;

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    @Override
    public void createInstance(String orderId) {
        log.info("createInstance start orderId = {}", orderId);
        createInstanceEcs(orderId);
        createInstanceGcs(orderId);
        createInstanceMysql(orderId);
        createInstancePostgreSql(orderId);
        createInstanceRedis(orderId);
        createInstanceObs(orderId);
        log.info("createInstance end orderId = {}", orderId);
    }

    @Override
    public void createInstanceOfCorporate(String orderId) {
        log.info("createInstanceOfCorporate start orderId = {}", orderId);
        createInstanceEcsOfCorporate(orderId);
        createInstanceGcsOfCorporate(orderId);
        createInstanceMysqlOfCorporate(orderId);
        createInstanceRedisOfCorporate(orderId);
        createInstanceObsOfCorporate(orderId);
        log.info("createInstanceOfCorporate end orderId = {}", orderId);
    }

    @Override
    public void deleteIp(String networkId) {
        log.info("deleteIp start networkId = {}", networkId);
        deleteIpv3Detail(networkId);
        deleteRelationNetwork(networkId);
        deleteIpv3(networkId);
        log.info("deleteIp end networkId = {}", networkId);
    }


    private void createInstanceEcs(String orderId) {
        ResourceDetailQuery query = new ResourceDetailQuery();
        query.setType(ProductTypeEnum.ECS.getCode());
        query.setOrderId(orderId);
        List<ResourceDetailDTO> productVoList = resourceDetailManager.list(query);
        productVoList = productVoList.stream()
                                     .filter(productVo -> StringUtils.isBlank(productVo.getConfigId()))
                                     .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productVoList)) {
            addRelation(CmdbModelTypeEnum.VM.getCode(), orderId, ProductTypeEnum.ECS);
            return;
        }
        log.info("createInstance ecs start orderId = {}", orderId);
        Map<String, ResourceDetailDTO> productMap = productVoList.stream()
                                                                 .collect(Collectors.toMap(ResourceDetailDTO::getDeviceId, product -> product));
        List<String> deviceIds = productVoList.stream()
                                              .map(ResourceDetailDTO::getDeviceId)
                                              .collect(Collectors.toList());
        List<VirtualMachineDTO> virtualMachineList = vmMapper.getByIdList(deviceIds);

        for (VirtualMachineDTO virtualMachine : virtualMachineList) {
            ResourceDetailDTO resourceDetailDO = productMap.get(virtualMachine.getId());
            Map<String, Object> map = new HashMap<>();
            map.put("CLOUD", virtualMachine.getCloud());
            String status = "关机";
            if ("RUNING".equals(virtualMachine.getStatus())) {
                status = "开机";
            } else if ("SHUTOFF".equals(virtualMachine.getStatus())) {
                status = "关机";
            }
            map.put("device_status", status);
            map.put("OS_VERSION", virtualMachine.getImagesName());
            map.put("RELATED_POOL", virtualMachine.getCmdbName());
            map.put("name", resourceDetailDO.getDeviceName());
            map.put("vcpu_info", virtualMachine.getVcpus());
            map.put("MEM_TOTAL_CAPACITY", virtualMachine.getRam() / 1024);
            List<Volume> volumeList = vmMapper.getVolume(virtualMachine.getId());
            long vDisks = volumeList.stream()
                                    .filter(volume -> volume.getVolumeSize() != null)
                                    .mapToLong(Volume::getVolumeSize)
                                    .sum();
            map.put("VDISKS_INFO", vDisks);
            List<IpAddress> ipList = vmMapper.getIpAddress(virtualMachine.getId(), "ECS");
            String ipIpv4 = "";
            String ipIpv6 = "";
            if (CollectionUtils.isNotEmpty(ipList)) {
                for (IpAddress ipAddress : ipList) {
                    if (ipAddress.getType().equals("EIP")) {
                        continue;
                    }
                    if (ipAddress.getType().equals("MANAGE")) {
                        if (ipAddress.getIp().length() <= 18 && ipAddress.getIp().contains(".")) {
                            ipIpv4 = ipAddress.getIp();
                        } else {
                            ipIpv6 = ipAddress.getIp();
                        }
                    } else {
                        if (StringUtils.isEmpty(ipIpv4) && ipAddress.getIp().length() <= 18 && ipAddress.getIp()
                                                                                                        .contains(".")) {
                            ipIpv4 = ipAddress.getIp();
                        }
                        if (StringUtils.isEmpty(ipIpv6) && ipAddress.getIp().contains(":")) {
                            ipIpv6 = ipAddress.getIp();
                        }
                    }
                }
                if (StringUtils.isNotBlank(ipIpv4)) {
                    map.put("MGMT_IPV4", ipIpv4);
                }
                if (StringUtils.isNotBlank(ipIpv6)) {
                    map.put("MGMT_IPV6", ipIpv6);
                }
                List<String> ip = ipList.stream().map(IpAddress::getIp).collect(Collectors.toList());
                String otherIps = String.join(",", ip);
                map.put("other_ips", otherIps);
            }

            map.put("MAINTENANCE_HANDOVER", "否(未入网)");
            map.put("UUID", virtualMachine.getInstanceUuid());
            log.info("createInstance ecs 入参 : {}", JSONObject.toJSONString(map));
            CmdbCommonRep<Map<String, Object>> cmdbCommonRep = cmdbComponentClient.insertCmdbData(CmdbModelTypeEnum.VM.getCode(), map);
            if (!"0".equals(cmdbCommonRep.getCode())) {
                log.error("入网交维失败：createInstance ecs err : {}", JSONObject.toJSONString(cmdbCommonRep));
                throw new BusinessException(BusinessExceptionEnum.CMDB_INSTANCE_FAILED.code, BusinessExceptionEnum.CMDB_INSTANCE_FAILED.message);
            }
            log.info("createInstance ecs 返回结果 : {}", JSONObject.toJSONString(cmdbCommonRep));
            Map<String, Object> data = cmdbCommonRep.getData();
            String instanceId = data.get("instanceId").toString();
            resourceDetailManager.updateConfigId(instanceId, resourceDetailDO.getId(), ipIpv4);
            createIpv3(virtualMachine, ipList);
        }
        addRelation(CmdbModelTypeEnum.VM.getCode(), orderId, ProductTypeEnum.ECS);
        log.info("createInstance ecs end orderId = {}", orderId);
    }

    private void createInstanceEcsOfCorporate(String orderId) {
        ResourceDetailQuery query = new ResourceDetailQuery();
        query.setType(ProductTypeEnum.ECS.getCode());
        query.setOrderId(orderId);
        List<ResourceDetailDTO> productVoList = resourceDetailManager.listCorporate(query);
        productVoList = productVoList.stream()
                .filter(productVo -> StringUtils.isBlank(productVo.getConfigId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productVoList)) {
            addRelation(CmdbModelTypeEnum.VM.getCode(), orderId, ProductTypeEnum.ECS);
            return;
        }
        log.info("createInstance ecs start orderId = {}", orderId);
        Map<String, ResourceDetailDTO> productMap = productVoList.stream()
                .collect(Collectors.toMap(ResourceDetailDTO::getDeviceId, product -> product));
        List<String> deviceIds = productVoList.stream()
                .map(ResourceDetailDTO::getDeviceId)
                .collect(Collectors.toList());
        List<VirtualMachineDTO> virtualMachineList = vmMapper.getByIdList(deviceIds);

        for (VirtualMachineDTO virtualMachine : virtualMachineList) {
            ResourceDetailDTO resourceDetailDO = productMap.get(virtualMachine.getId());
            Map<String, Object> map = new HashMap<>();
            map.put("CLOUD", virtualMachine.getCloud());
            String status = "关机";
            if ("RUNING".equals(virtualMachine.getStatus())) {
                status = "开机";
            } else if ("SHUTOFF".equals(virtualMachine.getStatus())) {
                status = "关机";
            }
            map.put("device_status", status);
            map.put("OS_VERSION", virtualMachine.getImagesName());
            map.put("RELATED_POOL", virtualMachine.getCmdbName());
            map.put("name", resourceDetailDO.getDeviceName());
            map.put("vcpu_info", virtualMachine.getVcpus());
            map.put("MEM_TOTAL_CAPACITY", virtualMachine.getRam() / 1024);
            List<Volume> volumeList = vmMapper.getVolume(virtualMachine.getId());
            long vDisks = volumeList.stream()
                    .filter(volume -> volume.getVolumeSize() != null)
                    .mapToLong(Volume::getVolumeSize)
                    .sum();
            map.put("VDISKS_INFO", vDisks);
            List<IpAddress> ipList = vmMapper.getIpAddress(virtualMachine.getId(), "ECS");
            String ipIpv4 = "";
            String ipIpv6 = "";
            if (CollectionUtils.isNotEmpty(ipList)) {
                for (IpAddress ipAddress : ipList) {
                    if (ipAddress.getType().equals("EIP")) {
                        continue;
                    }
                    if (ipAddress.getType().equals("MANAGE")) {
                        if (ipAddress.getIp().length() <= 18 && ipAddress.getIp().contains(".")) {
                            ipIpv4 = ipAddress.getIp();
                        } else {
                            ipIpv6 = ipAddress.getIp();
                        }
                    } else {
                        if (StringUtils.isEmpty(ipIpv4) && ipAddress.getIp().length() <= 18 && ipAddress.getIp()
                                .contains(".")) {
                            ipIpv4 = ipAddress.getIp();
                        }
                        if (StringUtils.isEmpty(ipIpv6) && ipAddress.getIp().contains(":")) {
                            ipIpv6 = ipAddress.getIp();
                        }
                    }
                }
                if (StringUtils.isNotBlank(ipIpv4)) {
                    map.put("MGMT_IPV4", ipIpv4);
                }
                if (StringUtils.isNotBlank(ipIpv6)) {
                    map.put("MGMT_IPV6", ipIpv6);
                }
                List<String> ip = ipList.stream().map(IpAddress::getIp).collect(Collectors.toList());
                String otherIps = String.join(",", ip);
                map.put("other_ips", otherIps);
            }

            map.put("MAINTENANCE_HANDOVER", "否(未入网)");
            map.put("UUID", virtualMachine.getInstanceUuid());
            log.info("createInstance ecs 入参 : {}", JSONObject.toJSONString(map));
            CmdbCommonRep<Map<String, Object>> cmdbCommonRep = cmdbComponentClient.insertCmdbData(CmdbModelTypeEnum.VM.getCode(), map);
            if (!"0".equals(cmdbCommonRep.getCode())) {
                log.error("入网交维失败：createInstance ecs err : {}", JSONObject.toJSONString(cmdbCommonRep));
                throw new BusinessException(BusinessExceptionEnum.CMDB_INSTANCE_FAILED.code, BusinessExceptionEnum.CMDB_INSTANCE_FAILED.message);
            }
            log.info("createInstance ecs 返回结果 : {}", JSONObject.toJSONString(cmdbCommonRep));
            Map<String, Object> data = cmdbCommonRep.getData();
            String instanceId = data.get("instanceId").toString();
            resourceDetailManager.updateConfigId(instanceId, resourceDetailDO.getId(), ipIpv4);
            createIpv3(virtualMachine, ipList);
        }
        addRelationOfCorporate(CmdbModelTypeEnum.VM.getCode(), orderId, ProductTypeEnum.ECS);
        log.info("createInstance ecs end orderId = {}", orderId);
    }

    private void createInstanceMysql(String orderId) {
        ResourceDetailQuery query = new ResourceDetailQuery();
        query.setType(ProductTypeEnum.MYSQL.getCode());
        query.setOrderId(orderId);
        List<ResourceDetailDTO> productVoList = resourceDetailManager.list(query);
        productVoList = productVoList.stream()
                                     .filter(productVo -> StringUtils.isBlank(productVo.getConfigId()))
                                     .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productVoList)) {
            addRelation(CmdbModelTypeEnum.VM.getCode(), orderId, ProductTypeEnum.MYSQL);
            return;
        }
        log.info("createInstance ecs start orderId = {}", orderId);
        Map<String, ResourceDetailDTO> productMap = productVoList.stream()
                                                                 .collect(Collectors.toMap(ResourceDetailDTO::getDeviceId, product -> product));
        List<String> deviceIds = productVoList.stream()
                                              .map(ResourceDetailDTO::getDeviceId)
                                              .collect(Collectors.toList());
        List<VirtualMachineDTO> virtualMachineList = vmMapper.getByIdList(deviceIds);

        for (VirtualMachineDTO virtualMachine : virtualMachineList) {
            ResourceDetailDTO resourceDetailDO = productMap.get(virtualMachine.getId());
            Map<String, Object> map = new HashMap<>();
            map.put("CLOUD", virtualMachine.getCloud());
            String status = "关机";
            if ("RUNING".equals(virtualMachine.getStatus())) {
                status = "开机";
            } else if ("SHUTOFF".equals(virtualMachine.getStatus())) {
                status = "关机";
            }
            map.put("device_status", status);
            map.put("OS_VERSION", virtualMachine.getImagesName());
            map.put("RELATED_POOL", virtualMachine.getCmdbName());
            map.put("name", resourceDetailDO.getDeviceName());
            map.put("vcpu_info", virtualMachine.getVcpus());
            map.put("MEM_TOTAL_CAPACITY", virtualMachine.getRam() / 1024);
            List<Volume> volumeList = vmMapper.getVolume(virtualMachine.getId());
            long vDisks = volumeList.stream()
                                    .filter(volume -> volume.getVolumeSize() != null)
                                    .mapToLong(Volume::getVolumeSize)
                                    .sum();
            map.put("VDISKS_INFO", vDisks);
            List<IpAddress> ipList = vmMapper.getIpAddress(virtualMachine.getId(), "ECS");
            String ipIpv4 = "";
            String ipIpv6 = "";
            if (CollectionUtils.isNotEmpty(ipList)) {
                for (IpAddress ipAddress : ipList) {
                    if (ipAddress.getType().equals("EIP")) {
                        continue;
                    }
                    if (ipAddress.getType().equals("MANAGE")) {
                        if (ipAddress.getIp().length() <= 18 && ipAddress.getIp().contains(".")) {
                            ipIpv4 = ipAddress.getIp();
                        } else {
                            ipIpv6 = ipAddress.getIp();
                        }
                    } else {
                        if (StringUtils.isEmpty(ipIpv4) && ipAddress.getIp().length() <= 18 && ipAddress.getIp()
                                                                                                        .contains(".")) {
                            ipIpv4 = ipAddress.getIp();
                        }
                        if (StringUtils.isEmpty(ipIpv6) && ipAddress.getIp().contains(":")) {
                            ipIpv6 = ipAddress.getIp();
                        }
                    }
                }
                if (StringUtils.isNotBlank(ipIpv4)) {
                    map.put("MGMT_IPV4", ipIpv4);
                }
                if (StringUtils.isNotBlank(ipIpv6)) {
                    map.put("MGMT_IPV6", ipIpv6);
                }
                List<String> ip = ipList.stream().map(IpAddress::getIp).collect(Collectors.toList());
                String otherIps = String.join(",", ip);
                map.put("other_ips", otherIps);
            }

            map.put("MAINTENANCE_HANDOVER", "否(未入网)");
            map.put("UUID", virtualMachine.getInstanceUuid());
            log.info("createInstance ecs 入参 : {}", JSONObject.toJSONString(map));
            CmdbCommonRep<Map<String, Object>> cmdbCommonRep = cmdbComponentClient.insertCmdbData(CmdbModelTypeEnum.VM.getCode(), map);
            if (!"0".equals(cmdbCommonRep.getCode())) {
                log.error("入网交维失败：createInstance ecs err : {}", JSONObject.toJSONString(cmdbCommonRep));
                throw new BusinessException(BusinessExceptionEnum.CMDB_INSTANCE_FAILED.code, BusinessExceptionEnum.CMDB_INSTANCE_FAILED.message);
            }
            log.info("createInstance ecs 返回结果 : {}", JSONObject.toJSONString(cmdbCommonRep));
            Map<String, Object> data = cmdbCommonRep.getData();
            String instanceId = data.get("instanceId").toString();
            resourceDetailManager.updateConfigId(instanceId, resourceDetailDO.getId(), ipIpv4);
            createIpv3(virtualMachine, ipList);
        }
        addRelation(CmdbModelTypeEnum.VM.getCode(), orderId, ProductTypeEnum.MYSQL);
        log.info("createInstance ecs end orderId = {}", orderId);
    }

    private void createInstancePostgreSql(String orderId) {
        ResourceDetailQuery query = new ResourceDetailQuery();
        query.setType(ProductTypeEnum.POSTGRESQL.getCode());
        query.setOrderId(orderId);
        List<ResourceDetailDTO> productVoList = resourceDetailManager.list(query);
        productVoList = productVoList.stream()
                .filter(productVo -> StringUtils.isBlank(productVo.getConfigId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productVoList)) {
            addRelation(CmdbModelTypeEnum.VM.getCode(), orderId, ProductTypeEnum.POSTGRESQL);
            return;
        }
        log.info("createInstance PostgreSql start orderId = {}", orderId);
        Map<String, ResourceDetailDTO> productMap = productVoList.stream()
                .collect(Collectors.toMap(ResourceDetailDTO::getDeviceId, product -> product));
        List<String> deviceIds = productVoList.stream()
                .map(ResourceDetailDTO::getDeviceId)
                .collect(Collectors.toList());
        List<VirtualMachineDTO> virtualMachineList = vmMapper.getByIdList(deviceIds);

        for (VirtualMachineDTO virtualMachine : virtualMachineList) {
            ResourceDetailDTO resourceDetailDO = productMap.get(virtualMachine.getId());
            Map<String, Object> map = new HashMap<>();
            map.put("CLOUD", virtualMachine.getCloud());
            String status = "关机";
            if ("RUNING".equals(virtualMachine.getStatus())) {
                status = "开机";
            } else if ("SHUTOFF".equals(virtualMachine.getStatus())) {
                status = "关机";
            }
            map.put("device_status", status);
            map.put("OS_VERSION", virtualMachine.getImagesName());
            map.put("RELATED_POOL", virtualMachine.getCmdbName());
            map.put("name", resourceDetailDO.getDeviceName());
            map.put("vcpu_info", virtualMachine.getVcpus());
            map.put("MEM_TOTAL_CAPACITY", virtualMachine.getRam() / 1024);
            List<Volume> volumeList = vmMapper.getVolume(virtualMachine.getId());
            long vDisks = volumeList.stream()
                    .filter(volume -> volume.getVolumeSize() != null)
                    .mapToLong(Volume::getVolumeSize)
                    .sum();
            map.put("VDISKS_INFO", vDisks);
            List<IpAddress> ipList = vmMapper.getIpAddress(virtualMachine.getId(), "ECS");
            String ipIpv4 = "";
            String ipIpv6 = "";
            if (CollectionUtils.isNotEmpty(ipList)) {
                for (IpAddress ipAddress : ipList) {
                    if (ipAddress.getType().equals("EIP")) {
                        continue;
                    }
                    if (ipAddress.getType().equals("MANAGE")) {
                        if (ipAddress.getIp().length() <= 18 && ipAddress.getIp().contains(".")) {
                            ipIpv4 = ipAddress.getIp();
                        } else {
                            ipIpv6 = ipAddress.getIp();
                        }
                    } else {
                        if (StringUtils.isEmpty(ipIpv4) && ipAddress.getIp().length() <= 18 && ipAddress.getIp()
                                .contains(".")) {
                            ipIpv4 = ipAddress.getIp();
                        }
                        if (StringUtils.isEmpty(ipIpv6) && ipAddress.getIp().contains(":")) {
                            ipIpv6 = ipAddress.getIp();
                        }
                    }
                }
                if (StringUtils.isNotBlank(ipIpv4)) {
                    map.put("MGMT_IPV4", ipIpv4);
                }
                if (StringUtils.isNotBlank(ipIpv6)) {
                    map.put("MGMT_IPV6", ipIpv6);
                }
                List<String> ip = ipList.stream().map(IpAddress::getIp).collect(Collectors.toList());
                String otherIps = String.join(",", ip);
                map.put("other_ips", otherIps);
            }

            map.put("MAINTENANCE_HANDOVER", "否(未入网)");
            map.put("UUID", virtualMachine.getInstanceUuid());
            log.info("createInstance PostgreSql 入参 : {}", JSONObject.toJSONString(map));
            CmdbCommonRep<Map<String, Object>> cmdbCommonRep = cmdbComponentClient.insertCmdbData(CmdbModelTypeEnum.VM.getCode(), map);
            if (!"0".equals(cmdbCommonRep.getCode())) {
                log.error("入网交维失败：createInstance PostgreSql err : {}", JSONObject.toJSONString(cmdbCommonRep));
                throw new BusinessException(BusinessExceptionEnum.CMDB_INSTANCE_FAILED.code, BusinessExceptionEnum.CMDB_INSTANCE_FAILED.message);
            }
            log.info("createInstance PostgreSql 返回结果 : {}", JSONObject.toJSONString(cmdbCommonRep));
            Map<String, Object> data = cmdbCommonRep.getData();
            String instanceId = data.get("instanceId").toString();
            resourceDetailManager.updateConfigId(instanceId, resourceDetailDO.getId(), ipIpv4);
            createIpv3(virtualMachine, ipList);
        }
        addRelation(CmdbModelTypeEnum.VM.getCode(), orderId, ProductTypeEnum.POSTGRESQL);
        log.info("createInstance PostgreSql end orderId = {}", orderId);
    }

    private void createInstanceMysqlOfCorporate(String orderId) {
        ResourceDetailQuery query = new ResourceDetailQuery();
        query.setType(ProductTypeEnum.MYSQL.getCode());
        query.setOrderId(orderId);
        List<ResourceDetailDTO> productVoList = resourceDetailManager.listCorporate(query);
        productVoList = productVoList.stream()
                .filter(productVo -> StringUtils.isBlank(productVo.getConfigId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productVoList)) {
            addRelation(CmdbModelTypeEnum.VM.getCode(), orderId, ProductTypeEnum.MYSQL);
            return;
        }
        log.info("createInstance ecs start orderId = {}", orderId);
        Map<String, ResourceDetailDTO> productMap = productVoList.stream()
                .collect(Collectors.toMap(ResourceDetailDTO::getDeviceId, product -> product));
        List<String> deviceIds = productVoList.stream()
                .map(ResourceDetailDTO::getDeviceId)
                .collect(Collectors.toList());
        List<VirtualMachineDTO> virtualMachineList = vmMapper.getByIdList(deviceIds);

        for (VirtualMachineDTO virtualMachine : virtualMachineList) {
            ResourceDetailDTO resourceDetailDO = productMap.get(virtualMachine.getId());
            Map<String, Object> map = new HashMap<>();
            map.put("CLOUD", virtualMachine.getCloud());
            String status = "关机";
            if ("RUNING".equals(virtualMachine.getStatus())) {
                status = "开机";
            } else if ("SHUTOFF".equals(virtualMachine.getStatus())) {
                status = "关机";
            }
            map.put("device_status", status);
            map.put("OS_VERSION", virtualMachine.getImagesName());
            map.put("RELATED_POOL", virtualMachine.getCmdbName());
            map.put("name", resourceDetailDO.getDeviceName());
            map.put("vcpu_info", virtualMachine.getVcpus());
            map.put("MEM_TOTAL_CAPACITY", virtualMachine.getRam() / 1024);
            List<Volume> volumeList = vmMapper.getVolume(virtualMachine.getId());
            long vDisks = volumeList.stream()
                    .filter(volume -> volume.getVolumeSize() != null)
                    .mapToLong(Volume::getVolumeSize)
                    .sum();
            map.put("VDISKS_INFO", vDisks);
            List<IpAddress> ipList = vmMapper.getIpAddress(virtualMachine.getId(), "ECS");
            String ipIpv4 = "";
            String ipIpv6 = "";
            if (CollectionUtils.isNotEmpty(ipList)) {
                for (IpAddress ipAddress : ipList) {
                    if (ipAddress.getType().equals("EIP")) {
                        continue;
                    }
                    if (ipAddress.getType().equals("MANAGE")) {
                        if (ipAddress.getIp().length() <= 18 && ipAddress.getIp().contains(".")) {
                            ipIpv4 = ipAddress.getIp();
                        } else {
                            ipIpv6 = ipAddress.getIp();
                        }
                    } else {
                        if (StringUtils.isEmpty(ipIpv4) && ipAddress.getIp().length() <= 18 && ipAddress.getIp()
                                .contains(".")) {
                            ipIpv4 = ipAddress.getIp();
                        }
                        if (StringUtils.isEmpty(ipIpv6) && ipAddress.getIp().contains(":")) {
                            ipIpv6 = ipAddress.getIp();
                        }
                    }
                }
                if (StringUtils.isNotBlank(ipIpv4)) {
                    map.put("MGMT_IPV4", ipIpv4);
                }
                if (StringUtils.isNotBlank(ipIpv6)) {
                    map.put("MGMT_IPV6", ipIpv6);
                }
                List<String> ip = ipList.stream().map(IpAddress::getIp).collect(Collectors.toList());
                String otherIps = String.join(",", ip);
                map.put("other_ips", otherIps);
            }

            map.put("MAINTENANCE_HANDOVER", "否(未入网)");
            map.put("UUID", virtualMachine.getInstanceUuid());
            log.info("createInstance ecs 入参 : {}", JSONObject.toJSONString(map));
            CmdbCommonRep<Map<String, Object>> cmdbCommonRep = cmdbComponentClient.insertCmdbData(CmdbModelTypeEnum.VM.getCode(), map);
            if (!"0".equals(cmdbCommonRep.getCode())) {
                log.error("入网交维失败：createInstance ecs err : {}", JSONObject.toJSONString(cmdbCommonRep));
                throw new BusinessException(BusinessExceptionEnum.CMDB_INSTANCE_FAILED.code, BusinessExceptionEnum.CMDB_INSTANCE_FAILED.message);
            }
            log.info("createInstance ecs 返回结果 : {}", JSONObject.toJSONString(cmdbCommonRep));
            Map<String, Object> data = cmdbCommonRep.getData();
            String instanceId = data.get("instanceId").toString();
            resourceDetailManager.updateConfigId(instanceId, resourceDetailDO.getId(), ipIpv4);
            createIpv3(virtualMachine, ipList);
        }
        addRelationOfCorporate(CmdbModelTypeEnum.VM.getCode(), orderId, ProductTypeEnum.MYSQL);
        log.info("createInstance ecs end orderId = {}", orderId);
    }

    private void createInstanceRedis(String orderId) {
        ResourceDetailQuery query = new ResourceDetailQuery();
        query.setType(ProductTypeEnum.REDIS.getCode());
        query.setOrderId(orderId);
        List<ResourceDetailDTO> productVoList = resourceDetailManager.list(query);
        productVoList = productVoList.stream()
                                     .filter(productVo -> StringUtils.isBlank(productVo.getConfigId()))
                                     .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productVoList)) {
            addRelation(CmdbModelTypeEnum.VM.getCode(), orderId, ProductTypeEnum.REDIS);
            return;
        }
        log.info("createInstance ecs start orderId = {}", orderId);
        Map<String, ResourceDetailDTO> productMap = productVoList.stream()
                                                                 .collect(Collectors.toMap(ResourceDetailDTO::getDeviceId, product -> product));
        List<String> deviceIds = productVoList.stream()
                                              .map(ResourceDetailDTO::getDeviceId)
                                              .collect(Collectors.toList());
        List<VirtualMachineDTO> virtualMachineList = vmMapper.getByIdList(deviceIds);

        for (VirtualMachineDTO virtualMachine : virtualMachineList) {
            ResourceDetailDTO resourceDetailDO = productMap.get(virtualMachine.getId());
            Map<String, Object> map = new HashMap<>();
            map.put("CLOUD", virtualMachine.getCloud());
            String status = "关机";
            if ("RUNING".equals(virtualMachine.getStatus())) {
                status = "开机";
            } else if ("SHUTOFF".equals(virtualMachine.getStatus())) {
                status = "关机";
            }
            map.put("device_status", status);
            map.put("OS_VERSION", virtualMachine.getImagesName());
            map.put("RELATED_POOL", virtualMachine.getCmdbName());
            map.put("name", resourceDetailDO.getDeviceName());
            map.put("vcpu_info", virtualMachine.getVcpus());
            map.put("MEM_TOTAL_CAPACITY", virtualMachine.getRam() / 1024);
            List<Volume> volumeList = vmMapper.getVolume(virtualMachine.getId());
            long vDisks = volumeList.stream()
                                    .filter(volume -> volume.getVolumeSize() != null)
                                    .mapToLong(Volume::getVolumeSize)
                                    .sum();
            map.put("VDISKS_INFO", vDisks);
            List<IpAddress> ipList = vmMapper.getIpAddress(virtualMachine.getId(), "ECS");
            String ipIpv4 = "";
            String ipIpv6 = "";
            if (CollectionUtils.isNotEmpty(ipList)) {
                for (IpAddress ipAddress : ipList) {
                    if (ipAddress.getType().equals("EIP")) {
                        continue;
                    }
                    if (ipAddress.getType().equals("MANAGE")) {
                        if (ipAddress.getIp().length() <= 18 && ipAddress.getIp().contains(".")) {
                            ipIpv4 = ipAddress.getIp();
                        } else {
                            ipIpv6 = ipAddress.getIp();
                        }
                    } else {
                        if (StringUtils.isEmpty(ipIpv4) && ipAddress.getIp().length() <= 18 && ipAddress.getIp()
                                                                                                        .contains(".")) {
                            ipIpv4 = ipAddress.getIp();
                        }
                        if (StringUtils.isEmpty(ipIpv6) && ipAddress.getIp().contains(":")) {
                            ipIpv6 = ipAddress.getIp();
                        }
                    }
                }
                if (StringUtils.isNotBlank(ipIpv4)) {
                    map.put("MGMT_IPV4", ipIpv4);
                }
                if (StringUtils.isNotBlank(ipIpv6)) {
                    map.put("MGMT_IPV6", ipIpv6);
                }
                List<String> ip = ipList.stream().map(IpAddress::getIp).collect(Collectors.toList());
                String otherIps = String.join(",", ip);
                map.put("other_ips", otherIps);
            }

            map.put("MAINTENANCE_HANDOVER", "否(未入网)");
            map.put("UUID", virtualMachine.getInstanceUuid());
            log.info("createInstance ecs 入参 : {}", JSONObject.toJSONString(map));
            CmdbCommonRep<Map<String, Object>> cmdbCommonRep = cmdbComponentClient.insertCmdbData(CmdbModelTypeEnum.VM.getCode(), map);
            if (!"0".equals(cmdbCommonRep.getCode())) {
                log.error("入网交维失败：createInstance ecs err : {}", JSONObject.toJSONString(cmdbCommonRep));
                throw new BusinessException(BusinessExceptionEnum.CMDB_INSTANCE_FAILED.code, BusinessExceptionEnum.CMDB_INSTANCE_FAILED.message);
            }
            log.info("createInstance ecs 返回结果 : {}", JSONObject.toJSONString(cmdbCommonRep));
            Map<String, Object> data = cmdbCommonRep.getData();
            String instanceId = data.get("instanceId").toString();
            resourceDetailManager.updateConfigId(instanceId, resourceDetailDO.getId(), ipIpv4);
            createIpv3(virtualMachine, ipList);
        }
        addRelation(CmdbModelTypeEnum.VM.getCode(), orderId, ProductTypeEnum.REDIS);
        log.info("createInstance ecs end orderId = {}", orderId);
    }

    private void createInstanceRedisOfCorporate(String orderId) {
        ResourceDetailQuery query = new ResourceDetailQuery();
        query.setType(ProductTypeEnum.REDIS.getCode());
        query.setOrderId(orderId);
        List<ResourceDetailDTO> productVoList = resourceDetailManager.listCorporate(query);
        productVoList = productVoList.stream()
                .filter(productVo -> StringUtils.isBlank(productVo.getConfigId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productVoList)) {
            addRelation(CmdbModelTypeEnum.VM.getCode(), orderId, ProductTypeEnum.REDIS);
            return;
        }
        log.info("createInstance ecs start orderId = {}", orderId);
        Map<String, ResourceDetailDTO> productMap = productVoList.stream()
                .collect(Collectors.toMap(ResourceDetailDTO::getDeviceId, product -> product));
        List<String> deviceIds = productVoList.stream()
                .map(ResourceDetailDTO::getDeviceId)
                .collect(Collectors.toList());
        List<VirtualMachineDTO> virtualMachineList = vmMapper.getByIdList(deviceIds);

        for (VirtualMachineDTO virtualMachine : virtualMachineList) {
            ResourceDetailDTO resourceDetailDO = productMap.get(virtualMachine.getId());
            Map<String, Object> map = new HashMap<>();
            map.put("CLOUD", virtualMachine.getCloud());
            String status = "关机";
            if ("RUNING".equals(virtualMachine.getStatus())) {
                status = "开机";
            } else if ("SHUTOFF".equals(virtualMachine.getStatus())) {
                status = "关机";
            }
            map.put("device_status", status);
            map.put("OS_VERSION", virtualMachine.getImagesName());
            map.put("RELATED_POOL", virtualMachine.getCmdbName());
            map.put("name", resourceDetailDO.getDeviceName());
            map.put("vcpu_info", virtualMachine.getVcpus());
            map.put("MEM_TOTAL_CAPACITY", virtualMachine.getRam() / 1024);
            List<Volume> volumeList = vmMapper.getVolume(virtualMachine.getId());
            long vDisks = volumeList.stream()
                    .filter(volume -> volume.getVolumeSize() != null)
                    .mapToLong(Volume::getVolumeSize)
                    .sum();
            map.put("VDISKS_INFO", vDisks);
            List<IpAddress> ipList = vmMapper.getIpAddress(virtualMachine.getId(), "ECS");
            String ipIpv4 = "";
            String ipIpv6 = "";
            if (CollectionUtils.isNotEmpty(ipList)) {
                for (IpAddress ipAddress : ipList) {
                    if (ipAddress.getType().equals("EIP")) {
                        continue;
                    }
                    if (ipAddress.getType().equals("MANAGE")) {
                        if (ipAddress.getIp().length() <= 18 && ipAddress.getIp().contains(".")) {
                            ipIpv4 = ipAddress.getIp();
                        } else {
                            ipIpv6 = ipAddress.getIp();
                        }
                    } else {
                        if (StringUtils.isEmpty(ipIpv4) && ipAddress.getIp().length() <= 18 && ipAddress.getIp()
                                .contains(".")) {
                            ipIpv4 = ipAddress.getIp();
                        }
                        if (StringUtils.isEmpty(ipIpv6) && ipAddress.getIp().contains(":")) {
                            ipIpv6 = ipAddress.getIp();
                        }
                    }
                }
                if (StringUtils.isNotBlank(ipIpv4)) {
                    map.put("MGMT_IPV4", ipIpv4);
                }
                if (StringUtils.isNotBlank(ipIpv6)) {
                    map.put("MGMT_IPV6", ipIpv6);
                }
                List<String> ip = ipList.stream().map(IpAddress::getIp).collect(Collectors.toList());
                String otherIps = String.join(",", ip);
                map.put("other_ips", otherIps);
            }

            map.put("MAINTENANCE_HANDOVER", "否(未入网)");
            map.put("UUID", virtualMachine.getInstanceUuid());
            log.info("createInstance ecs 入参 : {}", JSONObject.toJSONString(map));
            CmdbCommonRep<Map<String, Object>> cmdbCommonRep = cmdbComponentClient.insertCmdbData(CmdbModelTypeEnum.VM.getCode(), map);
            if (!"0".equals(cmdbCommonRep.getCode())) {
                log.error("入网交维失败：createInstance ecs err : {}", JSONObject.toJSONString(cmdbCommonRep));
                throw new BusinessException(BusinessExceptionEnum.CMDB_INSTANCE_FAILED.code, BusinessExceptionEnum.CMDB_INSTANCE_FAILED.message);
            }
            log.info("createInstance ecs 返回结果 : {}", JSONObject.toJSONString(cmdbCommonRep));
            Map<String, Object> data = cmdbCommonRep.getData();
            String instanceId = data.get("instanceId").toString();
            resourceDetailManager.updateConfigId(instanceId, resourceDetailDO.getId(), ipIpv4);
            createIpv3(virtualMachine, ipList);
        }
        addRelationOfCorporate(CmdbModelTypeEnum.VM.getCode(), orderId, ProductTypeEnum.REDIS);
        log.info("createInstance ecs end orderId = {}", orderId);
    }

    private void createInstanceGcs(String orderId) {
        ResourceDetailQuery query = new ResourceDetailQuery();
        query.setType(ProductTypeEnum.GCS.getCode());
        query.setOrderId(orderId);
        List<ResourceDetailDTO> productVoList = resourceDetailManager.list(query);
        productVoList = productVoList.stream()
                                     .filter(productVo -> StringUtils.isBlank(productVo.getConfigId()))
                                     .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productVoList)) {
            addRelation(CmdbModelTypeEnum.VM.getCode(), orderId, ProductTypeEnum.GCS);
            return;
        }
        log.info("createInstance gcs start orderId = {}", orderId);
        Map<String, ResourceDetailDTO> productMap = productVoList.stream()
                                                                 .collect(Collectors.toMap(ResourceDetailDTO::getDeviceId, product -> product));
        List<String> deviceIds = productVoList.stream()
                                              .map(ResourceDetailDTO::getDeviceId)
                                              .collect(Collectors.toList());
        List<VirtualMachineDTO> virtualMachineList = vmMapper.getByIdList(deviceIds);

        for (VirtualMachineDTO virtualMachine : virtualMachineList) {
            ResourceDetailDTO resourceDetailDO = productMap.get(virtualMachine.getId());
            Map<String, Object> map = new HashMap<>();
            map.put("CLOUD", virtualMachine.getCloud());
            String status = "关机";
            if ("RUNING".equals(virtualMachine.getStatus())) {
                status = "开机";
            } else if ("SHUTOFF".equals(virtualMachine.getStatus())) {
                status = "关机";
            }
            map.put("device_status", status);
            map.put("OS_VERSION", virtualMachine.getImagesName());
            map.put("RELATED_POOL", virtualMachine.getCmdbName());
            map.put("name", resourceDetailDO.getDeviceName());
            map.put("vcpu_info", virtualMachine.getVcpus());
            map.put("MEM_TOTAL_CAPACITY", virtualMachine.getRam() / 1024);
            List<Volume> volumeList = vmMapper.getVolume(virtualMachine.getId());
            long vDisks = volumeList.stream()
                                    .filter(volume -> volume.getVolumeSize() != null)
                                    .mapToLong(Volume::getVolumeSize)
                                    .sum();
            map.put("VDISKS_INFO", vDisks);
            List<IpAddress> ipList = vmMapper.getIpAddress(virtualMachine.getId(), "ECS");
            String ipIpv4 = "";
            String ipIpv6 = "";
            if (CollectionUtils.isNotEmpty(ipList)) {
                for (IpAddress ipAddress : ipList) {
                    if (ipAddress.getType().equals("EIP")) {
                        continue;
                    }
                    if (ipAddress.getType().equals("MANAGE")) {
                        if (ipAddress.getIp().length() <= 18 && ipAddress.getIp().contains(".")) {
                            ipIpv4 = ipAddress.getIp();
                        } else {
                            ipIpv6 = ipAddress.getIp();
                        }
                    } else {
                        if (StringUtils.isEmpty(ipIpv4) && ipAddress.getIp().length() <= 18 && ipAddress.getIp()
                                                                                                        .contains(".")) {
                            ipIpv4 = ipAddress.getIp();
                        }
                        if (StringUtils.isEmpty(ipIpv6) && ipAddress.getIp().contains(":")) {
                            ipIpv6 = ipAddress.getIp();
                        }
                    }
                }
                if (StringUtils.isNotBlank(ipIpv4)) {
                    map.put("MGMT_IPV4", ipIpv4);
                }
                if (StringUtils.isNotBlank(ipIpv6)) {
                    map.put("MGMT_IPV6", ipIpv6);
                }
                List<String> ip = ipList.stream().map(IpAddress::getIp).collect(Collectors.toList());
                String otherIps = String.join(",", ip);
                map.put("other_ips", otherIps);
            }

            map.put("MAINTENANCE_HANDOVER", "否(未入网)");
            map.put("UUID", virtualMachine.getInstanceUuid());
            map.put("is_gpu_vm", "是");
            map.put("total_gpu_mem", 16);
            log.info("createInstance gcs 入参 : {}", JSONObject.toJSONString(map));
            CmdbCommonRep<Map<String, Object>> cmdbCommonRep = cmdbComponentClient.insertCmdbData(CmdbModelTypeEnum.VM.getCode(), map);
            if (!"0".equals(cmdbCommonRep.getCode())) {
                log.error("入网交维失败：createInstance ecs err : {}", JSONObject.toJSONString(cmdbCommonRep));
                throw new BusinessException(BusinessExceptionEnum.CMDB_INSTANCE_FAILED.code, BusinessExceptionEnum.CMDB_INSTANCE_FAILED.message);
            }
            log.info("createInstance ecs 返回结果 : {}", JSONObject.toJSONString(cmdbCommonRep));
            Map<String, Object> data = cmdbCommonRep.getData();
            String instanceId = data.get("instanceId").toString();
            resourceDetailManager.updateConfigId(instanceId, resourceDetailDO.getId(), ipIpv4);
            createIpv3(virtualMachine, ipList);
        }
        addRelation(CmdbModelTypeEnum.VM.getCode(), orderId, ProductTypeEnum.GCS);
        log.info("createInstance ecs end orderId = {}", orderId);
    }

    private void createInstanceGcsOfCorporate(String orderId) {
        ResourceDetailQuery query = new ResourceDetailQuery();
        query.setType(ProductTypeEnum.GCS.getCode());
        query.setOrderId(orderId);
        List<ResourceDetailDTO> productVoList = resourceDetailManager.listCorporate(query);
        productVoList = productVoList.stream()
                .filter(productVo -> StringUtils.isBlank(productVo.getConfigId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productVoList)) {
            addRelation(CmdbModelTypeEnum.VM.getCode(), orderId, ProductTypeEnum.GCS);
            return;
        }
        log.info("createInstance gcs start orderId = {}", orderId);
        Map<String, ResourceDetailDTO> productMap = productVoList.stream()
                .collect(Collectors.toMap(ResourceDetailDTO::getDeviceId, product -> product));
        List<String> deviceIds = productVoList.stream()
                .map(ResourceDetailDTO::getDeviceId)
                .collect(Collectors.toList());
        List<VirtualMachineDTO> virtualMachineList = vmMapper.getByIdList(deviceIds);

        for (VirtualMachineDTO virtualMachine : virtualMachineList) {
            ResourceDetailDTO resourceDetailDO = productMap.get(virtualMachine.getId());
            Map<String, Object> map = new HashMap<>();
            map.put("CLOUD", virtualMachine.getCloud());
            String status = "关机";
            if ("RUNING".equals(virtualMachine.getStatus())) {
                status = "开机";
            } else if ("SHUTOFF".equals(virtualMachine.getStatus())) {
                status = "关机";
            }
            map.put("device_status", status);
            map.put("OS_VERSION", virtualMachine.getImagesName());
            map.put("RELATED_POOL", virtualMachine.getCmdbName());
            map.put("name", resourceDetailDO.getDeviceName());
            map.put("vcpu_info", virtualMachine.getVcpus());
            map.put("MEM_TOTAL_CAPACITY", virtualMachine.getRam() / 1024);
            List<Volume> volumeList = vmMapper.getVolume(virtualMachine.getId());
            long vDisks = volumeList.stream()
                    .filter(volume -> volume.getVolumeSize() != null)
                    .mapToLong(Volume::getVolumeSize)
                    .sum();
            map.put("VDISKS_INFO", vDisks);
            List<IpAddress> ipList = vmMapper.getIpAddress(virtualMachine.getId(), "ECS");
            String ipIpv4 = "";
            String ipIpv6 = "";
            if (CollectionUtils.isNotEmpty(ipList)) {
                for (IpAddress ipAddress : ipList) {
                    if (ipAddress.getType().equals("EIP")) {
                        continue;
                    }
                    if (ipAddress.getType().equals("MANAGE")) {
                        if (ipAddress.getIp().length() <= 18 && ipAddress.getIp().contains(".")) {
                            ipIpv4 = ipAddress.getIp();
                        } else {
                            ipIpv6 = ipAddress.getIp();
                        }
                    } else {
                        if (StringUtils.isEmpty(ipIpv4) && ipAddress.getIp().length() <= 18 && ipAddress.getIp()
                                .contains(".")) {
                            ipIpv4 = ipAddress.getIp();
                        }
                        if (StringUtils.isEmpty(ipIpv6) && ipAddress.getIp().contains(":")) {
                            ipIpv6 = ipAddress.getIp();
                        }
                    }
                }
                if (StringUtils.isNotBlank(ipIpv4)) {
                    map.put("MGMT_IPV4", ipIpv4);
                }
                if (StringUtils.isNotBlank(ipIpv6)) {
                    map.put("MGMT_IPV6", ipIpv6);
                }
                List<String> ip = ipList.stream().map(IpAddress::getIp).collect(Collectors.toList());
                String otherIps = String.join(",", ip);
                map.put("other_ips", otherIps);
            }

            map.put("MAINTENANCE_HANDOVER", "否(未入网)");
            map.put("UUID", virtualMachine.getInstanceUuid());
            map.put("is_gpu_vm", "是");
            map.put("total_gpu_mem", 16);
            log.info("createInstance gcs 入参 : {}", JSONObject.toJSONString(map));
            CmdbCommonRep<Map<String, Object>> cmdbCommonRep = cmdbComponentClient.insertCmdbData(CmdbModelTypeEnum.VM.getCode(), map);
            if (!"0".equals(cmdbCommonRep.getCode())) {
                log.error("入网交维失败：createInstance ecs err : {}", JSONObject.toJSONString(cmdbCommonRep));
                throw new BusinessException(BusinessExceptionEnum.CMDB_INSTANCE_FAILED.code, BusinessExceptionEnum.CMDB_INSTANCE_FAILED.message);
            }
            log.info("createInstance ecs 返回结果 : {}", JSONObject.toJSONString(cmdbCommonRep));
            Map<String, Object> data = cmdbCommonRep.getData();
            String instanceId = data.get("instanceId").toString();
            resourceDetailManager.updateConfigId(instanceId, resourceDetailDO.getId(), ipIpv4);
            createIpv3(virtualMachine, ipList);
        }
        addRelationOfCorporate(CmdbModelTypeEnum.VM.getCode(), orderId, ProductTypeEnum.GCS);
        log.info("createInstance ecs end orderId = {}", orderId);
    }

    public void createInstanceObs(String orderId) {
        ResourceDetailQuery query = new ResourceDetailQuery();
        query.setType(ProductTypeEnum.OBS.getCode());
        query.setOrderId(orderId);
        List<ResourceDetailDTO> productVoList = resourceDetailManager.list(query);
        productVoList = productVoList.stream()
                                     .filter(productVo -> StringUtils.isBlank(productVo.getConfigId()))
                                     .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productVoList)) {
            addRelation(CmdbModelTypeEnum.OBS.getCode(), orderId, ProductTypeEnum.OBS);
            return;
        }
        log.info("createInstance obs start orderId = {}", orderId);
        for (ResourceDetailDTO orderProductVo : productVoList) {
            VirtualMachineDTO virtualMachine = vmMapper.getPoolById(orderProductVo.getResourcePoolId());
            if (virtualMachine == null || StringUtils.isBlank(virtualMachine.getBucketPool())) {
                log.error("入网交维失败：createInstance obs err 未查找到存储池 goodsOrderId = {}", orderProductVo.getGoodsOrderId());
                throw new BusinessException(BusinessExceptionEnum.CMDB_INSTANCE_FAILED.code, BusinessExceptionEnum.CMDB_INSTANCE_FAILED.message);
            }
            StandardWorkOrderProductDO productDO = standardWorkOrderProductDAO.getBySubOrderId(orderProductVo.getGoodsOrderId());
            ObsModel obsModel = JSON.parseObject(productDO.getPropertySnapshot(), ObsModel.class);
            Map<String, Object> map = new HashMap<>();
            map.put("name", orderProductVo.getDeviceName());
            map.put("bucket_distributed_pool", virtualMachine.getBucketPool());
            map.put("CAPCITY", obsModel.getStorageDiskSize());
            map.put("bucket_user", orderProductVo.getApplyUserName());
            log.info("createInstance obs 入参 : {}", JSONObject.toJSONString(map));
            CmdbCommonRep<Map<String, Object>> cmdbCommonRep = cmdbComponentClient.insertCmdbData(CmdbModelTypeEnum.OBS.getCode(), map);
            if (!"0".equals(cmdbCommonRep.getCode())) {
                log.error("入网交维失败：createInstance obs err : {}", JSONObject.toJSONString(cmdbCommonRep));
                throw new BusinessException(BusinessExceptionEnum.CMDB_INSTANCE_FAILED.code, BusinessExceptionEnum.CMDB_INSTANCE_FAILED.message);
            }
            log.info("createInstance obs 返回结果 : {}", JSONObject.toJSONString(cmdbCommonRep));
            Map<String, Object> data = cmdbCommonRep.getData();
            String instanceId = data.get("instanceId").toString();
            resourceDetailManager.updateConfigId(instanceId, orderProductVo.getId(), null);
        }
        addRelation(CmdbModelTypeEnum.OBS.getCode(), orderId, ProductTypeEnum.OBS);
        log.info("createInstance obs end orderId = {}", orderId);
    }

    public void createInstanceObsOfCorporate(String orderId) {
        ResourceDetailQuery query = new ResourceDetailQuery();
        query.setType(ProductTypeEnum.OBS.getCode());
        query.setOrderId(orderId);
        List<ResourceDetailDTO> productVoList = resourceDetailManager.listCorporate(query);
        productVoList = productVoList.stream()
                .filter(productVo -> StringUtils.isBlank(productVo.getConfigId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productVoList)) {
            addRelation(CmdbModelTypeEnum.OBS.getCode(), orderId, ProductTypeEnum.OBS);
            return;
        }
        log.info("createInstance obs start orderId = {}", orderId);
        for (ResourceDetailDTO orderProductVo : productVoList) {
            VirtualMachineDTO virtualMachine = vmMapper.getPoolById(orderProductVo.getResourcePoolId());
            if (virtualMachine == null || StringUtils.isBlank(virtualMachine.getBucketPool())) {
                log.error("入网交维失败：createInstance obs err 未查找到存储池 goodsOrderId = {}", orderProductVo.getGoodsOrderId());
                throw new BusinessException(BusinessExceptionEnum.CMDB_INSTANCE_FAILED.code, BusinessExceptionEnum.CMDB_INSTANCE_FAILED.message);
            }
            CorporateOrderProductDO productDO = corporateOrderProductDAO.getBySubOrderId(orderProductVo.getGoodsOrderId());
            ObsModel obsModel = JSON.parseObject(productDO.getPropertySnapshot(), ObsModel.class);
            Map<String, Object> map = new HashMap<>();
            map.put("name", orderProductVo.getDeviceName());
            map.put("bucket_distributed_pool", virtualMachine.getBucketPool());
            map.put("CAPCITY", obsModel.getStorageDiskSize());
            map.put("bucket_user", orderProductVo.getApplyUserName());
            log.info("createInstance obs 入参 : {}", JSONObject.toJSONString(map));
            CmdbCommonRep<Map<String, Object>> cmdbCommonRep = cmdbComponentClient.insertCmdbData(CmdbModelTypeEnum.OBS.getCode(), map);
            if (!"0".equals(cmdbCommonRep.getCode())) {
                log.error("入网交维失败：createInstance obs err : {}", JSONObject.toJSONString(cmdbCommonRep));
                throw new BusinessException(BusinessExceptionEnum.CMDB_INSTANCE_FAILED.code, BusinessExceptionEnum.CMDB_INSTANCE_FAILED.message);
            }
            log.info("createInstance obs 返回结果 : {}", JSONObject.toJSONString(cmdbCommonRep));
            Map<String, Object> data = cmdbCommonRep.getData();
            String instanceId = data.get("instanceId").toString();
            resourceDetailManager.updateConfigId(instanceId, orderProductVo.getId(), null);
        }
        addRelationOfCorporate(CmdbModelTypeEnum.OBS.getCode(), orderId, ProductTypeEnum.OBS);
        log.info("createInstance obs end orderId = {}", orderId);
    }

    /**
     * I3_DETAIL_IP
     */
    private void createIpv3(VirtualMachineDTO virtualMachine, List<IpAddress> ipList) {
        ipList = ipList.stream().filter(ip -> !"MANAGE".equals(ip.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ipList)) {
            return;
        }

        for (IpAddress ipAddress : ipList) {
            if (StringUtils.isEmpty(ipAddress.getSubnetId())) {
                continue;
            }
            SubnetCommonDo subnet = networkHandle.getSubnet(ipAddress.getSubnetId());
            if (null == subnet||Objects.isNull(subnet.getInstanceId())) {
                continue;
            }
            List<String> instanceIds = new ArrayList<>();
            List<String> relatedInstances = new ArrayList<>();
            String[] parts = subnet.getCidr().split("/");
            String subnetIp = parts[0];
            int prefixLength = Integer.parseInt(parts[1]);
            Map<String, Object> map = new HashMap<>();
            map.put("address", ipAddress.getIp());
            map.put("state", "已分配");
            map.put("CLOUD", virtualMachine.getCloud());
            map.put("RELATED_POOL", virtualMachine.getCmdbName());
            map.put("addr", subnetIp);
            map.put("mask", prefixLength);
            log.info("createIpv3 I3_DETAIL_IP 入参 : {}", JSONObject.toJSONString(map));
            CmdbCommonRep<Map<String, Object>> cmdbCommonRep = cmdbComponentClient.insertCmdbData(CmdbModelTypeEnum.I3_DETAIL_IP.getCode(), map);
            if (!"0".equals(cmdbCommonRep.getCode())) {
                log.error("入网交维失败：createIpv3 I3_DETAIL_IP err : {}", JSONObject.toJSONString(cmdbCommonRep));
                continue;
            }
            log.info("createIpv3 I3_DETAIL_IP 返回结果 : {}", JSONObject.toJSONString(cmdbCommonRep));
            Map<String, Object> data = cmdbCommonRep.getData();
            String instanceId = data.get("instanceId").toString();
            NetworkIpAdress networkGoods = new NetworkIpAdress();
            networkGoods.setId(networkIpAddressMapper.selectSeqId());
            networkGoods.setNetworkId(subnet.getNetworkId());
            networkGoods.setSubnetId(subnet.getId());
            networkGoods.setDeleted(1);
            networkGoods.setIp(ipAddress.getIp());
            networkGoods.setType("NETWORK");
            networkGoods.setCmdbInstanceId(instanceId);
            networkIpAddressMapper.insert(networkGoods);
            relatedInstances.add(instanceId);
            instanceIds.add(subnet.getInstanceId());
            Map<String, Object> relationMap = new HashMap<>();
            relationMap.put("instance_ids", instanceIds);
            relationMap.put("related_instance_ids", relatedInstances);
            log.info("createIpv3 addRelation 入参 {}", JSONObject.toJSONString(relationMap));
            CmdbCommonRep<Map<String, Object>> common = cmdbComponentClient.updateRelation(CmdbModelTypeEnum.I3_IP.getCode(), CmdbModelTypeEnum.I3_DETAIL_IP.getCode(), relationMap);
            if (!"0".equals(common.getCode())) {
                log.error("入网交维失败：createIpv3 addRelation err : {}", JSONObject.toJSONString(common));
            }
            log.info("createIpv3 addRelation 返回结果 {}", JSONObject.toJSONString(common));
        }

    }


    private void addRelation(String type, String orderId, ProductTypeEnum productTypeEnum) {
        log.info("addRelation start type :{}, orderId: {}, productType: {}", type, orderId, productTypeEnum.getCode());
        ResourceDetailQuery query = new ResourceDetailQuery();
        query.setType(productTypeEnum.getCode());
        query.setOrderId(orderId);
        List<ResourceDetailDTO> productVoList = resourceDetailManager.list(query);
        productVoList = productVoList.stream()
                                     .filter(productVo -> StringUtils.isNoneBlank(productVo.getConfigId()))
                                     .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productVoList)) {
            return;
        }
        //获取cmdb租户
        String cmdbId = vmMapper.getCmdbTenatId(productVoList.get(0).getTenantId());
        // String cmdbId = getCmdbId(productVoList.get(0).getTenantId());
        if (StringUtils.isBlank(cmdbId)) {
            log.error("入网交维失败：addRelation error 查找不到cmdbId tenantId = {}", productVoList.get(0)
                                                                                                  .getTenantId());
            throw new BusinessException(BusinessExceptionEnum.CMDB_INSTANCE_FAILED.code, BusinessExceptionEnum.CMDB_INSTANCE_FAILED.message);
        }

        List<String> cmdbInstanceIds = new ArrayList<>();
        List<String> relatedInstances = new ArrayList<>();
        //------------------------------下面是同步CMDB 租户和云主机关联关系------------------------------------------------
        for (ResourceDetailDTO productVo : productVoList) {
            cmdbInstanceIds.add(productVo.getConfigId());
            relatedInstances.add(cmdbId);
        }
        Map<String, Object> relationMap = new HashMap<>();
        relationMap.put("instance_ids", cmdbInstanceIds);
        relationMap.put("related_instance_ids", relatedInstances);
        log.info("addRelation 入参 {}", JSONObject.toJSONString(relationMap));
        CmdbCommonRep<Map<String, Object>> common = cmdbComponentClient.addRelation(type, CmdbModelTypeEnum.TENANT.getCode(), relationMap);
        if (!"0".equals(common.getCode())) {
            log.error("入网交维失败：addRelation err : {}", JSONObject.toJSONString(common));
            throw new BusinessException(BusinessExceptionEnum.CMDB_INSTANCE_FAILED.code, BusinessExceptionEnum.CMDB_INSTANCE_FAILED.message);
        }
        log.info("addRelation end 返回结果 {}", JSONObject.toJSONString(common));
        //---------------------------------下面是同步CMDB 业务系统和云主机关联关系------------------------------------------
        for (ResourceDetailDTO productVo : productVoList) {
            if (null == productVo.getModuleId()) {
                log.info("addRelationNetwork 未查询到业务系统：{}", JSONObject.toJSONString(productVo));
                continue;
            }
            List<String> cmdbInstance = new ArrayList<>();
            List<String> busiSystemInstances = new ArrayList<>();
            cmdbInstance.add(productVo.getConfigId());
            RegionDTO regionDTO = regionManager.getById(Long.valueOf(productVo.getResourcePoolId()));
            String cmdbSystemId = vmMapper.getCmdbSystemIdByModuleId(productVo.getModuleId(), regionDTO.getCode());
            busiSystemInstances.add(cmdbSystemId);
            Map<String, Object> busMap = new HashMap<>();
            busMap.put("instance_ids", cmdbInstance);
            busMap.put("related_instance_ids", busiSystemInstances);
            log.info("addRelation  BUSINESS_SYSTEM 入参 {}", JSONObject.toJSONString(busMap));
            CmdbCommonRep<Map<String, Object>> busCommon = cmdbComponentClient.addRelation(type, CmdbModelTypeEnum.BUSINESS_SYSTEM.getCode(), busMap);
            if (!"0".equals(busCommon.getCode())) {
                log.error("入网交维失败：addRelationNetwork BUSINESS_SYSTEM err : {}", JSONObject.toJSONString(busCommon));
                throw new BusinessException(BusinessExceptionEnum.CMDB_INSTANCE_FAILED.code, BusinessExceptionEnum.CMDB_INSTANCE_FAILED.message);
            }
            log.info("addRelationNetwork BUSINESS_SYSTEM 返回结果 {}", JSONObject.toJSONString(busCommon));
        }
        addRelationNetwork(cmdbId, productVoList);
    }

    private void addRelationOfCorporate(String type, String orderId, ProductTypeEnum productTypeEnum) {
        log.info("addRelation start type :{}, orderId: {}, productType: {}", type, orderId, productTypeEnum.getCode());
        ResourceDetailQuery query = new ResourceDetailQuery();
        query.setType(productTypeEnum.getCode());
        query.setOrderId(orderId);
        List<ResourceDetailDTO> productVoList = resourceDetailManager.list(query);
        productVoList = productVoList.stream()
                .filter(productVo -> StringUtils.isNoneBlank(productVo.getConfigId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productVoList)) {
            return;
        }
        //获取cmdb租户
        String cmdbId = vmMapper.getCmdbTenatId(productVoList.get(0).getTenantId());
        // String cmdbId = getCmdbId(productVoList.get(0).getTenantId());
        if (StringUtils.isBlank(cmdbId)) {
            log.error("入网交维失败：addRelation error 查找不到cmdbId tenantId = {}", productVoList.get(0)
                    .getTenantId());
            throw new BusinessException(BusinessExceptionEnum.CMDB_INSTANCE_FAILED.code, BusinessExceptionEnum.CMDB_INSTANCE_FAILED.message);
        }

        List<String> cmdbInstanceIds = new ArrayList<>();
        List<String> relatedInstances = new ArrayList<>();
        //------------------------------下面是同步CMDB 租户和云主机关联关系------------------------------------------------
        for (ResourceDetailDTO productVo : productVoList) {
            cmdbInstanceIds.add(productVo.getConfigId());
            relatedInstances.add(cmdbId);
        }
        Map<String, Object> relationMap = new HashMap<>();
        relationMap.put("instance_ids", cmdbInstanceIds);
        relationMap.put("related_instance_ids", relatedInstances);
        log.info("addRelation 入参 {}", JSONObject.toJSONString(relationMap));
        CmdbCommonRep<Map<String, Object>> common = cmdbComponentClient.addRelation(type, CmdbModelTypeEnum.TENANT.getCode(), relationMap);
        if (!"0".equals(common.getCode())) {
            log.error("入网交维失败：addRelation err : {}", JSONObject.toJSONString(common));
            throw new BusinessException(BusinessExceptionEnum.CMDB_INSTANCE_FAILED.code, BusinessExceptionEnum.CMDB_INSTANCE_FAILED.message);
        }
        log.info("addRelation end 返回结果 {}", JSONObject.toJSONString(common));
        addRelationNetwork(cmdbId, productVoList);
    }

    private void addRelationNetwork(String cmdbTenantId, List<ResourceDetailDTO> productVoList) {
        List<String> list = productVoList.stream()
                                         .map(ResourceDetailDTO::getVpcId)
                                         .filter(vpcId -> StringUtils.isNotEmpty(vpcId))
                                         // vpc/network多个的时候，使用逗号分割，这里需要split一下
                                         .flatMap(vpcId -> Arrays.stream(vpcId.split(",")))
                                         .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        //todo
        List<SubnetCommonDo> subnetList = networkHandle.getSubnetByNetworkIds(list);
        subnetList = subnetList.stream()
                               .filter(subnetCommonDo -> StringUtils.isNotEmpty(subnetCommonDo.getInstanceId()))
                               .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(subnetList)) {
            return;
        }
        List<String> instanceIds = new ArrayList<>();
        List<String> relatedInstances = new ArrayList<>();
        for (SubnetCommonDo subnetCommonDo : subnetList) {
            instanceIds.add(subnetCommonDo.getInstanceId());
            relatedInstances.add(cmdbTenantId);
        }
        Map<String, Object> relationMap = new HashMap<>();
        relationMap.put("instance_ids", instanceIds);
        relationMap.put("related_instance_ids", relatedInstances);
        log.info("addRelation 入参 {}", JSONObject.toJSONString(relationMap));
        CmdbCommonRep<Map<String, Object>> common = cmdbComponentClient.addRelation(CmdbModelTypeEnum.I3_IP.getCode(), CmdbModelTypeEnum.TENANT.getCode(), relationMap);
        if (!"0".equals(common.getCode())) {
            log.error("入网交维失败：addRelationNetwork TENANT err : {}", JSONObject.toJSONString(common));
            throw new BusinessException(BusinessExceptionEnum.CMDB_INSTANCE_FAILED.code, BusinessExceptionEnum.CMDB_INSTANCE_FAILED.message);
        }
        log.info("addRelationNetwork TENANT 返回结果 {}", JSONObject.toJSONString(common));
        Long moduleId = productVoList.get(0).getModuleId();
        for (SubnetCommonDo subnetCommonDo : subnetList) {
            List<String> instanceList = new ArrayList<>();
            List<String> busSystemInstances = new ArrayList<>();
            instanceList.add(subnetCommonDo.getInstanceId());
            String cmdbSystemId = vmMapper.getCmdbSystemIdByModuleId(moduleId, subnetCommonDo.getRegionCode());
            busSystemInstances.add(cmdbSystemId);
            Map<String, Object> busMap = new HashMap<>();
            busMap.put("instance_ids", instanceList);
            busMap.put("related_instance_ids", busSystemInstances);
            CmdbCommonRep<Map<String, Object>> busCommon = cmdbComponentClient.addRelation(CmdbModelTypeEnum.I3_IP.getCode(), CmdbModelTypeEnum.BUSINESS_SYSTEM.getCode(), busMap);
            if (!"0".equals(busCommon.getCode())) {
                log.error("入网交维失败：addRelationNetwork BUSINESS_SYSTEM err : {}", JSONObject.toJSONString(busCommon));
                throw new BusinessException(BusinessExceptionEnum.CMDB_INSTANCE_FAILED.code, BusinessExceptionEnum.CMDB_INSTANCE_FAILED.message);
            }
            log.info("addRelationNetwork BUSINESS_SYSTEM 返回结果 {}", JSONObject.toJSONString(busCommon));
        }

    }

    /**
     * I3_DETAIL_IP
     */
    private void deleteIpv3Detail(String networkId) {
        List<NetworkIpAdress> ipList = networkIpAddressMapper.selectByNetworkId(networkId);
        if (CollectionUtils.isEmpty(ipList)) {
            return;
        }
        List<String> instanceIds = new ArrayList<>();
        List<String> relatedInstances = new ArrayList<>();
        for (NetworkIpAdress ipAddress : ipList) {
            if (StringUtils.isEmpty(ipAddress.getSubnetId())) {
                continue;
            }
            NetworkSubnetOrder subnetOrder = networkSubnetOrderMapper.selectById(ipAddress.getSubnetId());
            if (null == subnetOrder) {
                continue;
            }
            relatedInstances.add(ipAddress.getCmdbInstanceId());
            instanceIds.add(subnetOrder.getInstanceId());
        }
        Map<String, Object> relationMap = new HashMap<>();
        relationMap.put("instance_ids", instanceIds);
        relationMap.put("related_instance_ids", relatedInstances);
        log.info("deleteIpv3Detail deleteRelation 入参 {}", JSONObject.toJSONString(relationMap));
        CmdbCommonRep<Map<String, Object>> common = cmdbComponentClient.deleteRelation("I3_IP", "I3_DETAIL_IP", relationMap);
        if (!"0".equals(common.getCode())) {
            log.error("删除三级网络详情失败：deleteIpv3Detail deleteRelation err : {}", JSONObject.toJSONString(common));
        }
        log.info("deleteIpv3Detail deleteRelation 返回结果 {}", JSONObject.toJSONString(common));
        for (NetworkIpAdress ipAddress : ipList) {
            log.info("deleteIpv3Detail I3_DETAIL_IP 入参 : {}", ipAddress.getCmdbInstanceId());
            CmdbCommonRep<Map<String, Object>> cmdbCommonRep = cmdbComponentClient.deleteCmdbData("I3_DETAIL_IP", ipAddress.getCmdbInstanceId());
            if (!"0".equals(cmdbCommonRep.getCode())) {
                log.error("删除三级网络详情失败：deleteIpv3Detail I3_DETAIL_IP err : {}", JSONObject.toJSONString(cmdbCommonRep));
                continue;
            }
            log.info("deleteIpv3Detail I3_DETAIL_IP 返回结果 : {}", JSONObject.toJSONString(cmdbCommonRep));
        }


    }

    private void deleteRelationNetwork(String networkId) {
        List<NetworkSubnetOrder> subnetList = networkSubnetOrderMapper.selectByNetworkId(networkId);
        subnetList = subnetList.stream()
                               .filter(subnet -> StringUtils.isNotEmpty(subnet.getInstanceId()))
                               .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(subnetList)) {
            return;
        }
        NetworkOrder networkOrder = networkOrderMapper.selectById(networkId);
        List<String> instanceIds = new ArrayList<>();
        List<String> relatedInstances = new ArrayList<>();
        List<String> busiSystemInstances = new ArrayList<>();
        List<String> ip2Instances = new ArrayList<>();
        String cmdbSystemId = vmMapper.getCmdbSystemIdByModuleId(networkOrder.getModuleId(), networkOrder.getRegionCode());
        String cmdbTenatId = vmMapper.getCmdbTenatId(networkOrder.getTenantId());
        for (NetworkSubnetOrder networkSubnetOrder : subnetList) {
            instanceIds.add(networkSubnetOrder.getInstanceId());
            relatedInstances.add(cmdbTenatId);
            busiSystemInstances.add(cmdbSystemId);
            if (StringUtils.isNotEmpty(networkSubnetOrder.getLevel2InstanceId())) {
                ip2Instances.add(networkSubnetOrder.getLevel2InstanceId());
            }
        }
        // 解绑三级网段和租户
        Map<String, Object> relationMap = new HashMap<>();
        relationMap.put("instance_ids", instanceIds);
        relationMap.put("related_instance_ids", relatedInstances);
        log.info("deleteRelationNetwork 入参 {}", JSONObject.toJSONString(relationMap));
        CmdbCommonRep<Map<String, Object>> common = cmdbComponentClient.deleteRelation("I3_IP", "TENANT", relationMap);
        if (!"0".equals(common.getCode())) {
            log.error("网络删除失败：deleteRelationNetwork TENANT err : {}", JSONObject.toJSONString(common));

        }
        log.info("deleteRelationNetwork TENANT 返回结果 {}", JSONObject.toJSONString(common));
        // 解绑三级网段和业务组
        Map<String, Object> busMap = new HashMap<>();
        busMap.put("instance_ids", instanceIds);
        busMap.put("related_instance_ids", busiSystemInstances);
        CmdbCommonRep<Map<String, Object>> busCommon = cmdbComponentClient.deleteRelation("I3_IP", "BUSINESS_SYSTEM", busMap);
        if (!"0".equals(busCommon.getCode())) {
            log.error("网络删除失败：deleteRelationNetwork BUSINESS_SYSTEM err : {}", JSONObject.toJSONString(busCommon));

        }
        log.info("deleteRelationNetwork BUSINESS_SYSTEM 返回结果 {}", JSONObject.toJSONString(busCommon));
        // 解绑三级网段和二级网段
        if (CollectionUtils.isNotEmpty(ip2Instances)) {
            Map<String, Object> ip2Map = new HashMap<>();
            ip2Map.put("instance_ids", instanceIds);
            ip2Map.put("related_instance_ids", ip2Instances);
            CmdbCommonRep<Map<String, Object>> ip2Common = cmdbComponentClient.deleteRelation("I3_IP", "I2_IP", ip2Map);
            if (!"0".equals(ip2Common.getCode())) {
                log.error("网络删除失败：deleteRelationNetwork BUSINESS_SYSTEM err : {}", JSONObject.toJSONString(ip2Common));
            }
            log.info("deleteRelationNetwork BUSINESS_SYSTEM 返回结果 {}", JSONObject.toJSONString(ip2Common));
        }
    }

    private void deleteIpv3(String networkId) {
        List<NetworkSubnetOrder> subnetList = networkSubnetOrderMapper.selectByNetworkId(networkId);
        Set<String> instanceIdSet = subnetList.stream()
                                              .map(NetworkSubnetOrder::getInstanceId)
                                              .filter(instanceId -> StringUtils.isNotEmpty(instanceId))
                                              .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(instanceIdSet)) {
            return;
        }
        for (String instanceId : instanceIdSet) {
            // 将三级网段修改为未分配
            IpUpdateStatusVO ipUpdateStatusRc = new IpUpdateStatusVO();
            ipUpdateStatusRc.setStatus("0");
            ipUpdateStatusRc.setIpVersion(IpLevelEnum.IP3);
            List<String> ids = new ArrayList<>();
            ids.add(instanceId);
            ipUpdateStatusRc.setInstanceId(ids);
            networkService.ipUpdateStatus(ipUpdateStatusRc);

            // 回收三级网段
            log.info("deleteIpv3 I3_DETAIL_IP 入参 : {}", instanceId);
            CmdbCommonRep<Map<String, Object>> cmdbCommonRep = cmdbComponentClient.deleteCmdbData("I3_IP", instanceId);
            if (!"0".equals(cmdbCommonRep.getCode())) {
                log.error("入网交维失败：createIpv3 I3_DETAIL_IP err : {}", JSONObject.toJSONString(cmdbCommonRep));
                continue;
            }
            log.info("createIpv3 I3_DETAIL_IP 返回结果 : {}", JSONObject.toJSONString(cmdbCommonRep));
        }
    }

    @Override
    public void deleteInstance(Long detailId) {
        log.info("deleteInstance start detailId : {}", detailId);
        ResourceDetailDTO resourceDetailDO = resourceDetailManager.selectByIdNoStatus(detailId);
        if (!ProductTypeEnum.ECS.getCode().equals(resourceDetailDO.getType())
                && !ProductTypeEnum.GCS.getCode().equals(resourceDetailDO.getType())
                && !ProductTypeEnum.OBS.getCode().equals(resourceDetailDO.getType())
                && !ProductTypeEnum.REDIS.getCode().equals(resourceDetailDO.getType())
                && !ProductTypeEnum.MYSQL.getCode().equals(resourceDetailDO.getType())
                && !ProductTypeEnum.POSTGRESQL.getCode().equals(resourceDetailDO.getType())) {
            return;
        }
        if (StringUtils.isBlank(resourceDetailDO.getConfigId())) {
            return;
        }
        String type = null;
        String ECS_TYPE = "VIRTUAL_MACHINE";
        String OBS_TYPE = "BUCKET";
        String cmdbId = resourceDetailDO.getConfigId();
        if (GoodsTypeEnum.ECS.getCode().equals(resourceDetailDO.getType())
                || GoodsTypeEnum.GCS.getCode().equals(resourceDetailDO.getType())
                || GoodsTypeEnum.REDIS.getCode().equals(resourceDetailDO.getType())
                || GoodsTypeEnum.MYSQL.getCode().equals(resourceDetailDO.getType())
                || GoodsTypeEnum.POSTGRESQL.getCode().equals(resourceDetailDO.getType())) {
            type = ECS_TYPE;
        } else if (GoodsTypeEnum.OBS.getCode().equals(resourceDetailDO.getType())) {
            type = OBS_TYPE;
        }
        String cmdbTenatId = vmMapper.getCmdbTenatId(resourceDetailDO.getTenantId());
        //删除关系
        List<String> cmdbInstanceIds = new ArrayList<>();
        List<String> relatedInstances = new ArrayList<>();
        cmdbInstanceIds.add(cmdbId);
        relatedInstances.add(cmdbTenatId);
        Map<String, Object> relationMap = new HashMap<>();
        relationMap.put("instance_ids", cmdbInstanceIds);
        relationMap.put("related_instance_ids", relatedInstances);
        log.info("deleteRelation 入参 {}", JSONObject.toJSONString(relationMap));
        CmdbCommonRep<Map<String, Object>> common = cmdbComponentClient.deleteRelation(type, "TENANT", relationMap);
        if (!"0".equals(common.getCode())) {
            log.error("删除CMDB关系失败：deleteRelation err : {}", JSONObject.toJSONString(common));
            // throw new BusinessException(BusinessExceptionEnum.DELETE_CMDB_INSTANCE_FAILED.code, BusinessExceptionEnum.DELETE_CMDB_INSTANCE_FAILED.message);
        }
        log.info("deleteRelation end 返回结果 {}", JSONObject.toJSONString(common));
        //删除实例
        CmdbCommonRep<Map<String, Object>> cmdbCommonRep = cmdbComponentClient.deleteCmdbData(type, cmdbId);
        if (!"0".equals(cmdbCommonRep.getCode())) {
            log.error("删除CMDB实例失败：deleteInstance ecs err : {}", JSONObject.toJSONString(cmdbCommonRep));
            // throw new BusinessException(BusinessExceptionEnum.DELETE_CMDB_INSTANCE_FAILED.code, BusinessExceptionEnum.DELETE_CMDB_INSTANCE_FAILED.message);
        }
    }


    @Override
    public void updateInstanceEcs(String orderId) {
        log.info("updateInstanceEcs ecs start orderId = {}", orderId);
        List<ChangeWorkOrderProductDTO> productDTOS = changeProductManager.listByWorkOrderId(orderId);
        updateInstanceEcsByEvs(productDTOS);
        List<Long> ids = productDTOS.stream()
                                    .filter(productDTO -> ProductTypeEnum.ECS.getCode()
                                                                             .equals(productDTO.getProductType()) ||
                                            ProductTypeEnum.GCS.getCode().equals(productDTO.getProductType())||
                                            ProductTypeEnum.REDIS.getCode().equals(productDTO.getProductType()) ||
                                            ProductTypeEnum.MYSQL.getCode().equals(productDTO.getProductType())||
                                            ProductTypeEnum.POSTGRESQL.getCode().equals(productDTO.getProductType()))
                                    .map(productDTO -> Long.valueOf(productDTO.getResourceDetailId()))
                                    .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        log.info("updateInstanceEcs ecs start ids = {}", JSONObject.toJSONString(ids));
        for (Long id : ids) {
            ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(id);
            if (StringUtils.isEmpty(resourceDetailDTO.getConfigId())) {
                log.info("updateInstanceEcs configId is null id = {}", resourceDetailDTO.getId());
                continue;
            }
            updateInstanceEcsByResource(resourceDetailDTO);
        }
        log.info("updateInstanceEcs ecs end orderId = {}", orderId);
    }

    private void updateInstanceEcsByEvs(List<ChangeWorkOrderProductDTO> productDTOS) {
        List<ChangeWorkOrderProductDTO> list = productDTOS.stream().filter(productDTO ->
                ProductTypeEnum.EVS.getCode().equals(productDTO.getProductType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (ChangeWorkOrderProductDTO dto : list) {
            if (!dto.getChangeType().contains(ChangeTypeEnum.STORAGE_EXPAND.getCode())) {
                continue;
            }
            ChangeEvsModel changeEvsModel = JSONObject.parseObject(dto.getPropertySnapshot(), ChangeEvsModel.class);
            if (StringUtils.isNotBlank(changeEvsModel.getVmId())) {
                ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getByDeviceId(changeEvsModel.getVmId());
                if (StringUtils.isEmpty(resourceDetailDTO.getConfigId()) ||
                        (!ProductTypeEnum.ECS.getCode().equals(resourceDetailDTO.getType())
                                && !ProductTypeEnum.GCS.getCode().equals(resourceDetailDTO.getType()))
                                && !ProductTypeEnum.MYSQL.getCode().equals(resourceDetailDTO.getType())
                                && !ProductTypeEnum.REDIS.getCode().equals(resourceDetailDTO.getType())
                                && !ProductTypeEnum.POSTGRESQL.getCode().equals(resourceDetailDTO.getType())) {
                    log.info("updateInstanceEcs configId is null id = {}", resourceDetailDTO.getId());
                    continue;
                }
                log.info("updateInstanceEcsByEvs start id = {}", resourceDetailDTO.getId());
                updateInstanceEcsByResource(resourceDetailDTO);
            }
        }
    }

    public void updateInstanceEcsByResource(ResourceDetailDTO resourceDetailDTO) {
        if (StringUtils.isEmpty(resourceDetailDTO.getConfigId()) ||
                (!ProductTypeEnum.ECS.getCode().equals(resourceDetailDTO.getType())
                        && !ProductTypeEnum.GCS.getCode().equals(resourceDetailDTO.getType()))
                        && !ProductTypeEnum.MYSQL.getCode().equals(resourceDetailDTO.getType())
                        && !ProductTypeEnum.POSTGRESQL.getCode().equals(resourceDetailDTO.getType())
                        && !ProductTypeEnum.REDIS.getCode().equals(resourceDetailDTO.getType())) {
            log.error("updateInstanceEcs configId is null id = {}", resourceDetailDTO.getId());
            return;
        }
        VirtualMachineDTO virtualMachine = vmMapper.getById(resourceDetailDTO.getDeviceId());
        Map<String, Object> map = new HashMap<>();
        map.put("vcpu_info", virtualMachine.getVcpus());
        map.put("MEM_TOTAL_CAPACITY", virtualMachine.getRam() / 1024);
        List<Volume> volumeList = vmMapper.getVolume(virtualMachine.getId());
        long vDisks = volumeList.stream()
                                .filter(volume -> volume.getVolumeSize() != null)
                                .mapToLong(Volume::getVolumeSize)
                                .sum();
        map.put("VDISKS_INFO", vDisks);
        List<IpAddress> ipList = vmMapper.getIpAddress(virtualMachine.getId(), "ECS");
        if (CollectionUtils.isNotEmpty(ipList)) {
            List<String> ip = ipList.stream().map(IpAddress::getIp).collect(Collectors.toList());
            String otherIps = String.join(",", ip);
            map.put("other_ips", otherIps);
        }
        log.info("updateInstanceEcs ecs 入参 : {}", JSONObject.toJSONString(map));
        CmdbCommonRep<Map<String, Object>> cmdbCommonRep = cmdbComponentClient.updateCmdbData(CmdbModelTypeEnum.VM.getCode(), resourceDetailDTO.getConfigId(), map);
        log.info("updateInstanceEcs 返回参数 : {}", JSONObject.toJSONString(cmdbCommonRep));
        if (!"0".equals(cmdbCommonRep.getCode())) {
            log.error("更改云主机状态失败：updateInstanceEcs ecs err : {}", JSONObject.toJSONString(cmdbCommonRep));
            throw new BusinessException(BusinessExceptionEnum.CMDB_INSTANCE_FAILED.code, BusinessExceptionEnum.CMDB_INSTANCE_FAILED.message);
        }
    }


}
