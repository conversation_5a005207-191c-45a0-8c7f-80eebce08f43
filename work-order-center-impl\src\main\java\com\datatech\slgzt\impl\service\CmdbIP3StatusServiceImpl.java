package com.datatech.slgzt.impl.service;

import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.enums.ip.IpLevelEnum;
import com.datatech.slgzt.handle.CmdbComponentClient;
import com.datatech.slgzt.manager.CmdbIP3StatusManager;
import com.datatech.slgzt.model.cmdb.CmdbCommonRep;
import com.datatech.slgzt.model.dto.CmdbIP3StatusDTO;
import com.datatech.slgzt.model.query.NetworkCmdbQuery;
import com.datatech.slgzt.service.CmdbIP3StatusService;
import com.datatech.slgzt.utils.Precondition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class CmdbIP3StatusServiceImpl implements CmdbIP3StatusService {
    @Resource
    private CmdbIP3StatusManager manager;
    @Resource
    private CmdbComponentClient cmdbComponentClient;

    @Override
    public void insert(String instanceId) {
        manager.insert(instanceId);
    }

    @Override
    public void updateSubnetOpenStatus(String instanceId, Integer subnetOpenStatus, Integer oldSubnetOpenStatus) {
        manager.updateSubnetOpenStatus(instanceId, subnetOpenStatus, oldSubnetOpenStatus);
    }

    @Override
    public CmdbIP3StatusDTO selectByInstanceId(String instanceId) {
        return manager.selectByInstanceId(instanceId);
    }

    @Override
    public List<CmdbIP3StatusDTO> selectList(NetworkCmdbQuery query) {
        return manager.selectList(query);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByInstanceId(String instanceId) {
        manager.deleteByInstanceId(instanceId);
        CmdbCommonRep<Map<String, Object>> cmdbCommonRep = cmdbComponentClient.deleteCmdbData(IpLevelEnum.IP3.getDesc(), instanceId);
        Precondition.checkArgument("0".equals(cmdbCommonRep.getCode()),
                String.format("删除实例失败, instanceId: %s, 错误信息: %s", instanceId, JSONObject.toJSONString(cmdbCommonRep)),
                msg -> log.warn(msg));
        log.info("createIpv3 I3_DETAIL_IP 返回结果 : {}", JSONObject.toJSONString(cmdbCommonRep));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelByInstanceId(String instanceId) {
        manager.cancelByInstanceId(instanceId);
        CmdbCommonRep<Map<String, Object>> cmdbCommonRep = cmdbComponentClient.deleteCmdbData(IpLevelEnum.IP3.getDesc(), instanceId);
        Precondition.checkArgument("0".equals(cmdbCommonRep.getCode()),
                String.format("删除实例失败, instanceId: %s, 错误信息: %s", instanceId, JSONObject.toJSONString(cmdbCommonRep)),
                msg -> log.warn(msg));
        log.info("createIpv3 I3_DETAIL_IP 返回结果 : {}", JSONObject.toJSONString(cmdbCommonRep));
    }
}
