package com.datatech.slgzt.model.dto;

import com.datatech.slgzt.model.bpmn.BaseWorkOrder;
import com.datatech.slgzt.model.file.UploadFileModel;
import com.datatech.slgzt.model.nostander.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 资源开通工单基础信息
 *
 * @Author: liu<PERSON><PERSON>an
 * @Date: 2025/3/13
 */

@Data
public class StandardWorkOrderDTO extends BaseWorkOrder {

    private String id;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 计费号
     */
    private String billId;


    /**
     * 客户编码
     */
    private String customNo;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 创建人名称
     */
    private String createdByName;

    /**
     * 修改者id
     */
    private Long updatedBy;

    /**
     * 三级云领导ID
     */
    private Long cloudLeaderId;

    /**
     * 二级业务部门领导ID
     */
    private Long busiDepartLeaderId;

    /**
     * 三级业务部门领导ID
     */
    private Long levelThreeLeaderId;

    /**
     * 二级云资源领导id
     */
    private Long secondLevelLeaderId;

    /**
     * 流程实例ID
     */
    private String activitiId;

    /**
     * 流程模板KEY
     */
    private String activiteKey;

    /**
     * 订单描述
     */
    private String orderDesc;

    /**
     * 平台云编码
     */
    private String domainCode;
    /**
     * 平台云名称 todo 后续添加
     */
    private String domainName;
    /**
     * 云类型分类
     */
    private String catalogueDomainCode;

    /**
     * 云类型分类
     */
    private String startRoleCode;

    /**
     * 模块ID
     */
    private Long moduleId;

    /**
     * 所属业务模块 todo 后续添加
     */
    private String moduleName;

    /**
     * 厂家名称
     */
    private String manufacturer;

    /**
     * 厂家联系人
     */
    private String manufacturerContacts;

    /**
     * 厂家联系电话
     */
    private String manufacturerMobile;

    //资源上云说明书附件 的json
    private List<UploadFileModel> resourceApplyFile;

    /**
     * 业务系统id
     */
    private Long busiSystemId;

    /**
     * 资源回收时存在，工单映射归档工单所属商品id集，多个时逗号拼接
     */
    private String recoveryGoodsIds;

    /**
     * 商品类型，资源类型：ecs 云主机、evs 云硬盘、gcs GPU云主机、obs 对象存储、slb 负载均衡、nat NAT网关；网络类型：vpc VPC、network 网络
     */
    private String recoveryType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 订单状态 0 删除 1 正常
     */
    private Integer STATUS;

    /**
     * 当前流程所属节点
     */
    private String currentNodeCode;

    /**
     * Ecs申请资源列表的json
     *
     * @see CloudEcsResourceModel //ecs资源模型
     */
    private List<CloudEcsResourceModel> cloudEcsResourceList;

    /**
     * Cpu申请资源列表的json,gcs
     */
    private List<CpuEcsResourceModel> cpuEcsResourceList;

    /**
     * MySQL申请资源列表的json
     */
    private List<EcsModel> mysqlModelList;

    /**
     * postgre申请资源列表的json
     */
    private List<EcsModel> postgreSqlModelList;

    /**
     * Redis申请资源列表的json
     */
    private List<EcsModel> redisModelList;


    /**
     * evs申请资源列表的json
     */
    private List<EvsModel> evsModelList;

    /**
     * eip申请资源列表的json
     */
    private List<EipModel> eipModelList;

    /**
     * nat资源申请json
     */
    private List<NatGatwayModel> natGatwayModelList;

    /**
     * slb资源申请json
     */
    private List<SlbModel> slbModelList;

    /**
     * obs资源申请json
     */
    private List<ObsModel> obsModelList;

    /**
     * 容器资源配额申请json
     */
    private List<CQModel> cqModelList;

    /**
     * 备份策略申请json
     */
    private List<BackupModel> backupModelList;

    /**
     * vpn申请json
     */
    private List<VpnModel> vpnModelList;

    /**
     * nas申请json
     */
    private List<NasModel> nasModelList;

    /**
     * kafka申请json
     */
    private List<KafkaModel> kafkaModelList;

    /**
     * es申请json
     */
    private List<EsModel> esModelList;

    /**
     * 宝兰德redis申请json
     */
    private List<BldRedisModel> bldRedisModelList;

    private List<SaveSchemaAuditLogDTO> auditLogList;

    /**
     * flink申请json
     */
    private List<FlinkModel> flinkModelList;

    /**
     * 物理机申请json
     */
    private List<PhysicalMachineModel> physicalMachineModelList;

    ///------------------------

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 业务系统id,废弃
     */
    private Long businessSystemId;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

    /**
     * 业务系统编码
     */
    private String businessSystemCode;


    //创建用户名称
    private String createdUserName;

    /**
     * 当前流程节点名称
     */
    private String currentNodeName;

    /**
     * 工单开始时间
     */
    private LocalDateTime workOrderStartTime;

    /**
     * 工单结束时间-完结时间
     */
    private LocalDateTime workOrderEndTime;

    // 云平台
    private String catalogueDomainName;

    /**
     * 局方负责人
     */
    private String bureauUserName;

    private String departmentName;

    /**
     * 二级业务部门领导名称
     */
    private String businessDepartLeaderName;

    /**
     * 三级业务部门领导名称
     */
    private String levelThreeLeaderName;

    //是否线下开通
    private Boolean isOffline;

}

