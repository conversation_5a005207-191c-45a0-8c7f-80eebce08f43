package com.datatech.slgzt.impl.manager;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.convert.ResourceDetailManagerConvert;
import com.datatech.slgzt.dao.ResourceDetailDAO;
import com.datatech.slgzt.dao.VnicDAO;
import com.datatech.slgzt.dao.WocSecurityGroupDAO;
import com.datatech.slgzt.dao.model.ResourceDetailDO;
import com.datatech.slgzt.dao.model.VnicDO;
import com.datatech.slgzt.dao.model.security.WocSecurityGroupDO;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.SourceTypeEnum;
import com.datatech.slgzt.enums.VmOperationEnum;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.ModuleOfflineDTO;
import com.datatech.slgzt.model.dto.OperateVmDTO;
import com.datatech.slgzt.model.dto.ResetVmPwdDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.network.NetcardDetailDTO;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.model.query.VmOperateQuery;
import com.datatech.slgzt.model.query.VnicQuery;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.QueryParamCheckUtil;
import com.datatech.slgzt.utils.StreamUtils;
import com.datatech.slgzt.warpper.PageWarppers;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.HttpResult;
import com.ejlchina.okhttps.OkHttps;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月13日 13:00:18
 */
@Service
public class ResourceDetailManagerImpl implements ResourceDetailManager {

    @Resource
    private ResourceDetailDAO dao;

    @Resource
    private WocSecurityGroupDAO securityGroupDao;

    @Resource
    private VnicDAO vnicDAO;

    @Resource
    private ResourceDetailManagerConvert convert;

    @Value("${http.layoutCenterUrl}")
    private String layoutCenter;

    private final String layoutVmOperate = "v1/erm/vm/operate";
    private final String layoutVmResetPwd = "v1/erm/vm/resetPwd";


    @Override
    public ResourceDetailDTO selectByIdNoStatus(Long id) {
        return convert.do2dto(dao.selectByIdNoStatus(id));
    }

    /**
     * saveResourceDetail
     *
     * @param resourceDetailVO
     */
    @Override
    public Long saveResourceDetail(ResourceDetailDTO dto) {
        ResourceDetailDO detailDO = convert.dto2do(dto);
        dao.insert(detailDO);
        return detailDO.getId();
    }

    @Override
    public void updateById(ResourceDetailDTO dt) {
        dao.updateById(convert.dto2do(dt));
    }

    @Override
    public void updateVmIdAndEscNameById(ResourceDetailDTO dt) {
        dao.updateVmIdAndEscNameById(dt.getId(), dt.getDeviceId(), dt.getDeviceName(), dt.getRecoveryStatus());
    }

    @Override
    public void updateEipById(ResourceDetailDTO dt) {
        dao.updateEipById(dt.getId(), dt.getRecoveryStatus());
    }

    @Override
    public void updateSecurityGroupIdById(ResourceDetailDTO dt) {
        dao.updateSecurityGroupIdById(dt.getId(), dt.getSecurityGroupIds());
    }

    @Override
    public void updateRecoveryTypeByIds(List<Long> ids, Integer status) {
        dao.updateRecoveryTypeByIds(ids, status);
    }

    @Override
    public void updateRecoveryTypeByDeviceIds(List<String> deviceIds, Integer status) {
        dao.updateRecoveryTypeByDeviceIds(deviceIds, status);
    }

    @Override
    public void updateDisOrderStatusByDeviceIds(List<String> deviceIds, String status) {
        dao.updateDisOrderStatusByDeviceIds(deviceIds, status);
    }

    @Override
    public void batchSaveResourceDetail(List<ResourceDetailDTO> dtos) {
        for (ResourceDetailDTO dto : dtos) {
            dao.insert(convert.dto2do(dto));
        }
    }

    @Override
    public List<ResourceDetailDTO> list(ResourceDetailQuery query) {
        if (StringUtils.isBlank(query.getSourceType()) && CollectionUtils.isEmpty(query.getSourceTypeList())) {
            List<String> sourceTypeList = new ArrayList<>();
            sourceTypeList.add(SourceTypeEnum.STANDARD.getPrefix());
            sourceTypeList.add(SourceTypeEnum.NON_STANDARD.getPrefix());
            sourceTypeList.add(SourceTypeEnum.CORPORATE.getPrefix());
            sourceTypeList.add(SourceTypeEnum.XX.getPrefix());
            query.setSourceTypeList(sourceTypeList);
        }
        return StreamUtils.mapArray(dao.list(query), convert::do2dto);
    }

    @Override
    public List<ResourceDetailDTO> listByDeviceIds(List<String> deviceIds) {
        if (CollectionUtil.isEmpty(deviceIds)) {
            return Collections.emptyList();
        }
        List<ResourceDetailDO> resourceDetailDOS = dao.list(new ResourceDetailQuery().setDeviceIds(deviceIds));
        return convert.doList2dtoList(resourceDetailDOS);
    }

    @Override
    public List<ResourceDetailDTO> listCorporate(ResourceDetailQuery query) {
        if (StringUtils.isBlank(query.getSourceType())) {
            List<String> sourceTypeList = new ArrayList<>();
            sourceTypeList.add(SourceTypeEnum.CORPORATE.getPrefix());
            query.setSourceTypeList(sourceTypeList);
        }
        return StreamUtils.mapArray(dao.list(query), convert::do2dto);
    }

    @Override
    public PageResult<ResourceDetailDTO> page(ResourceDetailQuery query) {
        if (QueryParamCheckUtil.containsPercentage(query)) {
            return new PageResult();
        }
        String sourceType = query.getSourceType();
        if (StringUtils.isBlank(sourceType)) {
            List<String> sourceTypeList = new ArrayList<>();
            sourceTypeList.add(SourceTypeEnum.STANDARD.getPrefix());
            query.setSourceTypeList(sourceTypeList);
        }
        if ("DG".equals(sourceType)) {
            query.setSourceType(null);
            List<String> sourceTypeList = new ArrayList<>();
            sourceTypeList.add(SourceTypeEnum.CORPORATE.getPrefix());
            sourceTypeList.add(SourceTypeEnum.NON_STANDARD.getPrefix());
            query.setSourceTypeList(sourceTypeList);
        }
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<ResourceDetailDO> list = dao.list(query);
        if ((ProductTypeEnum.ECS.getCode().equals(query.getType())
                || ProductTypeEnum.GCS.getCode().equals(query.getType())
                || ProductTypeEnum.MYSQL.getCode().equals(query.getType())
                || ProductTypeEnum.POSTGRESQL.getCode().equals(query.getType()))
                && CollectionUtil.isNotEmpty(list)) {
            convertSecurityGroupName(list);
            packageVnic(list, sourceType);
        }
        return PageWarppers.box(new PageInfo<>(list), convert::do2dto);
    }

    @Override
    public PageResult<ResourceDetailDTO> pageEX(ResourceDetailQuery query) {
        if (QueryParamCheckUtil.containsPercentage(query)) {
            return new PageResult();
        }
        if (StringUtils.isBlank(query.getSourceType())) {
            query.setSourceType(SourceTypeEnum.EXTERNAL.getPrefix());
        }
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<ResourceDetailDO> list = dao.list(query);
        if (CollectionUtil.isNotEmpty(list)){
            for (ResourceDetailDO resourceDetailDO: list){
                resourceDetailDO.setId(resourceDetailDO.getGoodsOrderId());
            }
        }
        return PageWarppers.box(new PageInfo<>(list), convert::do2dto);
    }

    private void convertSecurityGroupName(List<ResourceDetailDO> list) {
        // 1. 收集所有安全组ID
        List<Long> securityGroupIds = list.stream()
                .filter(r -> StringUtils.isNotBlank(r.getSecurityGroupIds()))
                .flatMap(r -> Arrays.stream(r.getSecurityGroupIds().split(",")))
                .map(Long::valueOf)
                .distinct()  // 去重，避免重复查询
                .collect(Collectors.toList());
        // 2. 批量查询安全组信息
        if (CollectionUtil.isEmpty(securityGroupIds)) return;
        Map<Long, String> idToNameMap = securityGroupDao.getByIds(securityGroupIds)
                .stream()
                .collect(Collectors.toMap(
                        WocSecurityGroupDO::getId,
                        WocSecurityGroupDO::getName
                ));
        // 3. 设置安全组名称
        list.forEach(detail -> {
            if (StringUtils.isNotBlank(detail.getSecurityGroupIds())) {
                String names = Arrays.stream(detail.getSecurityGroupIds().split(","))
                        .map(id -> idToNameMap.getOrDefault(Long.valueOf(id), ""))
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.joining(","));
                detail.setSecurityGroupName(names);
            }
        });
    }

    private void packageVnic(List<ResourceDetailDO> list, String sourceType) {
        List<String> deviceIds = list.stream().map(ResourceDetailDO::getDeviceId).collect(Collectors.toList());
        VnicQuery vnicQuery = new VnicQuery();
        vnicQuery.setVmIds(deviceIds);
        vnicQuery.setSourceType(sourceType);
        List<VnicDO> vnicDOList = vnicDAO.list(vnicQuery);
        if (CollectionUtil.isNotEmpty(vnicDOList)) {
            Map<String, List<VnicDO>> map = vnicDOList.stream().collect(Collectors.groupingBy(VnicDO::getVmId));
            for (ResourceDetailDO resourceDetailDO : list) {
                List<VnicDO> vnicDOS = map.get(resourceDetailDO.getDeviceId());
                if (CollectionUtil.isNotEmpty(vnicDOS)) {
                    resourceDetailDO.setVnicId(vnicDOS.stream().map(VnicDO::getId).collect(Collectors.joining(",")));
                    resourceDetailDO.setVnicName(vnicDOS.stream().map(VnicDO::getVnicName).collect(Collectors.joining(",")));
                }
            }
        }
    }

    @Override
    public ResourceDetailDTO getByGoodsOrderId(Long id) {
        return convert.do2dto(dao.getByGoodsOrderId(id));
    }

    @Override
    public List<ResourceDetailDTO> selectOrderGoodsByType(List<String> businessIds, List<String> productTypes, Integer recoveryType) {
        List<ResourceDetailDO> dos = dao.selectOrderGoodsByType(businessIds, productTypes, recoveryType);
        return convert.doList2dtoList(dos);
    }

    @Override
    public ResourceDetailDTO getByDeviceId(String deviceId) {
        return convert.do2dto(dao.getByDeviceId(deviceId));
    }

    @Override
    public void updateConfigId(String configId, Long id, String manageIp) {
        dao.updateConfigId(configId, id, manageIp);
    }

    /**
     * 根据实例uuid和ip查询网卡信息
     *
     * @param instanceUuid
     * @param ip
     * @return
     */
    @Override
    public NetcardDetailDTO selectNetInfoByUuidAndIp(String instanceUuid, String ip) {
        return dao.selectNetInfoByUuidAndIp(instanceUuid, ip);
    }

    /**
     * 操作云主机
     *
     * @param operate
     */
    @Override
    public CommonResult operateVm(VmOperateQuery operate) {
        //修改密码
        if (VmOperationEnum.RESETPWD.getCode().equals(operate.getOperationType())) {
            ResetVmPwdDTO dto = dao.selectOperateVmResetPwd(operate);
            HttpResult post = OkHttps.sync(layoutCenter + layoutVmResetPwd)
                    .bodyType(OkHttps.JSON)
                    .setBodyPara(JSON.toJSONString(dto))
                    .post();
            if (Objects.isNull(post)) {
                return CommonResult.failure("云主机修改密码失败");
            }
            Mapper dataMapper = post.getBody().toMapper();
            String success = dataMapper.getString("success");
            Precondition.checkArgument("1".equals(success), "云主机修改密码失败，callLayoutOrder--编排中心初始化返回结果失败");
            return CommonResult.success("云主机修改密码成功");
        }
        //其他操作
        OperateVmDTO operateVmDTO = dao.selectOperateVm(operate);
        //重启操作需要校验是否处于开机状态
        if (VmOperationEnum.REBOOT.getAlias().equals(operate.getOperationType()) && !"RUNING".equals(operateVmDTO.getDeviceStatus())) {
            return CommonResult.failure("云主机需要开机才能重启");
        }
        if (VmOperationEnum.REBUILD_ECS.getAlias().equals(operate.getOperationType()) && "RUNING".equals(operateVmDTO.getDeviceStatus())) {
            return CommonResult.failure("云主机需要关机才能更换镜像");
        }
        if (VmOperationEnum.REBUILD_ECS.getAlias().equals(operate.getOperationType())) {
            if (StringUtils.isNotBlank(operateVmDTO.getVolumeId())) {
                return CommonResult.failure("云主机需要卸载数据盘才能更换镜像");
            }
            if (StringUtils.isNotBlank(operateVmDTO.getEip())) {
                return CommonResult.failure("云主机需要解绑弹性公网ip才能更换镜像");
            }
        }
        operateVmDTO.setImageId(operate.getImageId());
        HttpResult post = OkHttps.sync(layoutCenter + layoutVmOperate)
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(operateVmDTO))
                .post();
        if (Objects.isNull(post)) {
            return CommonResult.failure("云主机操作失败");
        }
        Mapper dataMapper = post.getBody().toMapper();
        String success = dataMapper.getString("success");
        Precondition.checkArgument("1".equals(success), "云主机操作失败，callLayoutOrder--编排中心初始化返回结果失败");
        //更新产品状态
        dao.updateDeviceStatus(operate.getOperationType() + "ING", operateVmDTO.getInstanceId());
        String desc = VmOperationEnum.getByAlias(operateVmDTO.getOperationType()).getDesc();
        return CommonResult.success(desc + "中，请稍后查看");
    }

    @Override
    public List<Long> selectBusinessIdByGoodsIds(List<String> goodsIds) {
        return dao.selectBusinessIdByGoodsIds(goodsIds);
    }

    @Override
    public List<ResourceDetailDTO> selectByGoodsIds(List<String> goodsIdList) {
        List<ResourceDetailDO> detailDOS = dao.selectByGoodsIds(goodsIdList);
        return convert.doList2dtoList(detailDOS);
    }

    @Override
    public ResourceDetailDTO getById(Long id) {
        ResourceDetailDO resourceDetailDO = dao.selectById(id);
        if (Objects.isNull(resourceDetailDO)) {
            return null;
        }
        List<ResourceDetailDO> list = ListUtil.toList(resourceDetailDO);
        convertSecurityGroupName(list);
        if ("BZ".equals(resourceDetailDO.getSourceType())) {
            resourceDetailDO.setSourceType(null);
        }
        packageVnic(list, resourceDetailDO.getSourceType());
        return convert.do2dto(list.get(0));
    }

    @Override
    public void deleteById(Long id) {
        dao.deleteById(id);
    }

    @Override
    public List<ResourceDetailDTO> selectRecoveryStatusByIds(List<String> ids, Integer recoveryStatus, Integer status) {
        List<ResourceDetailDO> detailDOS = dao.selectRecoveryStatusByIds(ids, recoveryStatus, status);
        return convert.doList2dtoList(detailDOS);
    }

    @Override
    public String selectByBusinessSystemId(Long businessSystemId) {
        return dao.selectByBusinessSystemId(businessSystemId);
    }

    @Override
    public List<String> selectRegionCodeByOrderId(String orderId) {
        return dao.selectRegionCodeByOrderId(orderId);}

    public List<ModuleOfflineDTO> selectNotUserModule(Long businessSysId) {
        return dao.selectNotUserModule(businessSysId);
    }

    @Override
    public ResourceDetailDTO getConfigId(String configId) {
        return convert.do2dto(dao.getConfigId(configId));
    }

    @Override
    public void updateChangeStatusByIds(List<Long> resourceIds, String changeStatus) {
        if (CollectionUtil.isNotEmpty(resourceIds)) {
            dao.updateChangeStatusByIds(resourceIds, changeStatus);
        }
    }

    @Override
    public void updateChangeStatusByDeviceIds(List<String> deviceIds, String changeStatus) {
        if (CollectionUtil.isNotEmpty(deviceIds)) {
            dao.updateChangeStatusByDeviceIds(deviceIds, changeStatus);
        }
    }

    public void updateEipInfoByEipId(String eipId, String eip, String bandWidth) {
        dao.updateEipInfoByEipId(eipId, eip, bandWidth);
    }

    @Override
    public void evsAddEipInfoByDeviceId(String deviceId, String eipId, String eip, String bandWidth) {
        dao.evsAddEipInfoByDeviceId(deviceId, eipId, eip, bandWidth);
    }

    @Override
    public void clearEipInfoByEipId(String eipId) {
        dao.clearEipInfoByEipId(eipId);
    }

    @Override
    public void clearBackupInfoById(Long id) {
        dao.clearBackupInfoById(id);
    }

    @Override
    public void clearEipInfoById(Long id){
        dao.clearEipInfoById(id);
    }


    @Override
    public void clearRelatedInfoById(Long id) {
        dao.clearRelatedInfoById(id);
    }

    @Override
    public void updateEvsInfoById(ResourceDetailDTO dto) {
        dao.updateEvsInfoById(convert.dto2do(dto));
    }

    @Override
    public void updateHandoverStatusByConfigId(String configId, String handoverStatus) {
        dao.updateHandoverStatusByConfigId(configId, handoverStatus);
    }

    @Override
    public List<ResourceDetailDTO> selectByExpireTime(String time, String endTime) {
        List<ResourceDetailDO> detailDOS = dao.selectByExpireTime(time, endTime);
        return convert.doList2dtoList(detailDOS);
    }


    @Override
    public List<ResourceDetailDTO> selectByExpireTimeThreeDay() {
        List<ResourceDetailDO> detailDOS = dao.selectByExpireTimeThreeDay();
        return convert.doList2dtoList(detailDOS);
    }


    @Override
    public List<ResourceDetailDTO> selectExpireDetail() {
        List<ResourceDetailDO> detailDOS = dao.selectExpireDetail();
        return convert.doList2dtoList(detailDOS);
    }
}
