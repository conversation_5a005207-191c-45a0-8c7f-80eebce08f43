package com.datatech.slgzt.model.vo.device;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @program: workordercenterproject
 * @description: 虚拟卡设备信息导出VO
 * @author: LK
 * @create: 2025-08-12 15:34
 **/
@Data
public class DeviceVirtualGpuInfoExportVO {

//    @ExcelExportHeader("归属平台")
//    private String areaCode;

    @ExcelExportHeader("归属平台")
    private String regionName;

    @ExcelExportHeader("归属业务")
    private String businessSystemName;

//    @ExcelExportHeader("所属部门")
//    private String deptName;
//
//    @ExcelExportHeader("设备ID")
//    private String deviceId;

    //算力利用率
    @ExcelExportHeader("算力利用率(%)")
    private BigDecimal gpuUtilPercent;

    //显存利用率
    @ExcelExportHeader("显存利用率(%)")
    private BigDecimal memUtilpercent;

    //显存大小
    @ExcelExportHeader("显存大小(G)")
    private BigDecimal memory;

    //任务数
    @ExcelExportHeader("任务数")
    private Integer taskNum;

    //运行状态
    @ExcelExportHeader("运行状态")
    private String runStatus;

    @ExcelExportHeader(value = "底层采集状态")
    private String collectStatus;
}
