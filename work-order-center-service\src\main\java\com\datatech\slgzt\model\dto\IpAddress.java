package com.datatech.slgzt.model.dto;

import lombok.Data;

import java.util.Date;

/**
* IP表
* <AUTHOR>
* @date 2025-01-13 09:51:05
**/
@Data
public class IpAddress implements java.io.Serializable{

	private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    private String id;
    /**
     * ip地址
     */
    private String ip;
    /**
     * ip名称
     */
    private String name;
    /**
     * 描述
     */
    private String description;
    /**
     * 云区域id
     */
    private Long regionId;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * vdcCode
     */
    private String vdcCode;
    /**
     * 项目ID
     */
    private String projectId;
    /**
     * 设备ID
     */
    private String deviceId;
    /**
     * 网络ID
     */
    private String networkId;
    /**
     * 子网ID
     */
    private String subnetId;
    /**
     * EIP ：弹性ip,BUSINESS(云主机返回的ipv4地址)
     */
    private String type;
    /**
     * VM : 虚拟机
     */
    private String deviceType;
    /**
     * 网关ip
     */
    private String gateway;
    /**
     * 端口/网卡ID
     */
    private String portId;
    /**
     * mac地址
     */
    private String mac;
    /**
     * 底层id
     */
    private String resourceId;
    /**
     * 创建时间
     */
    private Date createdTime;
    /**
     * 修改时间
     */
    private Date updatedTime;
    /**
     * 1生效，0失效
     */
    private Long deleted;

    /**
     * 带宽
     */
    private Long bandwidth;

    /**
     * eip的全局唯一编码
     */
    private String gId;

    private Date syncUpdatedTime;

    /**
     * normal或direct
     */
    private String nicType;

    /**
     * 可使用的入向最大带宽
     */
    private String ingerss;

    /**
     * 可使用的出向最大带宽
     */
    private String engerss;

    /**
     * 小云管底层实例id
     */
    private String instanceUuid;

    private String vpcId;

    private String subnetName;

}