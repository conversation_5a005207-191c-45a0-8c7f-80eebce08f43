package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.CmdbIP3StatusMapper;
import com.datatech.slgzt.dao.model.CmdbIP3StatusDO;
import com.datatech.slgzt.model.query.NetworkCmdbQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 网络cmdb DAO
 * <AUTHOR>
 */
@Repository
public class CmdbIP3StatusDAO {

    @Autowired
    private CmdbIP3StatusMapper mapper;

    public List<CmdbIP3StatusDO> selectList(NetworkCmdbQuery query) {
        return mapper.selectList(Wrappers.<CmdbIP3StatusDO>lambdaQuery()
                .eq(CmdbIP3StatusDO::getStatus, 1)
                .eq(ObjNullUtils.isNotNull(query.getSubnetOpenStatus()), CmdbIP3StatusDO::getSubnetOpenStatus, query.getSubnetOpenStatus())
                .ne(ObjNullUtils.isNotNull(query.getSubnetOpenStatusNotEquals()), CmdbIP3StatusDO::getSubnetOpenStatus, query.getSubnetOpenStatusNotEquals())
                .ge(ObjNullUtils.isNotNull(query.getCreateTimeStart()), CmdbIP3StatusDO::getCreateTime, query.getCreateTimeStart())
                .le(ObjNullUtils.isNotNull(query.getCreateTimeEnd()), CmdbIP3StatusDO::getCreateTime, query.getCreateTimeEnd())
        );
    }

    public void update(CmdbIP3StatusDO cmdbIP3StatusDO) {
        cmdbIP3StatusDO.setUpdateTime(LocalDateTime.now());
        cmdbIP3StatusDO.setStatus(null);
        mapper.updateById(cmdbIP3StatusDO);
    }


    public void updateSubnetOpenStatus(String instanceId, Integer subnetOpenStatus, Integer oldSubnetOpenStatus) {
        LambdaUpdateWrapper<CmdbIP3StatusDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(CmdbIP3StatusDO::getSubnetOpenStatus, subnetOpenStatus)
                .set(CmdbIP3StatusDO::getUpdateTime, LocalDateTime.now())
                .eq(CmdbIP3StatusDO::getInstanceId, instanceId)
                .eq(oldSubnetOpenStatus != null, CmdbIP3StatusDO::getSubnetOpenStatus, oldSubnetOpenStatus);
        mapper.update(null, updateWrapper);
    }

    public void insert(CmdbIP3StatusDO cmdbIP3StatusDO) {
        LocalDateTime now = LocalDateTime.now();
        cmdbIP3StatusDO.setStatus(1);
        cmdbIP3StatusDO.setCreateTime(now);
        cmdbIP3StatusDO.setUpdateTime(now);
        mapper.insert(cmdbIP3StatusDO);
    }

    public CmdbIP3StatusDO selectById(String instanceId) {
        return mapper.selectOne(Wrappers.<CmdbIP3StatusDO>lambdaQuery()
                .eq(CmdbIP3StatusDO::getInstanceId, instanceId)
                .eq(CmdbIP3StatusDO::getStatus, 1)
        );
    }

    public int deleteById(String instanceId) {
        CmdbIP3StatusDO cmdbIP3StatusDO = selectById(instanceId);
        if (cmdbIP3StatusDO == null) {
            return 0;
        }
        LocalDateTime now = LocalDateTime.now();
        cmdbIP3StatusDO.setStatus(0);
        cmdbIP3StatusDO.setUpdateTime(now);
        cmdbIP3StatusDO.setDeleteTime(now);
        return mapper.updateById(cmdbIP3StatusDO);
    }

    public int cancelById(String instanceId) {
        CmdbIP3StatusDO cmdbIP3StatusDO = selectById(instanceId);
        if (cmdbIP3StatusDO == null) {
            return 0;
        }
        LocalDateTime now = LocalDateTime.now();
        cmdbIP3StatusDO.setSubnetOpenStatus(-2);
        cmdbIP3StatusDO.setStatus(0);
        cmdbIP3StatusDO.setUpdateTime(now);
        cmdbIP3StatusDO.setDeleteTime(now);
        return mapper.updateById(cmdbIP3StatusDO);
    }
}
