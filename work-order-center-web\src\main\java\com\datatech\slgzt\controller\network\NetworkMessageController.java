package com.datatech.slgzt.controller.network;


import com.datatech.slgzt.annotation.Lock;
import com.datatech.slgzt.enums.SourceTypeEnum;
import com.datatech.slgzt.manager.VirtualIpManager;
import com.datatech.slgzt.model.AllocationPool;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.callback.YunshuCallback;
import com.datatech.slgzt.model.dto.network.*;
import com.datatech.slgzt.model.vo.network.AvailableIpReqVO;
import com.datatech.slgzt.model.vo.network.CheckIpAvailableReqVO;
import com.datatech.slgzt.model.vo.network.NetworkOrderResult;
import com.datatech.slgzt.model.vo.network.NetworkTableVo;
import com.datatech.slgzt.model.vo.vpc.NetworkSubnetOrderResult;
import com.datatech.slgzt.service.network.NetworkMessageService;
import com.datatech.slgzt.utils.IpUtils;
import com.datatech.slgzt.utils.Precondition;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 网络资源管理
 *
 * @Author: liuzhenjun
 * @Date: 2024/11/18
 * @Description: 校验网络开通时网络信息校验
 */

@RestController
@RequestMapping("/network")
public class NetworkMessageController {

    @Resource
    private NetworkMessageService networkMessageService;

    @Resource
    private VirtualIpManager virtualIpManager;

    /**
     * 获取network管理列表
     *
     * @param networkTableVo networkTableVo
     * @return
     */
    @PostMapping("/networkTree")
    public CommonResult<List<NetworkOrderResult>> selectNetworkList(@RequestBody NetworkTableVo networkTableVo) {
        Precondition.checkArgument(networkTableVo.getRegionCode(), "资源池编号不能为空");
        Precondition.checkArgument(networkTableVo.getOrderId(), "工单编号不能为空");
        return networkMessageService.selectNetworkList(networkTableVo);
    }

    /**
     * 创建network
     *
     * @param networkCreateDTO networkOrderDTO
     * @return
     */
    @PostMapping("/networkCreate")
    @Lock(prefixKey = "key-networkCreate", waitTime = 15000, key = {"#networkCreateDTO.orderId"})
    public CommonResult<String> networkCreate(@RequestBody NetworkCreateDTO networkCreateDTO) {
        networkCreateDTO.setSourceType(SourceTypeEnum.STANDARD.getPrefix());
        return networkMessageService.networkCreate(networkCreateDTO);
    }

    /**
     * 批量创建network
     *
     * @param networkCreateDTO networkOrderDTO
     * @return
     */
    @PostMapping("/networkCreateBatch")
    public CommonResult<String> networkCreateBatch(@RequestBody NetworkCreateDTO networkCreateDTO) {
        Precondition.checkArgument(networkCreateDTO.getTenantId(), "租户ID不能为空");
        Precondition.checkArgument(networkCreateDTO.getBusinessSysId(), "业务系统ID不能为空");
        Precondition.checkArgument(networkCreateDTO.getBusinessSysName(), "业务系统名称不能为空");
        Precondition.checkArgument(networkCreateDTO.getRegionCode(), "资源池不能为空");
        Precondition.checkArgument(networkCreateDTO.getModuleId(), "所属业务模块不能为空");
        Precondition.checkArgument(networkCreateDTO.getModuleName(), "所属业务模块不能为空");
        for (NetworkOrderDTO network : networkCreateDTO.getNetworks()) {
            if (network.getSubnets() != null) {
                for (SubnetDTO subnet : network.getSubnets()) {
                    checkAllocationPools(subnet);
                }
            }
        }
        return networkMessageService.networkCreateBatch(networkCreateDTO);
    }

    /**
     * 独立开通子网
     * @param networkOrderDTO
     * @return
     */
    @PostMapping("/createSubnetWork")
    public CommonResult<String> createSubnetWork(@RequestBody NetworkOrderDTO networkOrderDTO){
        Precondition.checkArgument(networkOrderDTO.getNetworkId(), "网络ID不能为空");
        if (networkOrderDTO.getSubnets() != null) {
            for (SubnetDTO subnet : networkOrderDTO.getSubnets()) {
                checkAllocationPools(subnet);
            }
        }
        return networkMessageService.createSubnetWork(networkOrderDTO);
    }

    /**
     * 获取网络状态
     *
     * @param orderId orderId
     * @return
     */
    @GetMapping("/getNetworkStatus")
    @Lock(prefixKey = "key-getNetworkStatus", waitTime = 15000, key = {"#orderId"})
    public CommonResult<String> getNetworkStatus(@RequestParam String orderId) {
        return networkMessageService.getNetworkStatus(orderId);
    }

    /**
     * 获取网络详情
     *
     * @param networkId networkId
     * @return
     */
    @GetMapping("/getNetworkDetail")
    public CommonResult<NetworkDetailDTO> getNetworkDetail(@RequestParam String networkId) {
        return networkMessageService.getNetworkDetail(networkId);
    }

    /**
     * 云枢回掉接口
     *
     * @param yunshuCallback yunshuCallback
     * @return
     */
    @PostMapping("/callback")
    public CommonResult<String> callback(@RequestBody YunshuCallback yunshuCallback) {
        return networkMessageService.callback(yunshuCallback);
    }

    /**
     * 网络回收
     *
     * @param list 入参
     * @return
     */
    @PostMapping("/networkRecycle")
    public CommonResult networkRecycle(@RequestBody @Validated List<NetworkConfirmDTO> list) {
        networkMessageService.networkRecycle(list);
        return CommonResult.success();
    }

    /**
     * 根据网络id查询子网信息
     * @param networkId
     * @return
     */
    @GetMapping("/getSubnetsByNetworkId")
    public CommonResult<List<NetworkSubnetOrderResult>> getSubnetsByNetworkId(@RequestParam String networkId) {
        return CommonResult.success(networkMessageService.selectSubnetsByNetworkId(networkId));
    }

    /**
     * 获取可用IP列表
     *
     * @param availableIpDTO 请求参数对象，包含子网ID、过滤IP列表和IP数量
     * @return 可用IP列表和IP版本
     */
    @PostMapping("/getAvailableIp")
    public CommonResult<AvailableIpDTO> getAvailableIp(@RequestBody AvailableIpReqVO availableIpDTO) {
        Precondition.checkArgument(availableIpDTO.getSubnetId(), "子网ID不能为空");
        Precondition.checkArgument(availableIpDTO.getCount(), "IP数量不能为空");
        Precondition.checkArgument(availableIpDTO.getCount() > 0, "IP数量必须大于0");

        AvailableIpDTO result = virtualIpManager.getAvailableIp(
            availableIpDTO.getSubnetId(),
            availableIpDTO.getFilterIps(),
            availableIpDTO.getCount()
        );
        Precondition.checkArgument(availableIpDTO.getCount().equals(result.getAvailableIps().size()), "IP数量不足");
        return CommonResult.success(result);
    }

    /**
     * 校验IP是否可用
     *
     * @param checkIpAvailableDTO 请求参数对象，包含子网ID和IP地址
     * @return 错误信息，如果为null表示可用
     */
    @PostMapping("/checkIpAvailable")
    public CommonResult<String> checkIpAvailable(@RequestBody CheckIpAvailableReqVO checkIpAvailableDTO) {        
        String result = virtualIpManager.checkIpAvailable(checkIpAvailableDTO.getSubnetId(), checkIpAvailableDTO.getIpAddress());        
        return CommonResult.success(result);
    }

    private void checkAllocationPools(SubnetDTO subnetDTO) {
        String cidr = subnetDTO.getCidr();
        if (subnetDTO.getAllocationPools() == null) {
            return;
        }
        for (AllocationPool allocationPool : subnetDTO.getAllocationPools()) {
            Precondition.checkArgument(
                    allocationPool.getStartIp() != null && IpUtils.isIpInCidrAuto(allocationPool.getStartIp(), cidr),
                    String.format("起始ip(%s)不在网段(%s)中", allocationPool.getStartIp(), cidr)
            );
            Precondition.checkArgument(
                    allocationPool.getEndIp() != null && IpUtils.isIpInCidrAuto(allocationPool.getEndIp(), cidr),
                    String.format("终止ip:(%s)不在网段(%s)中", allocationPool.getEndIp(), cidr)
            );
            if (allocationPool.getStartIp() != null && allocationPool.getEndIp() != null) {
                // 如果是ipv4
                if ("IPv4".equals(subnetDTO.getIpVersion())) {
                    Precondition.checkArgument(
                            IpUtils.ipv4ToLong(allocationPool.getStartIp()) < IpUtils.ipv4ToLong(allocationPool.getEndIp()),
                            String.format("起始ip(%s)不能大于终止ip(%s)", allocationPool.getStartIp(), allocationPool.getEndIp())
                    );
                } else if ("IPv6".equals(subnetDTO.getIpVersion())) {
                    Precondition.checkArgument(
                            IpUtils.ipv6ToBigInteger(allocationPool.getStartIp()).compareTo(IpUtils.ipv6ToBigInteger(allocationPool.getEndIp())) < 0,
                            String.format("起始ip(%s)不能大于终止ip(%s)", allocationPool.getStartIp(), allocationPool.getEndIp())
                    );
                }
            }
        }
    }

}

