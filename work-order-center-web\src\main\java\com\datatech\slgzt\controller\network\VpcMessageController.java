package com.datatech.slgzt.controller.network;


import com.datatech.slgzt.annotation.Lock;
import com.datatech.slgzt.enums.SourceTypeEnum;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.AzManager;
import com.datatech.slgzt.manager.RegionManager;
import com.datatech.slgzt.manager.TenantManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.business.CmpAppDTO;
import com.datatech.slgzt.model.dto.TenantDTO;
import com.datatech.slgzt.model.dto.business.BusinessService;
import com.datatech.slgzt.model.dto.network.AllocationPoolDTO;
import com.datatech.slgzt.model.dto.network.NetworkDetailDTO;
import com.datatech.slgzt.model.dto.vpc.*;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.model.vo.vpc.VpcOrderResult;
import com.datatech.slgzt.model.vo.vpc.VpcSubnetOrderResult;
import com.datatech.slgzt.model.vo.vpc.VpcTableVo;
import com.datatech.slgzt.service.network.VpcExternalService;
import com.datatech.slgzt.service.network.VpcMessageService;
import com.datatech.slgzt.utils.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * vpc资源管理
 *
 * @Author: liuzhenjun
 * @Date: 2024/11/18
 * @Description: 校验网络开通时vpc信息校验
 */

@RestController
@RequestMapping("/vpc")
public class VpcMessageController {

    @Resource
    private VpcMessageService vpcMessageService;
    @Resource
    private VpcExternalService vpcExternalService;
    @Resource
    private TenantManager tenantManager;

    @Resource
    private RegionManager regionManager;


    @Resource
    private AzManager azManager;

    @Resource
    private BusinessService businessService;


    /**
     * 获取vpc管理列表
     *
     * @param vpcTableVo vpcTableVo
     * @return
     */
    @PostMapping("/vpcTree")
    public CommonResult<List<VpcOrderResult>> selectVpcList(@RequestBody VpcTableVo vpcTableVo) throws JsonProcessingException {
        Precondition.checkArgument(vpcTableVo.getRegionCode(), "资源池编号不能为空");
        Precondition.checkArgument(vpcTableVo.getOrderId(), "工单编号不能为空");
        Precondition.checkArgument(vpcTableVo.getAzCode(), "可用区编号不能为空");
        return vpcMessageService.selectVpcList(vpcTableVo);
    }


    /**
     * 获取vpc管理列表
     *
     * @param vpcTableVo vpcTableVo
     * @return
     */
    @PostMapping("/corporateVpcTree")
    public CommonResult<List<VpcOrderResult>> corporateVpcTree(@RequestBody VpcTableVo vpcTableVo) throws JsonProcessingException {
        return vpcMessageService.corporateVpcTree(vpcTableVo);
    }


    /**
     * 批量创建VPC
     *
     * @param vpcCreateDTO vpcCreateDTO
     * @return
     */
    @PostMapping("/vpcCreateBatch")
    @Lock(prefixKey = "key-vpcCreateBatch", waitTime = 15000, key = {"#vpcCreateDTO.tenantId"})
    public CommonResult<String> vpcCreateBatch(@RequestBody VpcCreateDTO vpcCreateDTO) {
        Precondition.checkArgument(vpcCreateDTO.getTenantId(), "租户ID不能为空");
        Precondition.checkArgument(vpcCreateDTO.getBusinessSysId(), "业务系统ID不能为空");
        Precondition.checkArgument(vpcCreateDTO.getBusinessSysName(), "业务系统名称不能为空");
        Precondition.checkArgument(vpcCreateDTO.getRegionCode(), "资源池不能为空");
        Precondition.checkArgument(vpcCreateDTO.getModuleId(), "所属业务模块不能为空");
        Precondition.checkArgument(vpcCreateDTO.getModuleName(), "所属业务模块不能为空");
        vpcCreateDTO.setSourceType(SourceTypeEnum.STANDARD.getPrefix());
        for (VpcOrderDTO network : vpcCreateDTO.getNetworks()) {
            if(network.getSubnetDTOList()!=null){
                for (VpcSubnetDTO vpcSubnetDTO : network.getSubnetDTOList()) {
                    checkAllocationPools(vpcSubnetDTO);
                }
            }
        }
        return vpcMessageService.vpcCreateBatch(vpcCreateDTO);
    }

    /**
     * 批量创建VPC
     *
     * @param vpcCreateDTO vpcCreateDTO
     * @return
     */
    @PostMapping("/vpcCreateBatchDG")
    @Lock(prefixKey = "key-vpcCreateBatch", waitTime = 15000, key = {"#vpcCreateDTO.tenantId"})
    public CommonResult<String> vpcCreateBatchDG(@RequestBody VpcCreateDTO vpcCreateDTO) {
        Precondition.checkArgument(vpcCreateDTO.getTenantId(), "租户ID不能为空");
        Precondition.checkArgument(vpcCreateDTO.getRegionCode(), "资源池不能为空");
        //通过登录用户获取用户的业务系统
        TenantDTO tenantDTO = tenantManager.getById(vpcCreateDTO.getTenantId());
        Precondition.checkArgument(ObjNullUtils.isNotNull(tenantDTO), "用户没有租户");
        CmpAppDTO cmpAppDTO = businessService.selectByTenantId(tenantDTO.getId());
        Precondition.checkArgument(ObjNullUtils.isNotNull(cmpAppDTO), "用户没有业务系统");
        vpcCreateDTO.setBusinessSysId(cmpAppDTO.getSystemId());
        vpcCreateDTO.setBusinessSysName(cmpAppDTO.getSystemName());
        vpcCreateDTO.setSourceType(SourceTypeEnum.CORPORATE.getPrefix());
        for (VpcOrderDTO network : vpcCreateDTO.getNetworks()) {
            if(network.getSubnetDTOList()!=null){
                for (VpcSubnetDTO vpcSubnetDTO : network.getSubnetDTOList()) {
                    checkAllocationPools(vpcSubnetDTO);
                }
            }
        }
        return vpcMessageService.vpcCreateBatch(vpcCreateDTO);
    }

    /**
     * 创建VPC
     *
     * @param vpcCreateDTO vpcCreateDTO
     * @return
     */
    @PostMapping("/vpcCreate")
    @Lock(prefixKey = "key-vpcCreate", waitTime = 15000, key = {"#vpcCreateDTO.orderId"})
    public CommonResult<String> vpcCreate(@RequestBody VpcCreateDTO vpcCreateDTO) {
        Precondition.checkArgument(vpcCreateDTO.getRegionCode(), "资源池不能为空");
        Precondition.checkArgument(vpcCreateDTO.getOrderId(), "工单编号不能为空");
        return vpcMessageService.vpcCreate(vpcCreateDTO);
    }

    /**
     * 批量创建VPC子网
     *
     * @param vpcOrderDTO vpcOrderDTO
     * @return
     */
    @PostMapping("/createVpcSubnetBatch")
    @Lock(prefixKey = "key-createVpcSubnetBatch", waitTime = 15000, key = {"#vpcOrderDTO.vpcId"})
    public CommonResult<String> createVpcSubnetBatch(@RequestBody VpcOrderDTO vpcOrderDTO) {
        Precondition.checkArgument(vpcOrderDTO.getVpcId(), "vpcId不能为空");
        for (VpcSubnetDTO vpcSubnetDTO : vpcOrderDTO.getSubnetDTOList()) {
            checkAllocationPools(vpcSubnetDTO);
        }

        StringBuilder sb = new StringBuilder();
        for (VpcSubnetDTO vpcSubnetDTO : vpcOrderDTO.getSubnetDTOList()) {
            CommonResult<VpcSubnetCreateDTO> commonResult = vpcMessageService.createSubnet(vpcSubnetDTO, vpcOrderDTO.getVpcId());
            if (200 != commonResult.getCode()){
                sb.append(vpcSubnetDTO.getSubnetName()).append(": ").append(commonResult.getMessage()).append("<br />");
            }
        }
        vpcMessageService.updateDetail(vpcOrderDTO);
        if (StringUtils.isNotEmpty(sb.toString())){
            return CommonResult.failure(sb.toString());
        }
        return CommonResult.success("vpc子网创建下发成功");
    }

    /**
     * 创建VPC子网，外部接口
     *
     * @param subnetCreateDTO subnetCreateDTO
     * @return
     */
    @PostMapping("/createVpcSubnet")
    @Lock(prefixKey = "key-createVpcSubnet", waitTime = 15000, key = {"#subnetCreateDTO.vpcId"})
    public CommonResult<VpcSubnetCreateDTO> createVpcSubnet(@RequestBody VpcSubnetCreateDTO subnetCreateDTO) {
        Precondition.checkArgument(subnetCreateDTO.getAzCode(), "可用区不能为空");
        Precondition.checkArgument(azManager.getByCode(subnetCreateDTO.getAzCode()), "未查询到可用区");
        Precondition.checkArgument(subnetCreateDTO.getSubnetName(), "子网名称不能为空");
        Precondition.checkArgument(subnetCreateDTO.getStartIp(), "开始IP不能为空");
        Precondition.checkArgument(subnetCreateDTO.getNetmask(), "子网掩码不能为空");
        if (subnetCreateDTO.getAllocationPools() != null) {
            String cidr = subnetCreateDTO.getStartIp() + "/" + subnetCreateDTO.getNetmask();
            for (AllocationPoolDTO allocationPool : subnetCreateDTO.getAllocationPools()) {
                Precondition.checkArgument(
                        allocationPool.getStartIp() != null && IpUtils.isIpInCidr(allocationPool.getStartIp(), cidr),
                        String.format("起始ip(%s)不在网段(%s)中", allocationPool.getStartIp(), cidr)
                );
                Precondition.checkArgument(
                        allocationPool.getEndIp() != null && IpUtils.isIpInCidr(allocationPool.getEndIp(), cidr),
                        String.format("终止ip:(%s)不在网段(%s)中", allocationPool.getEndIp(), cidr)
                );
                if (allocationPool.getStartIp() != null && allocationPool.getEndIp() != null) {
                    Precondition.checkArgument(
                            IpUtils.ipv4ToLong(allocationPool.getStartIp()) < IpUtils.ipv4ToLong(allocationPool.getEndIp()),
                            String.format("起始ip(%s)不能大于终止ip(%s)", allocationPool.getStartIp(), allocationPool.getEndIp())
                    );
                }
            }
        }
        return vpcExternalService.createSubnet(subnetCreateDTO);
    }

    /**
     * 获取vpc状态，校验vpc
     *
     * @param orderId orderId
     * @return
     */
    @GetMapping("/getVpcStatus")
    @Lock(prefixKey = "key-getVpcStatus", waitTime = 15000, key = {"#orderId"})
    public CommonResult<String> getVpcStatus(@RequestParam String orderId) {
        return vpcMessageService.getVpcStatus(orderId);
    }

    /**
     * 获取Vpc详情
     *
     * @param vpcId vpcId
     * @return
     */
    @GetMapping("/getVpcDetail")
    public CommonResult<NetworkDetailDTO> getVpcDetail(@RequestParam String vpcId) {
        return vpcMessageService.getVpcDetail(vpcId);
    }

    /**
     * 创建VPC和子网，外部接口
     *
     * @param vpcOrderDTO vpcOrderDTO
     * @return CommonResult
     */
    @PostMapping("/createVpcAndSubnet")
    public CommonResult<String> createVpcAndSubnet(@RequestBody VpcOrderDTO vpcOrderDTO) {
        Precondition.checkArgument(vpcOrderDTO.getTenantId(), "租户ID不能为空");
        Precondition.checkArgument(tenantManager.getById(vpcOrderDTO.getTenantId()), "未查询到租户信息");
        Precondition.checkArgument(vpcOrderDTO.getVpcName(), "vpc名称不能为空");
        Precondition.checkArgument(vpcOrderDTO.getCidr(), "vpc网段不能为空");
        Precondition.checkArgument(vpcOrderDTO.getRegionCode(), "资源池不能为空");
        Precondition.checkArgument(regionManager.getByCode(vpcOrderDTO.getRegionCode()), "未查询到租户信息");
        Precondition.checkArgument(vpcOrderDTO.getSubnetDTOList(), "子网信息不能为空");
        for (VpcSubnetDTO vpcSubnetDTO : vpcOrderDTO.getSubnetDTOList()) {
            Precondition.checkArgument(vpcSubnetDTO.getAzCode(), "可用区不能为空");
            Precondition.checkArgument(azManager.getByCode(vpcSubnetDTO.getAzCode()), "未查询到可用区");
            Precondition.checkArgument(vpcSubnetDTO.getSubnetName(), "子网名称不能为空");
            Precondition.checkArgument(vpcSubnetDTO.getStartIp(), "开始IP不能为空");
            Precondition.checkArgument(vpcSubnetDTO.getNetmask(), "子网掩码不能为空");
            String cidr = vpcSubnetDTO.getStartIp() + "/" + vpcSubnetDTO.getNetmask();
            Precondition.checkArgument(IPInSubnetChecker.isSubnetOf(cidr, vpcOrderDTO.getCidr()), "子网网段不在vpc网段内");
        }
        return vpcExternalService.createVpcAndSubnet(vpcOrderDTO);
    }


    /**
     * 删除VPC，外部接口
     *
     * @param list list
     * @return CommonResult
     */
    @RequestMapping(value = "/vpcDelete", method = RequestMethod.DELETE)
    public CommonResult<String> vpcDelete(@RequestBody List<String> list) {
        Long userId = UserHelper.INSTANCE.getCurrentUserId();
        return vpcExternalService.vpcDelete(list, userId);
    }

    /**
     * 获取vpc管理列表，外部接口
     *
     * @param vpcTableVo vpcTableVo
     * @return
     */
    @RequestMapping(value = "/queryVpc", method = RequestMethod.GET)
    public CommonResult<List<VpcOrderResult>> queryVpc(VpcTableVo vpcTableVo) {
        Precondition.checkArgument(vpcTableVo.getRegionCode(), "资源池编号不能为空");
        Precondition.checkArgument(vpcTableVo.getTenantId(), "租户id不能为空");
        return vpcMessageService.queryVpc(vpcTableVo);
    }


    /**
     * 获取vpc列表，外部接口
     *
     * @param vpcTableVo vpcTableVo
     * @return
     */
    @RequestMapping(value = "/queryVpcPage", method = RequestMethod.GET)
    public CommonResult<PageResult<VpcOrderResult>> queryVpcPage(VpcTableVo vpcTableVo) {
        UserCenterUserDTO oacUserInfo = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(oacUserInfo, "用户未登录");
      /*  List<Long> tenantIds = null;
        if (null != vpcTableVo.getTenantId()) {
            tenantIds = new ArrayList<>();
            tenantIds.add(Long.valueOf(vpcTableVo.getTenantId()));
        } else {
            //获取当前用户角色
            tenantIds = tenantManager.listTenIdByOwnerId(oacUserInfo.getId());
            if (ObjNullUtils.isNull(tenantIds)) {
                return CommonResult.success(new PageResult<>());
            }
        }
        vpcTableVo.setTenantIds(tenantIds);*/
        return CommonResult.success(vpcMessageService.queryVpcListPage(vpcTableVo));
    }

    /**
     * 网络回收
     *
     * @param list 入参
     * @return
     */
    @RequestMapping(value = "/deleteVpc", method = RequestMethod.DELETE)
    public CommonResult deleteVpc(@RequestBody List<String> list) {
        Long userId = UserHelper.INSTANCE.getCurrentUserId();
        vpcExternalService.vpcDelete(list, userId);
        return CommonResult.success();
    }

    /**
     * 网络回收
     *
     * @param deleteDTO 入参
     * @return
     */
    @RequestMapping(value = "/deleteVpcSubnet", method = RequestMethod.DELETE)
    public CommonResult deleteVpcSubnet(@RequestBody VpcSubnetDeleteDTO deleteDTO) {
        vpcExternalService.vpcSubnetDelete(deleteDTO);
        return CommonResult.success();
    }

    /**
     * 根据vpcId查询子网id
     * @param vpcId
     * @return
     */
    @GetMapping("/getSubnetsByVpcId")
    public CommonResult<List<VpcSubnetOrderResult>> listSubnets(@RequestParam String vpcId) {
        return CommonResult.success(vpcExternalService.getSubnetsByVpcId(vpcId));
    }

    private void checkAllocationPools(VpcSubnetDTO subnetDTO) {
        String cidr = subnetDTO.getStartIp() + "/" + subnetDTO.getNetmask();
        if (subnetDTO.getAllocationPools() == null) {
            return;
        }
        for (AllocationPoolDTO allocationPool : subnetDTO.getAllocationPools()) {
            Precondition.checkArgument(
                    allocationPool.getStartIp() != null && IpUtils.isIpInCidrAuto(allocationPool.getStartIp(), cidr),
                    String.format("起始ip(%s)不在网段(%s)中", allocationPool.getStartIp(), cidr)
            );
            Precondition.checkArgument(
                    allocationPool.getEndIp() != null && IpUtils.isIpInCidrAuto(allocationPool.getEndIp(), cidr),
                    String.format("终止ip:(%s)不在网段(%s)中", allocationPool.getEndIp(), cidr)
            );
            if (allocationPool.getStartIp() != null && allocationPool.getEndIp() != null) {
                Precondition.checkArgument(
                        IpUtils.ipv4ToLong(allocationPool.getStartIp()) < IpUtils.ipv4ToLong(allocationPool.getEndIp()),
                        String.format("起始ip(%s)不能大于终止ip(%s)", allocationPool.getStartIp(), allocationPool.getEndIp())
                );
            }
        }
    }
}

