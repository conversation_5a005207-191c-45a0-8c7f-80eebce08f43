package com.datatech.slgzt.impl.service.dag;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.dao.mapper.network.NetworkOrderMapper;
import com.datatech.slgzt.dao.mapper.network.NetworkSubnetOrderMapper;
import com.datatech.slgzt.dao.mapper.network.VpcOrderMapper;
import com.datatech.slgzt.dao.mapper.network.VpcSubnetOrderMapper;
import com.datatech.slgzt.dao.model.network.NetworkOrder;
import com.datatech.slgzt.dao.model.network.NetworkSubnetOrder;
import com.datatech.slgzt.dao.model.vpc.VpcOrder;
import com.datatech.slgzt.dao.model.vpc.VpcSubnetOrder;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.enums.network.NetworkPrefixEnum;
import com.datatech.slgzt.impl.service.xieyun.JobStepHelper;
import com.datatech.slgzt.manager.DagProductManager;
import com.datatech.slgzt.model.BaseProductModel;
import com.datatech.slgzt.model.DagResOpenResultDTO;
import com.datatech.slgzt.model.dto.DagProductDTO;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.model.query.DagProductQuery;
import com.datatech.slgzt.service.dag.DagResOpenService;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.support.ReferenceJobFactory;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.interceptor.DefaultTransactionAttribute;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月22日 10:23:50
 */
@Configuration
@Slf4j
public class DagCreateJobDef implements InitializingBean {

    @Resource
    private JobBuilderFactory jobBuilderFactory;
    @Resource
    private StepBuilderFactory stepBuilderFactory;

    @Autowired
    private List<DagResOpenService> dagProductCreateService;

    @Resource
    private DagCreateJobListener dagCreateJobListener;

    private final Map<String, DagResOpenService> serviceMap = Maps.newHashMap();

    @Resource
    private DagProductManager dagProductManager;
    @Resource
    private VpcOrderMapper vpcMapper;
    @Resource
    private VpcSubnetOrderMapper vpcSubnetMapper;
    @Resource
    private NetworkOrderMapper networkMapper;
    @Resource
    private NetworkSubnetOrderMapper networkSubnetMapper;

    @Bean("dagProductCreateJob")
    public Job environmentCreationJob() {
        Job job = jobBuilderFactory.get("dagProductCreateJob").incrementer(new RunIdIncrementer())
                .listener(dagCreateJobListener)
                .start(dagProductCreateInit())
                //vpc
                .next(dagVpcCreate())
                //network
                .next(dagNetworkCreate())
                //cloudPort
                .next(dagCloudPortCreate())
                //ecs
                .next(dagEcsCreate())
                //gcs
                .next(dagGcsCreate())
                //slb
                .next(dagSlbCreate())
                //nat
                .next(dagNatCreate())
                //eip
                .next(dagEipCreate())
                //evs
                .next(dagEvsCreate())
                //obs
                .next(dagObsCreate())
                .build();
        return new ReferenceJobFactory(job).createJob();
    }

    private Tasklet getProductTasklet(ProductTypeEnum productType) {
        return (contribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String orderId = jobStepHelper.get("orderId");
            // 通过orderId获取到product对象
            List<DagProductDTO> productDTOs = dagProductManager.list(new DagProductQuery()
                    .setOrderId(orderId)
                    .setProductType(productType.getCode()));
            //如果productDTOs为空，说明没有相关的资源，直接返回
            if (ObjNullUtils.isNull(productDTOs)) {
                return RepeatStatus.FINISHED;
            }
            // 找一个product，进行开通
            Optional<DagProductDTO> productDTOOptional = filterProduct(productDTOs);
            if (productDTOOptional.isPresent()) {
                DagProductDTO productDTO = productDTOOptional.get();
                try {
                    if (productType == ProductTypeEnum.ECS
                            || productType == ProductTypeEnum.GCS
                            || productType == ProductTypeEnum.MYSQL
                            || productType == ProductTypeEnum.POSTGRESQL
                            || productType == ProductTypeEnum.REDIS) {
                        CloudEcsResourceModel ecsModel =
                                JSON.parseObject(productDTO.getPropertySnapshot(), CloudEcsResourceModel.class);
                        List<PlaneNetworkModel> planeNetworkModel = getPlaneNetworkModelEcs(ecsModel, jobStepHelper);
                        ecsModel.setPlaneNetworkModel(planeNetworkModel);
                        productDTO.setPropertySnapshot(JSON.toJSONString(ecsModel));
                        // 更新快照，回调使用
                        DagProductDTO updateDTO = new DagProductDTO();
                        updateDTO.setId(productDTO.getId());
                        updateDTO.setPropertySnapshot(productDTO.getPropertySnapshot());
                        dagProductManager.update(updateDTO);
                    } else if (productType == ProductTypeEnum.SLB) {
                        SlbModel slbModel =
                                JSON.parseObject(productDTO.getPropertySnapshot(), SlbModel.class);
                        PlaneNetworkModel planeNetworkModel = getPlaneNetworkSlbAndNat(slbModel, jobStepHelper);
                        slbModel.setPlaneNetworkModel(planeNetworkModel);
                        productDTO.setPropertySnapshot(JSON.toJSONString(slbModel));
                        // 更新快照，回调使用
                        DagProductDTO updateDTO = new DagProductDTO();
                        updateDTO.setId(productDTO.getId());
                        updateDTO.setPropertySnapshot(productDTO.getPropertySnapshot());
                        dagProductManager.update(updateDTO);
                    } else if (productType == ProductTypeEnum.NAT) {
                        NatGatwayModel natGatwayModel =
                                JSON.parseObject(productDTO.getPropertySnapshot(), NatGatwayModel.class);
                        PlaneNetworkModel planeNetworkModel = getPlaneNetworkSlbAndNat(natGatwayModel, jobStepHelper);
                        natGatwayModel.setPlaneNetworkModel(planeNetworkModel);
                        productDTO.setPropertySnapshot(JSON.toJSONString(natGatwayModel));
                        // 更新快照，回调使用
                        DagProductDTO updateDTO = new DagProductDTO();
                        updateDTO.setId(productDTO.getId());
                        updateDTO.setPropertySnapshot(productDTO.getPropertySnapshot());
                        dagProductManager.update(updateDTO);
                    }
                    DagResOpenService createService = serviceMap.get(productType.getCode());
                    // 开通
                    createService.openResource(productDTO);
                } catch (Exception e) {
                    log.warn("product:{} , error message:{}", productDTO, ExceptionUtils.getStackTrace(e));
                    DagProductDTO dto = new DagProductDTO();
                    dto.setId(productDTO.getId());
                    dto.setOpenStatus(ResOpenEnum.OPEN_FAIL.getCode());
                    dto.setMessage(e.getMessage());
                    dagProductManager.update(dto);
                }
                // 开通后stop，等待product开通成功后，回调restart
                jobStepHelper.stop();
            }
            return RepeatStatus.FINISHED;
        };
    }

    //初始化的step用来获取执行id 主动停止 但是只会有一次
    @Bean("dagProductCreateInit")
    public Step dagProductCreateInit() {
        return stepBuilderFactory.get("dagProductCreateInit").tasklet((stepContribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String stepAlreadyExecuted = jobStepHelper.get("firstInit");
            //判断是否为空
            if (ObjNullUtils.isNull(stepAlreadyExecuted)) {
                //如果为空，说明是第一次执行
                jobStepHelper.put("firstInit", "true");
                //主动停止任务
                jobStepHelper.stop();
                return RepeatStatus.FINISHED;
            }
            return RepeatStatus.FINISHED;
        }).build();
    }

    // vpc创建
    @Bean("dagVpcCreate")
    public Step dagVpcCreate() {
        return stepBuilderFactory.get("dagVpcCreate").tasklet((stepContribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String orderId = jobStepHelper.get("orderId");
            //通过orderId 获取到VPC创建的对象
            List<DagProductDTO> vpcDTOList = dagProductManager.list(new DagProductQuery()
                    .setOrderId(orderId)
                    .setProductType("vpc")
                    .setStatusList(Lists.newArrayList(ResOpenEnum.WAIT_OPEN.getCode(), ResOpenEnum.OPEN_FAIL.getCode()))
            );
            //如果vpcDTOList不为空，说明已经创建完VPC了，直接返回
            if (ObjNullUtils.isNull(vpcDTOList)) {
                return RepeatStatus.FINISHED;
            }

            // 所有的对象都不是新建的，都是已经存在的
            boolean allExisted = true;
            for (DagProductDTO productDTO : vpcDTOList) {
                DagResOpenService createService = serviceMap.get("vpc");
                try {
                    //创建VPC--- 这里可以改成直接返回PlaneNetworkModel 让后面的程序直接用
                    DagResOpenResultDTO openResultDTO = (DagResOpenResultDTO) createService.openResource(productDTO);
                    jobStepHelper.put(productDTO.getDagId(), openResultDTO.getVpcId());
                    Map<String, String> map = jobStepHelper.get("subnetDagId2Id");
                    if (map == null) {
                        jobStepHelper.put("subnetDagId2Id", openResultDTO.getSubnetDagId2Id());
                    } else {
                        map.putAll(openResultDTO.getSubnetDagId2Id());
                    }
                    if (openResultDTO.getNewOpen()) {
                        allExisted = false;
                        break;
                    }
                } catch (Exception e) {
                    log.warn("product:{} , error message:{}", productDTO, ExceptionUtils.getStackTrace(e));
                    DagProductDTO dto = new DagProductDTO();
                    dto.setId(productDTO.getId());
                    dto.setOpenStatus(ResOpenEnum.OPEN_FAIL.getCode());
                    dto.setMessage(e.getMessage());
                    dagProductManager.update(dto);
                }
            }
            if (!allExisted) {
                jobStepHelper.stop();
            }
            return RepeatStatus.FINISHED;
        }).build();
    }


    // 网络创建
    @Bean("dagNetworkCreate")
    public Step dagNetworkCreate() {
        return stepBuilderFactory.get("dagNetworkCreate").tasklet((stepContribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String orderId = jobStepHelper.get("orderId");
            //通过orderId 获取到VPC创建的对象
            List<DagProductDTO> vpcDTOList = dagProductManager.list(new DagProductQuery()
                    .setOrderId(orderId)
                    .setProductType("network")
                    .setStatusList(Lists.newArrayList(ResOpenEnum.WAIT_OPEN.getCode(), ResOpenEnum.OPEN_FAIL.getCode()))
            );
            //如果vpcDTOList不为空，说明已经创建完VPC了，直接返回
            if (ObjNullUtils.isNull(vpcDTOList)) {
                return RepeatStatus.FINISHED;
            }

            // 所有的对象都不是新建的，都是已经存在的
            boolean allExisted = true;
            for (DagProductDTO productDTO : vpcDTOList) {
                DagResOpenService createService = serviceMap.get("network");
                try {
                    //创建network--- 这里可以改成直接返回PlaneNetworkModel 让后面的程序直接用
                    DagResOpenResultDTO openResultDTO = (DagResOpenResultDTO) createService.openResource(productDTO);
                    jobStepHelper.put(productDTO.getDagId(), openResultDTO.getVpcId());
                    Map<String, String> map = jobStepHelper.get("subnetDagId2Id");
                    if (map == null) {
                        jobStepHelper.put("subnetDagId2Id", openResultDTO.getSubnetDagId2Id());
                    } else {
                        map.putAll(openResultDTO.getSubnetDagId2Id());
                    }
                    if (openResultDTO.getNewOpen()) {
                        allExisted = false;
                        break;
                    }
                } catch (Exception e) {
                    log.warn("product:{} , error message:{}", productDTO, ExceptionUtils.getStackTrace(e));
                    DagProductDTO dto = new DagProductDTO();
                    dto.setId(productDTO.getId());
                    dto.setOpenStatus(ResOpenEnum.OPEN_FAIL.getCode());
                    dto.setMessage(e.getMessage());
                    dagProductManager.update(dto);
                }
            }
            if (!allExisted) {
                jobStepHelper.stop();
            }
            return RepeatStatus.FINISHED;
        }).build();
    }

    // cloudPort创建
    @Bean("dagCloudPortCreate")
    public Step dagCloudPortCreate() {
        DefaultTransactionAttribute attribute = new DefaultTransactionAttribute();
        attribute.setPropagationBehavior(TransactionDefinition.PROPAGATION_NOT_SUPPORTED);
        return stepBuilderFactory.get("dagCloudPortCreate").tasklet((stepContribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String orderId = jobStepHelper.get("orderId");
            //通过orderId 获取到VPC创建的对象
            List<DagProductDTO> cloudPortDTOList = dagProductManager.list(new DagProductQuery()
                    .setOrderId(orderId)
                    .setProductType("cloudPort"));
            //如果ecsDTOList不为空，说明已经创建完ecs了，直接返回
            if (ObjNullUtils.isNull(cloudPortDTOList)) {
                return RepeatStatus.FINISHED;
            }
            cloudPortDTOList.forEach(dagProductDTO -> {
                try {
                    //获取jobStepHelper中的vpcId
                    CloudPortModel cloudPortModel = JSON.parseObject(dagProductDTO.getPropertySnapshot(), CloudPortModel.class);
                    List<String> relDagIds = cloudPortModel.getRelDagIds();
                    //找到vpc_开头的dagId
                    String dagVpcId = relDagIds.stream().filter(dagId -> dagId.startsWith("vpc_")).findAny().orElseGet(null);
                    if (ObjNullUtils.isNotNull(dagVpcId)) {
                        //获取jobStepHelper中的vpcId
                        String vpcId = jobStepHelper.get(dagVpcId);
                        //查询vpc
                        VpcOrder vpcOrder = vpcMapper.selectById(vpcId);
                        String vpcName = vpcOrder.getVpcName();
                        cloudPortModel.setVpcName(vpcName);
                        cloudPortModel.setVpcId(vpcId);
                    }
                    DagResOpenService createService = serviceMap.get("cloudPort");
                    createService.openResource(dagProductDTO);
                    //更新产品表
                    dagProductDTO.setOpenStatus(ResOpenEnum.OPEN_SUCCESS.getCode());
                    dagProductManager.update(dagProductDTO);
                } catch (Exception e) {
                    //更新产品表
                    dagProductDTO.setOpenStatus(ResOpenEnum.OPEN_FAIL.getCode());
                    dagProductManager.update(dagProductDTO);
                    //已经这个节点关闭事务直接抛出异常即可
                    throw e;
                }

            });
            return RepeatStatus.FINISHED;
        }).transactionAttribute(attribute).build();
    }

    //创建ecs
    @Bean("dagEcsCreate")
    public Step dagEcsCreate() {
        return stepBuilderFactory.get("dagEcsCreate").tasklet(getProductTasklet(ProductTypeEnum.ECS)).build();
    }

    //gcs
    @Bean("dagGcsCreate")
    public Step dagGcsCreate() {
        return stepBuilderFactory.get("dagGcsCreate").tasklet(getProductTasklet(ProductTypeEnum.GCS)).build();
    }

    //slb
    @Bean("dagSlbCreate")
    public Step dagSlbCreate() {
        return stepBuilderFactory.get("dagSlbCreate").tasklet(getProductTasklet(ProductTypeEnum.SLB)).build();
    }

    //nat
    @Bean("dagNatCreate")
    public Step dagNatCreate() {
        return stepBuilderFactory.get("dagNatCreate").tasklet(getProductTasklet(ProductTypeEnum.NAT)).build();
    }

    //eip
    @Bean("dagEipCreate")
    public Step dagEipCreate() {
        return stepBuilderFactory.get("dagEipCreate").tasklet(getProductTasklet(ProductTypeEnum.EIP)).build();
    }

    //evs
    @Bean("dagEvsCreate")
    public Step dagEvsCreate() {
        return stepBuilderFactory.get("dagEvsCreate").tasklet(getProductTasklet(ProductTypeEnum.EVS)).build();
    }

    //obs
    @Bean("dagObsCreate")
    public Step dagObsCreate() {
        return stepBuilderFactory.get("dagObsCreate").tasklet(getProductTasklet(ProductTypeEnum.OBS)).build();
    }


    @Override
    public void afterPropertiesSet() {
        for (DagResOpenService service : dagProductCreateService) {
            serviceMap.put(service.registerOpenService().getCode(), service);
        }
    }

    private Optional<DagProductDTO> filterProduct(List<DagProductDTO> productDTOs) {
        return productDTOs.stream().filter(i -> i.getParentProductId() == 0
                && (ResOpenEnum.WAIT_OPEN.getCode().equals(i.getOpenStatus()) || ResOpenEnum.OPEN_FAIL.getCode().equals(i.getOpenStatus()))
        ).findFirst();
    }

    private List<PlaneNetworkModel> getPlaneNetworkModelEcs(BaseProductModel productModel, JobStepHelper jobStepHelper) {
//        "planeNetworkModel": [
//            {
//                "id": "vpc_17472214221421134",
//                    "name": "zjltest01vpc",
//                    "plane": "私网地址",
//                    "sort": 0,
//                    "subnets": [
//                {
//                    "subnetId": "vsub_17472214221571620",
//                        "subnetIp": "**********/23",
//                        "subnetName": "zjltest01vpc"
//                }
//                ],
//                "type": "vpc"
//            }
//        ]

        Map<String, PlaneNetworkModel> vpcMap = new HashMap<>();
        Map<String, PlaneNetworkModel> networkMap = new HashMap<>();
        // 子网的id
        for (String subnetDagId : productModel.getSubnetDagIds()) {
            Map<String, String> subnetDagId2Id = jobStepHelper.get("subnetDagId2Id");
            String subnetId = subnetDagId2Id.get(subnetDagId);
            if (subnetDagId.startsWith("vpc_subnet_")) {
                VpcSubnetOrder subnetDO = vpcSubnetMapper.selectById(subnetId);
                PlaneNetworkModel planeNetworkModel = vpcMap.get(subnetDO.getVpcId());
                if (planeNetworkModel == null) {
                    planeNetworkModel = new PlaneNetworkModel();
                    planeNetworkModel.setType(NetworkPrefixEnum.VPC.getType());
                    VpcOrder vpcDO = vpcMapper.selectByIdAndStatus(subnetDO.getVpcId());
                    planeNetworkModel.setId(vpcDO.getId());
                    planeNetworkModel.setName(vpcDO.getVpcName());
                    planeNetworkModel.setPlane(vpcDO.getPlane());
                    planeNetworkModel.setSubnets(Lists.newArrayList());
                    planeNetworkModel.setSort(0);
                    vpcMap.put(subnetDO.getVpcId(), planeNetworkModel);
                }
                PlaneNetworkModel.Subnet subnet = new PlaneNetworkModel.Subnet();

                subnet.setSubnetId(subnetDO.getId());
                subnet.setSubnetName(subnetDO.getSubnetName());
                // todo 确定这个是什么
                subnet.setSubnetIp(subnetDO.getCidr());

                planeNetworkModel.getSubnets().add(subnet);
            } else {
                // network_subnet_
                NetworkSubnetOrder subnetDO = networkSubnetMapper.selectById(subnetId);
                PlaneNetworkModel planeNetworkModel = networkMap.get(subnetDO.getNetworkId());
                if (planeNetworkModel == null) {
                    planeNetworkModel = new PlaneNetworkModel();
                    planeNetworkModel.setType(NetworkPrefixEnum.NET.getType());
                    NetworkOrder networkDO = networkMapper.selectById(subnetDO.getNetworkId());
                    planeNetworkModel.setId(networkDO.getId());
                    planeNetworkModel.setName(networkDO.getName());
                    planeNetworkModel.setPlane(networkDO.getPlane());
                    planeNetworkModel.setSubnets(Lists.newArrayList());
                    planeNetworkModel.setSort(0);
                    vpcMap.put(subnetDO.getNetworkId(), planeNetworkModel);
                }
                PlaneNetworkModel.Subnet subnet = new PlaneNetworkModel.Subnet();

                subnet.setSubnetId(subnetDO.getId());
                subnet.setSubnetName(subnetDO.getSubnetName());
                // todo 确定这个是什么，还少不少要传的东西了
                subnet.setSubnetIp(subnetDO.getCidr());

                planeNetworkModel.getSubnets().add(subnet);
            }
        }
        List<PlaneNetworkModel> planeNetworkModelArrayList = new ArrayList<>();
        planeNetworkModelArrayList.addAll(vpcMap.values());
        planeNetworkModelArrayList.addAll(networkMap.values());
        return planeNetworkModelArrayList;
    }

    private PlaneNetworkModel getPlaneNetworkSlbAndNat(BaseProductModel productModel, JobStepHelper jobStepHelper) {
        String subnetDagId = productModel.getSubnetDagIds().stream()
                .filter(i -> i.startsWith("vpc_subnet_") || i.startsWith("network_subnet_"))
                .findFirst().get();
        Map<String, String> subnetDagId2Id = jobStepHelper.get("subnetDagId2Id");
        String subnetId = subnetDagId2Id.get(subnetDagId);
        PlaneNetworkModel planeNetworkModel = new PlaneNetworkModel();
        if (subnetDagId.startsWith("vpc_subnet_")) {
            VpcSubnetOrder subnetDO = vpcSubnetMapper.selectById(subnetId);
            planeNetworkModel.setType(NetworkPrefixEnum.VPC.getType());
            VpcOrder vpcDO = vpcMapper.selectById(subnetDO.getVpcId());
            planeNetworkModel.setId(vpcDO.getId());
            planeNetworkModel.setName(vpcDO.getVpcName());
            planeNetworkModel.setPlane(vpcDO.getPlane());
            planeNetworkModel.setSubnets(Lists.newArrayList());
            planeNetworkModel.setSort(0);

            PlaneNetworkModel.Subnet subnet = new PlaneNetworkModel.Subnet();
            subnet.setSubnetId(subnetDO.getId());
            subnet.setSubnetName(subnetDO.getSubnetName());
            // todo 确定这个是什么
            subnet.setSubnetIp(subnetDO.getCidr());
            planeNetworkModel.getSubnets().add(subnet);
        } else {
            // network_subnet_
            NetworkSubnetOrder subnetDO = networkSubnetMapper.selectById(subnetId);
            planeNetworkModel.setType(NetworkPrefixEnum.NET.getType());
            NetworkOrder networkDO = networkMapper.selectById(subnetDO.getNetworkId());
            planeNetworkModel.setId(networkDO.getId());
            planeNetworkModel.setName(networkDO.getName());
            planeNetworkModel.setPlane(networkDO.getPlane());
            planeNetworkModel.setSubnets(Lists.newArrayList());
            planeNetworkModel.setSort(0);

            PlaneNetworkModel.Subnet subnet = new PlaneNetworkModel.Subnet();
            subnet.setSubnetId(subnetDO.getId());
            subnet.setSubnetName(subnetDO.getSubnetName());
            // todo 确定这个是什么，还少不少要传的东西了
            subnet.setSubnetIp(subnetDO.getCidr());
            planeNetworkModel.getSubnets().add(subnet);
        }
        return planeNetworkModel;
    }
}
