package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.DeviceVirtualInfoMapper;
import com.datatech.slgzt.dao.model.DeviceGpuInfoDO;
import com.datatech.slgzt.dao.model.DeviceVirtualInfoDO;
import com.datatech.slgzt.model.query.DeviceInfoQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class DeviceVirtualInfoDAO {
    @Resource
    private DeviceVirtualInfoMapper mapper;

    public DeviceVirtualInfoDO getById(String id) {
        return mapper.selectById(id);
    }

    public void insert(DeviceVirtualInfoDO deviceCardMetricsDO) {
        mapper.insert(deviceCardMetricsDO);
    }

    public void updateLastByDeviceId(DeviceVirtualInfoDO deviceGpuInfoDO) {
        mapper.update(null, Wrappers.<DeviceVirtualInfoDO>lambdaUpdate()
                                    .set(DeviceVirtualInfoDO::getLastPeriod, deviceGpuInfoDO.getLastPeriod())
                                    .like(DeviceVirtualInfoDO::getDeviceId, deviceGpuInfoDO.getDeviceId()));
    }

    public void updateById(DeviceVirtualInfoDO deviceCardMetricsDO) {
        mapper.updateById(deviceCardMetricsDO);
    }

    public void deleteById(Long id) {
        mapper.deleteById(id);
    }

    public void deleteByIds(List<Long> ids) {
        mapper.deleteBatchIds(ids);
    }

    public List<DeviceVirtualInfoDO> selectList(DeviceInfoQuery query){
        return mapper.selectList(Wrappers.<DeviceVirtualInfoDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(query.getAreaCode()), DeviceVirtualInfoDO::getAreaCode, query.getAreaCode())
                .eq(ObjNullUtils.isNotNull(query.getDomainCode()), DeviceVirtualInfoDO::getDomainCode, query.getDomainCode())
                .in(ObjNullUtils.isNotNull(query.getDeviceIds()), DeviceVirtualInfoDO::getDeviceId, query.getDeviceIds())
                .eq(ObjNullUtils.isNotNull(query.getCatalogueDomainCode()), DeviceVirtualInfoDO::getCatalogueDomainCode, query.getCatalogueDomainCode())
                .eq(ObjNullUtils.isNotNull(query.getRegionCode()), DeviceVirtualInfoDO::getRegionCode, query.getRegionCode())
                .eq(ObjNullUtils.isNotNull(query.getDncIp()), DeviceVirtualInfoDO::getDeviceIp, query.getDncIp())
                .eq(ObjNullUtils.isNotNull(query.getBusinessSystemId()), DeviceVirtualInfoDO::getBusinessSystemId, query.getBusinessSystemId())
                .isNotNull(query.getGpuSort(),DeviceVirtualInfoDO::getLastPeriod)
                .last(query.getGpuSort(),"order by CAST(COALESCE(json_value(LAST_PERIOD, '$.gpuUtilPercent'), '-1') AS DECIMAL) DESC")
        );
    }
}
