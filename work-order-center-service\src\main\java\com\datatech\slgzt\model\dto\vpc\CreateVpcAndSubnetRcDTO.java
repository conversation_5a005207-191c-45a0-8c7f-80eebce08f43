package com.datatech.slgzt.model.dto.vpc;

import com.datatech.slgzt.model.dto.network.AllocationPoolDTO;
import lombok.Data;

import java.util.List;

@Data
public class CreateVpcAndSubnetRcDTO {
    /**
     * 租户id -必填 小运管租户
     */
    private Long tenantId;
    /**
     * e55 必填 资源池code
     */
    private String regionCode;
    /**
     * 私有网络名称 必填
     */
    private String name;

    /**
     * 网段，ipv4和V6有一个必填
     */
    private String ipv4Cidr;
    /**
     * ipv6 网段，未使用
     */
    private String ipv6Cidr;
    /**
     * 备注信息，非必填
     */
    private String description;

    /**
     * vpcId 同gid 必填
     */
    private String orderId;
    /**
     * 未使用，非必填
     */
    private String vdcCode;
    /**
     * 已废弃，非必填
     */
    private String projectId;

    /**
     * gid，唯一id，必填
     */
    private String gId;
    private List<CreateSubnetRcDto> subnetDTOList;

    public static class CreateSubnetRcDto {
        /**
         * 资源池 必填
         */
        private String regionCode;

        /**
         * 可用区，必填
         */
        private String azCode;

        /**
         * 小运管租户id
         */
        private Long tenantId;
        /**
         * name 必填
         */
        private String name;
        private String description;

        /**
         * ipv4网段，必填
         */
        private String cidr;
        /**
         * 使用ipv6时必填，暂时不涉及
         */
        private String ipv6Cidr;
        private Long ipVersion;
        private String gatewayIp;
        private List<String> dnsNameservers;
        private String ipv6RaMode;
        private String ipv6AddressMode;
        /**
         * vpc Gid 必填
         */
        private String vpcId;

        /**
         * 子网主键id 必填
         */
        private  String orderId;
        private String outInstanceId;
        private Boolean ipv6Enable;

        private Integer vlanId;

        private List<AllocationPoolDTO> allocationPools;

        public String getRegionCode() {
            return regionCode;
        }

        public void setRegionCode(String regionCode) {
            this.regionCode = regionCode;
        }

        public String getAzCode() {
            return azCode;
        }

        public void setAzCode(String azCode) {
            this.azCode = azCode;
        }

        public Long getTenantId() {
            return tenantId;
        }

        public void setTenantId(Long tenantId) {
            this.tenantId = tenantId;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getCidr() {
            return cidr;
        }

        public void setCidr(String cidr) {
            this.cidr = cidr;
        }

        public String getIpv6Cidr() {
            return ipv6Cidr;
        }

        public void setIpv6Cidr(String ipv6Cidr) {
            this.ipv6Cidr = ipv6Cidr;
        }

        public Long getIpVersion() {
            return ipVersion;
        }

        public void setIpVersion(Long ipVersion) {
            this.ipVersion = ipVersion;
        }

        public String getGatewayIp() {
            return gatewayIp;
        }

        public void setGatewayIp(String gatewayIp) {
            this.gatewayIp = gatewayIp;
        }

        public List<String> getDnsNameservers() {
            return dnsNameservers;
        }

        public void setDnsNameservers(List<String> dnsNameservers) {
            this.dnsNameservers = dnsNameservers;
        }

        public String getIpv6RaMode() {
            return ipv6RaMode;
        }

        public void setIpv6RaMode(String ipv6RaMode) {
            this.ipv6RaMode = ipv6RaMode;
        }

        public String getIpv6AddressMode() {
            return ipv6AddressMode;
        }

        public void setIpv6AddressMode(String ipv6AddressMode) {
            this.ipv6AddressMode = ipv6AddressMode;
        }

        public String getVpcId() {
            return vpcId;
        }

        public void setVpcId(String vpcId) {
            this.vpcId = vpcId;
        }

        public String getOrderId() {
            return orderId;
        }

        public void setOrderId(String orderId) {
            this.orderId = orderId;
        }

        public String getOutInstanceId() {
            return outInstanceId;
        }

        public void setOutInstanceId(String outInstanceId) {
            this.outInstanceId = outInstanceId;
        }

        public Integer getVlanId() {
            return vlanId;
        }

        public void setVlanId(Integer vlanId) {
            this.vlanId = vlanId;
        }

        public Boolean getIpv6Enable() {
            return ipv6Enable;
        }

        public void setIpv6Enable(Boolean ipv6Enable) {
            this.ipv6Enable = ipv6Enable;
        }

        public List<AllocationPoolDTO> getAllocationPools() {
            return allocationPools;
        }

        public void setAllocationPools(List<AllocationPoolDTO> allocationPools) {
            this.allocationPools = allocationPools;
        }
    }
}
