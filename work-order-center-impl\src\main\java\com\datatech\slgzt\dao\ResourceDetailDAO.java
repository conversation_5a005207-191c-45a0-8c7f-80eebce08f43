package com.datatech.slgzt.dao;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.ResourceDetailMapper;
import com.datatech.slgzt.dao.model.ResourceDetailDO;
import com.datatech.slgzt.enums.StatusEnum;
import com.datatech.slgzt.model.dto.ModuleOfflineDTO;
import com.datatech.slgzt.model.dto.OperateVmDTO;
import com.datatech.slgzt.model.dto.ResetVmPwdDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.network.NetcardDetailDTO;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.model.query.VmOperateQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月13日 12:40:37
 */
@Repository
public class ResourceDetailDAO {

    @Resource
    private ResourceDetailMapper mapper;


    public ResourceDetailDO selectByIdNoStatus(Long id) {
        return mapper.selectByIdNoStatus(id);
    }

    public void insert(ResourceDetailDO detailDO) {
        mapper.insert(detailDO);
    }

    public void updateById(ResourceDetailDO detailDO) {
        mapper.updateById(detailDO);
    }

    public void updateVmIdAndEscNameById(Long id, String vmId, String ecsName, Integer recoveryStatus) {
        LambdaUpdateWrapper<ResourceDetailDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ResourceDetailDO::getId, id);
        updateWrapper.set(ResourceDetailDO::getVmId, vmId);
        updateWrapper.set(ResourceDetailDO::getEcsName, ecsName);
        updateWrapper.set(ResourceDetailDO::getMountOrNot, "否");
        updateWrapper.set(Objects.nonNull(recoveryStatus), ResourceDetailDO::getRecoveryStatus, recoveryStatus);
        mapper.update(null, updateWrapper);
    }

    public void updateEipById(Long id, Integer recoveryStatus) {
        LambdaUpdateWrapper<ResourceDetailDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ResourceDetailDO::getId, id);
        updateWrapper.set(ResourceDetailDO::getRecoveryStatus, recoveryStatus);
        mapper.update(new ResourceDetailDO(), updateWrapper);
    }

    public void updateSecurityGroupIdById(Long id, String securityGroupIds) {
        LambdaUpdateWrapper<ResourceDetailDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ResourceDetailDO::getId, id);
        updateWrapper.set(ResourceDetailDO::getSecurityGroupIds, securityGroupIds);
        mapper.update(null, updateWrapper);
    }

    public void updateRecoveryTypeByIds(List<Long> ids, Integer recoveryType) {
        ResourceDetailDO resourceDetailDO = new ResourceDetailDO();
        resourceDetailDO.setRecoveryStatus(recoveryType);
        mapper.update(resourceDetailDO, Wrappers.<ResourceDetailDO>lambdaUpdate()
                .in(ResourceDetailDO::getId, ids));
    }


    public void updateRecoveryTypeByDeviceIds(List<String> deviceIds, Integer recoveryType) {
        ResourceDetailDO resourceDetailDO = new ResourceDetailDO();
        resourceDetailDO.setRecoveryStatus(recoveryType);
        mapper.update(resourceDetailDO, Wrappers.<ResourceDetailDO>lambdaUpdate()
                .in(ResourceDetailDO::getDeviceId, deviceIds));
    }

    public void updateDisOrderStatusByDeviceIds(List<String> deviceIds, String disOrderStatus) {
        ResourceDetailDO resourceDetailDO = new ResourceDetailDO();
        resourceDetailDO.setDisOrderStatus(disOrderStatus);
        mapper.update(resourceDetailDO, Wrappers.<ResourceDetailDO>lambdaUpdate()
                                                .in(ResourceDetailDO::getDeviceId, deviceIds));
    }

    public ResourceDetailDO selectById(String id) {
        return mapper.selectById(id);
    }

    public ResourceDetailDO getByGoodsOrderId(Long goodsOrderId) {
        return mapper.selectOne(Wrappers.<ResourceDetailDO>lambdaQuery()
                .eq(ResourceDetailDO::getGoodsOrderId, goodsOrderId));
    }

    public ResourceDetailDO getByDeviceId(String deviceId) {
        return mapper.selectOne(Wrappers.<ResourceDetailDO>lambdaQuery()
                .eq(ResourceDetailDO::getDeviceId, deviceId));
    }

    public List<ResourceDetailDO> list(ResourceDetailQuery query) {
        LambdaQueryWrapper<ResourceDetailDO> queryWrapper = Wrappers.<ResourceDetailDO>lambdaQuery()
                //通用
                .eq(ObjNullUtils.isNotNull(query.getSourceType()), ResourceDetailDO::getSourceType, query.getSourceType())
                .isNull(ObjNullUtils.isNotNull(query.getEipIsRel()), ResourceDetailDO::getEipId)
                .eq(ObjNullUtils.isNotNull(query.getType()), ResourceDetailDO::getType, query.getType())
                .in(ObjNullUtils.isNotNull(query.getTypeList()), ResourceDetailDO::getType, query.getTypeList())
                .eq(ObjNullUtils.isNotNull(query.getGoodsOrderId()), ResourceDetailDO::getGoodsOrderId, query.getGoodsOrderId())
                .in(ObjNullUtils.isNotNull(query.getRecoveryStatusList()), ResourceDetailDO::getRecoveryStatus, query.getRecoveryStatusList())
                .in(ObjNullUtils.isNotNull(query.getSourceTypeList()), ResourceDetailDO::getSourceType, query.getSourceTypeList())
                .eq(ObjNullUtils.isNotNull(query.getApplyTime()), ResourceDetailDO::getApplyTime, query.getApplyTime())
                .eq(ObjNullUtils.isNotNull(query.getDisOrderStatus()), ResourceDetailDO::getDisOrderStatus, query.getDisOrderStatus())
                .eq(ObjNullUtils.isNotNull(query.getOrderId()), ResourceDetailDO::getOrderId, query.getOrderId())
                .eq(ObjNullUtils.isNotNull(query.getBusinessSysId()), ResourceDetailDO::getBusinessSysId, query.getBusinessSysId())
                .like(ObjNullUtils.isNotNull(query.getDeviceName()), ResourceDetailDO::getDeviceName, query.getDeviceName())
                .eq(ObjNullUtils.isNotNull(query.getDeviceId()), ResourceDetailDO::getDeviceId, query.getDeviceId())
                .eq(ObjNullUtils.isNotNull(query.getVmId()), ResourceDetailDO::getVmId, query.getVmId())
                .eq(ObjNullUtils.isNotNull(query.getResourceId()), ResourceDetailDO::getResourceId, query.getResourceId())
                .like(ObjNullUtils.isNotNull(query.getTenantName()), ResourceDetailDO::getTenantName, query.getTenantName())
                .like(ObjNullUtils.isNotNull(query.getBusinessSysName()), ResourceDetailDO::getBusinessSysName, query.getBusinessSysName())
                .in(ObjNullUtils.isNotNull(query.getBusinessSysIdList()), ResourceDetailDO::getBusinessSysId, query.getBusinessSysIdList())
                .in(ObjNullUtils.isNotNull(query.getIds()), ResourceDetailDO::getId, query.getIds())
                .in(ObjNullUtils.isNotNull(query.getGoodOrderIds()), ResourceDetailDO::getGoodsOrderId, query.getGoodOrderIds())
                .like(ObjNullUtils.isNotNull(query.getCloudPlatform()), ResourceDetailDO::getDomainName, query.getCloudPlatform())
                .eq(ObjNullUtils.isNotNull(query.getDomainCode()), ResourceDetailDO::getDomainCode, query.getDomainCode())
                .like(ObjNullUtils.isNotNull(query.getVpcId()), ResourceDetailDO::getVpcId, query.getVpcId())
                .like(ObjNullUtils.isNotNull(query.getResourcePoolName()), ResourceDetailDO::getResourcePoolName, query.getResourcePoolName())
                .eq(ObjNullUtils.isNotNull(query.getResourcePoolId()), ResourceDetailDO::getResourcePoolId, query.getResourcePoolId())
                .eq(ObjNullUtils.isNotNull(query.getAzId()), ResourceDetailDO::getAzId, query.getAzId())
                .like(ObjNullUtils.isNotNull(query.getOrderCode()), ResourceDetailDO::getOrderCode, query.getOrderCode())
                .ge(ObjNullUtils.isNotNull(query.getCreateTimeStart()), ResourceDetailDO::getCreateTime, query.getCreateTimeStart())
                .le(ObjNullUtils.isNotNull(query.getCreateTimeEnd()), ResourceDetailDO::getCreateTime, query.getCreateTimeEnd())
                .ge(ObjNullUtils.isNotNull(query.getExpireTimeStart()), ResourceDetailDO::getExpireTime, query.getExpireTimeStart())
                .le(ObjNullUtils.isNotNull(query.getExpireTimeEnd()), ResourceDetailDO::getExpireTime, query.getExpireTimeEnd())
                .eq(ObjNullUtils.isNotNull(query.getBillId()), ResourceDetailDO::getBillId, query.getBillId())
                .eq(ObjNullUtils.isNotNull(query.getDeviceStatus()), ResourceDetailDO::getDeviceStatus, query.getDeviceStatus())
                .like(ObjNullUtils.isNotNull(query.getApplyUserName()), ResourceDetailDO::getApplyUserName, query.getApplyUserName())
                .eq(ObjNullUtils.isNotNull(query.getApplyUserId()), ResourceDetailDO::getApplyUserId, query.getApplyUserId())
                .in(ObjNullUtils.isNotNull(query.getTenantList()), ResourceDetailDO::getTenantId, query.getTenantList())
                //计算资源
                .like(ObjNullUtils.isNotNull(query.getOsVersion()), ResourceDetailDO::getOsVersion, query.getOsVersion())
                .like(ObjNullUtils.isNotNull(query.getSpec()), ResourceDetailDO::getSpec, query.getSpec())
                .like(ObjNullUtils.isNotNull(query.getSysDisk()), ResourceDetailDO::getSysDisk, query.getSysDisk())
                .like(ObjNullUtils.isNotNull(query.getDataDisk()), ResourceDetailDO::getDataDisk, query.getDataDisk())
                .like(ObjNullUtils.isNotNull(query.getIp()), ResourceDetailDO::getIp, query.getIp())
                .like(ObjNullUtils.isNotNull(query.getEip()), ResourceDetailDO::getEip, query.getEip())
                .eq(ObjNullUtils.isNotNull(query.getBandWidth()), ResourceDetailDO::getBandWidth, query.getBandWidth())
                .like(ObjNullUtils.isNotNull(query.getInstanceUuid()), ResourceDetailDO::getInstanceUuid, query.getInstanceUuid())
                //存储资源
                .eq(ObjNullUtils.isNotNull(query.getStoreType()), ResourceDetailDO::getStoreType, query.getStoreType())
                .eq(ObjNullUtils.isNotNull(query.getCapacity()), ResourceDetailDO::getCapacity, query.getCapacity())
                .eq(ObjNullUtils.isNotNull(query.getAccessKey()), ResourceDetailDO::getAccessKey, query.getAccessKey())
                .eq(ObjNullUtils.isNotNull(query.getSecretKey()), ResourceDetailDO::getSecretKey, query.getSecretKey())
                .like(ObjNullUtils.isNotNull(query.getPublicAddress()), ResourceDetailDO::getPublicAddress, query.getPublicAddress())
                .like(ObjNullUtils.isNotNull(query.getInternalAddress()), ResourceDetailDO::getInternalAddress, query.getInternalAddress())
                .eq(ObjNullUtils.isNotNull(query.getMountOrNot()), ResourceDetailDO::getMountOrNot, query.getMountOrNot())
                .eq(ObjNullUtils.isNotNull(query.getStatus()), ResourceDetailDO::getStatus, query.getStatus())
                .like(ObjNullUtils.isNotNull(query.getEcsName()), ResourceDetailDO::getEcsName, query.getEcsName())
                //网络
                .like(ObjNullUtils.isNotNull(query.getVpcName()), ResourceDetailDO::getVpcName, query.getVpcName())
                .like(ObjNullUtils.isNotNull(query.getSubnetName()), ResourceDetailDO::getSubnetName, query.getSubnetName())
                .ge(ObjNullUtils.isNotNull(query.getEffectiveTimeStart()), ResourceDetailDO::getEffectiveTime, query.getEffectiveTimeStart())
                .le(ObjNullUtils.isNotNull(query.getEffectiveTimeEnd()), ResourceDetailDO::getEffectiveTime, query.getEffectiveTimeEnd())
                .in(ObjNullUtils.isNotNull(query.getVpcIds()), ResourceDetailDO::getVpcId, query.getVpcIds())
                .eq(ObjNullUtils.isNotNull(query.getHandoverStatus()), ResourceDetailDO::getHandoverStatus, query.getHandoverStatus())
                .like(ObjNullUtils.isNotNull(query.getSecurityGroupIds()), ResourceDetailDO::getSecurityGroupIds, query.getSecurityGroupIds())
                .eq(ObjNullUtils.isNotNull(query.getBackupId()), ResourceDetailDO::getBackupId, query.getBackupId())
                .eq(ObjNullUtils.isNotNull(query.getRelatedDeviceId()), ResourceDetailDO::getRelatedDeviceId, query.getRelatedDeviceId())
                .eq(ObjNullUtils.isNotNull(query.getBackupType()), ResourceDetailDO::getBackupType,
                        query.getBackupType() != null ? query.getBackupType().toUpperCase() : null)
                .eq(ObjNullUtils.isNotNull(query.getIpv6Enable()), ResourceDetailDO::getIpv6Enable, query.getIpv6Enable())
                .in(ObjNullUtils.isNotNull(query.getDeviceIds()), ResourceDetailDO::getDeviceId, query.getDeviceIds())
                .like(ObjNullUtils.isNotNull(query.getFrequency()), ResourceDetailDO::getFrequency, query.getFrequency())
                .like(ObjNullUtils.isNotNull(query.getUsername()), ResourceDetailDO::getCapacity, query.getUsername())
                .like(ObjNullUtils.isNotNull(query.getNetType()), ResourceDetailDO::getVpcId, query.getNetType())
                .orderByDesc(ResourceDetailDO::getCreateTime);

        String keywords = query.getKeywords();
        // 通过keywords多条件匹配 请输入云主机/iP/所属云/资源池
        if (StringUtils.isNotEmpty(keywords)) {
            queryWrapper.and(wrapper -> wrapper
                    .like(ResourceDetailDO::getDeviceName, keywords)
                    .or()
                    .like(ResourceDetailDO::getIp, keywords)
                    .or()
                    .like(ResourceDetailDO::getCloudPlatform, keywords)
                    .or()
                    .like(ResourceDetailDO::getResourcePoolName, keywords)
            );
        }


        return mapper.selectList(queryWrapper);
    }

    public void updateConfigId(String configId, Long id, String manageIp) {
        mapper.updateConfigId(configId, id, manageIp);
    }

    public NetcardDetailDTO selectNetInfoByUuidAndIp(String instanceUuid, String ip) {
        return mapper.selectNetInfoByUuidAndIp(instanceUuid, ip);
    }

    public ResetVmPwdDTO selectOperateVmResetPwd(VmOperateQuery query) {
        return mapper.selectOperateVmResetPwd(query);
    }

    public OperateVmDTO selectOperateVm(VmOperateQuery query) {
        return mapper.selectOperateVm(query);
    }

    public void updateDeviceStatus(String deviceStatus, String deviceId) {
        mapper.updateDeviceStatusByDeviceId(deviceStatus, deviceId);
    }

    public List<ResourceDetailDO> selectOrderGoodsByType(List<String> businessIds, List<String> productTypes, Integer recoveryType) {
        return mapper.selectOrderGoodsByType(businessIds, productTypes, recoveryType);
    }

    public List<Long> selectBusinessIdByGoodsIds(List<String> goodsIds) {
        QueryWrapper<ResourceDetailDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("STATUS", StatusEnum.NORMAL.code());
        queryWrapper.in("ID", goodsIds);
        List<ResourceDetailDO> detailDOS = mapper.selectList(queryWrapper);
        return detailDOS.stream().map(ResourceDetailDO::getBusinessSysId).collect(Collectors.toList());
    }

    public List<ResourceDetailDO> selectByGoodsIds(List<String> goodsIdList) {
        QueryWrapper<ResourceDetailDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("STATUS", StatusEnum.NORMAL.code());
        queryWrapper.in("ID", goodsIdList);
        return mapper.selectList(queryWrapper);
    }

    public ResourceDetailDO selectById(Long id) {
        return mapper.selectById(id);
    }

    public void deleteById(Long id) {
        mapper.deleteById(id);
    }

    public List<ResourceDetailDO> selectRecoveryStatusByIds(List<String> ids, Integer recoveryStatus, Integer status) {
        return mapper.selectRecoveryStatusByIds(ids, recoveryStatus, status);
    }

    public String selectByBusinessSystemId(Long businessSystemId) {
        QueryWrapper<ResourceDetailDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("STATUS", StatusEnum.NORMAL.code());
        queryWrapper.eq("BUSINESS_SYS_ID", businessSystemId);
        List<ResourceDetailDO> detailDOS = mapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(detailDOS)) {
            return detailDOS.get(0).getBusinessSysName();
        }

        return null;
    }

    public List<String> selectRegionCodeByOrderId(String orderId) {
        return mapper.selectRegionCodeByOrderId(orderId);
    }

    public List<ModuleOfflineDTO> selectNotUserModule(Long businessSysId) {
        return mapper.selectNotUserModule(businessSysId);
    }

    public ResourceDetailDO getConfigId(String configId) {
        return mapper.selectOne(Wrappers.<ResourceDetailDO>lambdaQuery()
                .eq(ResourceDetailDO::getConfigId, configId));
    }

    public void updateChangeStatusByIds(List<Long> resourceIds, String changeStatus) {
        mapper.updateChangeStatusByIds(resourceIds, changeStatus);
    }

    public void updateChangeStatusByDeviceIds(List<String> deviceIds, String changeStatus) {
        mapper.updateChangeStatusByDeviceIds(deviceIds, changeStatus);
    }

    public void updateEipInfoByEipId(String eipId, String eip, String bandWidth) {
        LambdaUpdateWrapper<ResourceDetailDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ResourceDetailDO::getEipId, eipId);
        updateWrapper.set(ResourceDetailDO::getEip, eip);
        updateWrapper.set(ResourceDetailDO::getBandWidth, bandWidth);
        mapper.update(new ResourceDetailDO(), updateWrapper);
    }

    public void evsAddEipInfoByDeviceId(String deviceId, String eipId, String eip, String bandWidth) {
        LambdaUpdateWrapper<ResourceDetailDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ResourceDetailDO::getDeviceId, deviceId);
        updateWrapper.set(ResourceDetailDO::getEipId, eipId);
        updateWrapper.set(ResourceDetailDO::getEip, eip);
        updateWrapper.set(ResourceDetailDO::getBandWidth, bandWidth);
        mapper.update(new ResourceDetailDO(), updateWrapper);
    }

    public void clearEipInfoByEipId(String eipId) {
        LambdaUpdateWrapper<ResourceDetailDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ResourceDetailDO::getEipId, eipId);
        updateWrapper.set(ResourceDetailDO::getEipId, null);
        updateWrapper.set(ResourceDetailDO::getEip, null);
        updateWrapper.set(ResourceDetailDO::getBandWidth, null);
        mapper.update(new ResourceDetailDO(), updateWrapper);
    }

    public void clearBackupInfoById(Long id) {
        LambdaUpdateWrapper<ResourceDetailDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ResourceDetailDO::getId, id);
        updateWrapper.set(ResourceDetailDO::getBackupId, null);
        mapper.update(new ResourceDetailDO(), updateWrapper);
    }

    public void clearEipInfoById(Long id) {
        LambdaUpdateWrapper<ResourceDetailDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ResourceDetailDO::getId, id);
        updateWrapper.set(ResourceDetailDO::getEipId, null);
        updateWrapper.set(ResourceDetailDO::getEip, null);
        updateWrapper.set(ResourceDetailDO::getBandWidth, null);
        mapper.update(new ResourceDetailDO(), updateWrapper);
    }

    public void clearRelatedInfoById(Long id) {
        LambdaUpdateWrapper<ResourceDetailDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ResourceDetailDO::getId, id);
        updateWrapper.set(ResourceDetailDO::getVmId, null);
        updateWrapper.set(ResourceDetailDO::getEcsName, null);
        updateWrapper.set(ResourceDetailDO::getRelatedDeviceId, null);
        updateWrapper.set(ResourceDetailDO::getRelatedDeviceName, null);
        updateWrapper.set(ResourceDetailDO::getRelatedDeviceType, null);
        mapper.update(new ResourceDetailDO(), updateWrapper);
    }

    public void updateEvsInfoById(ResourceDetailDO dto){
        LambdaUpdateWrapper<ResourceDetailDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ResourceDetailDO::getId, dto.getId());
        updateWrapper.set(ResourceDetailDO::getVolumeId, dto.getVolumeId());
        updateWrapper.set(ResourceDetailDO::getDataDisk, dto.getDataDisk());
        mapper.update(new ResourceDetailDO(), updateWrapper);
    }





    public void updateHandoverStatusByConfigId(String configId, String handoverStatus) {
        mapper.updateHandoverStatusByConfigId(configId, handoverStatus);
    }

    public List<ResourceDetailDO> selectByExpireTime(String time, String endTime) {
        return mapper.selectByExpireTime(time, endTime);
    }
    public List<ResourceDetailDO> selectByExpireTimeThreeDay() {
        return mapper.selectByExpireTimeThreeDay();
    }
    public List<ResourceDetailDO> selectExpireDetail() {
        return mapper.selectExpireDetail();
    }
}
