package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.network.NetworkSubnetOrder;
import com.datatech.slgzt.model.dto.network.CreateNetworkSubnetRcDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface NetworkSubnetOrderManagerConvert {



    @Mapping(target = "name", source = "subnetName")
    @Mapping(target = "ipVersionStr", source = "ipVersion")
    @Mapping(target = "ipVersion",ignore = true)
    @Mapping(target = "netGlobalId", source = "networkId")
    @Mapping(target = "orderId", source = "id")
    @Mapping(target = "gatewayIp", source = "gateway")
    @Mapping(target = "globalId", source = "id")
    @Mapping(target = "allocationPools", ignore = true)
    CreateNetworkSubnetRcDTO do2RcDTO(NetworkSubnetOrder orderDO);

}
