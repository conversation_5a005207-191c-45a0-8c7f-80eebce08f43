package com.datatech.slgzt.impl.network;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.annotation.Lock;
import com.datatech.slgzt.consumer.BatchRestartConsumer;
import com.datatech.slgzt.convert.ResourceDetailManagerConvert;
import com.datatech.slgzt.convert.VpcOrderManagerConvert;
import com.datatech.slgzt.dao.RegionDAO;
import com.datatech.slgzt.dao.mapper.VmMapper;
import com.datatech.slgzt.dao.mapper.network.VpcOrderMapper;
import com.datatech.slgzt.dao.mapper.network.VpcSubnetOrderMapper;
import com.datatech.slgzt.dao.model.RegionDO;
import com.datatech.slgzt.dao.model.ResourceDetailDO;
import com.datatech.slgzt.dao.model.vpc.CreateVpcSubnetReq;
import com.datatech.slgzt.dao.model.vpc.VpcOrder;
import com.datatech.slgzt.dao.model.vpc.VpcSubnetOrder;
import com.datatech.slgzt.enums.RecoveryStatusEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.enums.SourceTypeEnum;
import com.datatech.slgzt.enums.domain.CatalogueDomain;
import com.datatech.slgzt.enums.ip.IpLevelEnum;
import com.datatech.slgzt.enums.network.NetworkPrefixEnum;
import com.datatech.slgzt.enums.network.NetworkStatusEnum;
import com.datatech.slgzt.enums.network.VpcVerifyEnum;
import com.datatech.slgzt.exception.BusinessException;
import com.datatech.slgzt.handle.ResourceHandle;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.*;
import com.datatech.slgzt.model.BatchRestartModel;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.KafkaMessage;
import com.datatech.slgzt.model.business.CmpAppDTO;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.dto.business.BusinessService;
import com.datatech.slgzt.model.dto.network.*;
import com.datatech.slgzt.model.dto.vpc.*;
import com.datatech.slgzt.model.query.RegionQuery;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.model.vo.callback.TaskVO;
import com.datatech.slgzt.model.vo.network.ip.IpUpdateStatusVO;
import com.datatech.slgzt.model.vo.vpc.NetOpenParamVerify;
import com.datatech.slgzt.model.vo.vpc.VpcOrderResult;
import com.datatech.slgzt.model.vo.vpc.VpcSubnetOrderResult;
import com.datatech.slgzt.model.vo.vpc.VpcTableVo;
import com.datatech.slgzt.service.OrderDataProvideService;
import com.datatech.slgzt.service.PlatformService;
import com.datatech.slgzt.service.network.NetworkCollectService;
import com.datatech.slgzt.service.network.VpcMessageService;
import com.datatech.slgzt.utils.*;
import com.datatech.slgzt.warpper.PageWarppers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: suxin
 * @Date: 2024/11/18
 * @Description: vpc校验服务
 */
@Slf4j
@Service
public class VpcMessageServiceImpl implements VpcMessageService {

    @Resource
    private ResourceHandle resourceHandle;

    @Resource
    private PlatformService platformService;

    @Resource
    private VpcOrderMapper vpcOrderMapper;
    @Resource
    private VpcSubnetOrderMapper vpcSubnetOrderMapper;
    @Resource
    private DgRecoveryOrderProductManager dgRecoveryOrderProductManager;
    @Resource
    private DgRecoveryOrderManager dgRecoveryOrderManager;

    @Resource
    private VmMapper vmMapper;
    @Resource
    private NetworkCollectService networkService;

    @Resource
    private OrderDataProvideService orderDataProvideService;

    @Resource
    private ResourceDetailManagerConvert resourceDetailManagerConvert;

    @Resource
    private RegionDAO regionDAO;

    @Resource
    private VpcOrderManagerConvert vpcOrderConvert;

    @Resource
    private BusinessService businessService;

    @Resource
    private RecoveryWorkOrderProductManager recoveryWorkOrderProductManager;
    @Resource
    private TenantManager tenantManager;

    @Resource
    private RegionManager regionManager;

    @Resource
    private KafkaTemplate<String, Object> kafkaTemplate;

    @Resource
    private RedissonClient redissonClient;

    private static final String redisKeyPrefix = "dag:vpc:";


    @Override
    public CommonResult<List<VpcOrderResult>> selectVpcList(VpcTableVo vpcTableVo) {
        OrderCommonDTO oacOrder = orderDataProvideService.getOrderCommon(vpcTableVo.getOrderId());
        vpcTableVo.setTenantId(oacOrder.getTenantId());
        // vpcTableVo.setSourceType(oacOrder.getSourceType());
        List<VpcOrderResult> list = vpcOrderMapper.selectVpcWithSubnetList(vpcTableVo);
        return CommonResult.success(list);
    }

    @Override
    public CommonResult<List<VpcOrderResult>> corporateVpcTree(VpcTableVo vpcTableVo) {
        List<VpcOrderResult> list = vpcOrderMapper.selectVpcWithSubnetList(vpcTableVo);
        return CommonResult.success(list);
    }

    @Override
    public CommonResult<List<VpcOrderResult>> queryVpc(VpcTableVo vpcTableVo) {
        vpcTableVo.setSourceType(SourceTypeEnum.EXTERNAL.getPrefix());
        List<VpcOrderResult> list = vpcOrderMapper.selectVpcWithSubnetListEx(vpcTableVo);
        for (VpcOrderResult vpcOrderResult : list) {
            List<VpcSubnetOrderResult> vpcSubnetOrderList = vpcOrderResult.getVpcSubnetOrderList().stream().filter(o -> o.getStatus().equals("SUCCESS") && o.getDeleted() == 1).collect(Collectors.toList());
            vpcOrderResult.setVpcSubnetOrderList(vpcSubnetOrderList);
        }
        return CommonResult.success(list);
    }

    @Override
    public PageResult<VpcOrderResult> queryVpcListPage(VpcTableVo vpcTableVo) {
        if (QueryParamCheckUtil.containsPercentage(vpcTableVo)) {
            return new PageResult();
        }
        vpcTableVo.setSourceType(SourceTypeEnum.EXTERNAL.getPrefix());
        PageHelper.startPage(vpcTableVo.getPageNum(), vpcTableVo.getPageSize());
        List<VpcOrderResult> list = vpcOrderMapper.selectVpcOrderList(vpcTableVo);
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(record -> {
                String recoveryStatusCn = RecoveryStatusEnum.getRemarkByType(record.getRecoveryStatus());
                record.setRecoveryStatusCn(recoveryStatusCn);
            });
        }
        return PageWarppers.box(new PageInfo<>(list));
    }

    @Override
    @Transactional
    public CommonResult<String> vpcCreate(VpcCreateDTO vpcCreateDTO) {
        Long userId = UserHelper.INSTANCE.getCurrentUserId();
        OrderCommonDTO oacOrder = orderDataProvideService.getOrderCommon(vpcCreateDTO.getOrderId());
        for (VpcOrderDTO vpcOrderDTO : vpcCreateDTO.getNetworks()) {
            if (vpcOrderMapper.selectVpcName(vpcCreateDTO.getRegionCode(), vpcOrderDTO.getVpcName(), oacOrder.getTenantId(), vpcOrderDTO.getCidr()) > 0) {
                throw new BusinessException(vpcOrderDTO.getVpcName() + "：vpc已存在，请勿重复创建");
            }
        }
        TenantDTO tenantDTO = tenantManager.getById(oacOrder.getTenantId());
        if (null != oacOrder.getBusinessSysId()) {
            vpcCreateDTO.setBusinessSysId(oacOrder.getBusinessSysId());
            CmpAppDTO cmpAppDTO = businessService.getById(oacOrder.getBusinessSysId());
            vpcCreateDTO.setBusinessSysName(cmpAppDTO.getSystemName());
            vpcCreateDTO.setModuleId(oacOrder.getModuleId());
            vpcCreateDTO.setModuleName(oacOrder.getModuleName());
        }
        vpcCreateDTO.setOrderCode(oacOrder.getOrderCode());
        vpcCreateDTO.setTenantName(tenantDTO.getName());
        vpcCreateDTO.setApplyUserId(oacOrder.getUserId());
        vpcCreateDTO.setApplyUserName(oacOrder.getUserName());
        vpcCreateDTO.setSourceType(oacOrder.getSourceType());
        vpcCreateDTO.setBillId(oacOrder.getBillId());
        RegionDTO regionDTO = regionManager.getByCode(vpcCreateDTO.getRegionCode());
        String domainCode = regionDTO.getDomainCode();
        CatalogueDomain domain = CatalogueDomain.getByCode(domainCode);
        vpcCreateDTO.setDomainCode(domainCode);
        vpcCreateDTO.setDomainName(domain.getName());
        vpcCreateDTO.setCatalogueDomainCode(domain.getParent().getCode());
        vpcCreateDTO.setCatalogueDomainName(domain.getParent().getName());
        vpcCreateDTO.setUserId(userId);
        // t设置租户id
        Long bottomTenantId = platformService.getOrCreateTenantId(oacOrder.getBillId(), vpcCreateDTO.getRegionCode());
        vpcCreateDTO.setBottomTenantId(bottomTenantId);
        vpcCreateDTO.setTenantId(oacOrder.getTenantId());
        return createVpc(vpcCreateDTO);
    }

    @Override
    @Transactional
    public CommonResult<String> vpcCreateBatch(VpcCreateDTO vpcCreateDTO) {
        UserCenterUserDTO userCenter = UserHelper.INSTANCE.getCurrentUser();
        for (VpcOrderDTO vpcOrderDTO : vpcCreateDTO.getNetworks()) {
            if (vpcOrderMapper.selectVpcName(vpcCreateDTO.getRegionCode(), vpcOrderDTO.getVpcName(), vpcCreateDTO.getTenantId(), vpcOrderDTO.getCidr()) > 0) {
                throw new BusinessException(vpcOrderDTO.getVpcName() + "：vpc已存在，请勿重复创建");
            }
        }
        TenantDTO tenantDTO = tenantManager.getById(vpcCreateDTO.getTenantId());
        vpcCreateDTO.setTenantName(tenantDTO.getName());
        vpcCreateDTO.setApplyUserId(userCenter.getId());
        vpcCreateDTO.setApplyUserName(userCenter.getUserName());
        vpcCreateDTO.setSourceType(vpcCreateDTO.getSourceType());
        vpcCreateDTO.setUserId(userCenter.getId());
        vpcCreateDTO.setBillId(tenantDTO.getBillId());
        vpcCreateDTO.setOrderId("0");
        // t设置租户id
        Long bottomTenantId = platformService.getOrCreateTenantId(tenantDTO.getBillId(), vpcCreateDTO.getRegionCode());
//        Precondition.checkArgument(bottomTenantId, "当前业务系统和资源池下不存在创建中或已完成工单，暂时无法开通VPC");
        vpcCreateDTO.setBottomTenantId(bottomTenantId);
        return createVpc(vpcCreateDTO);
    }

    private CommonResult<String> createVpc(VpcCreateDTO vpcCreateDTO) {
        for (VpcOrderDTO vpcOrderDTO : vpcCreateDTO.getNetworks()) {
            // 组装vpc_order参数
            VpcOrder vpcOrder = getVpcOrder(vpcOrderDTO, vpcCreateDTO);
            //获取统一订购参数
            CreateVpcAndSubnetRcDTO createVpcDto = getCreateVpc(vpcOrder, vpcOrderDTO, vpcCreateDTO);
            List<CreateVpcAndSubnetRcDTO.CreateSubnetRcDto> createSubnetDtoList = new ArrayList<>();
            createVpcDto.setSubnetDTOList(createSubnetDtoList);
            List<VpcSubnetOrder> vpcSubnetOrderList = new ArrayList<>();
            createList(createSubnetDtoList, vpcSubnetOrderList, vpcOrderDTO, vpcOrder, vpcCreateDTO);
            vpcOrderMapper.insert(vpcOrder);
            if (!CollectionUtils.isEmpty(vpcSubnetOrderList)) {
                vpcSubnetOrderMapper.batchInsertVpcSubnet(vpcSubnetOrderList);
            }
            log.info("vpc创建请求参数 createVpcDto:{}", JSONObject.toJSONString(createVpcDto));
            String requestUrl = resourceHandle.resourceCenterUrl + resourceHandle.getVpcCreateVpcAndSubVerify();
            Map<String, Object> response = HttpClientUtil.post(requestUrl, JSONObject.toJSONString(createVpcDto), getHeaders());
            log.info("vpc创建返回参数 response:{}", response);
            Integer code = (Integer) response.get("code");
            String json = (String) response.get("json");
            if (code != 200) {
                String message = "vpc创建失败";
                if (StringUtils.isNotEmpty(json)) {
                    JSONObject jsonObject = JSONObject.parseObject(json);
                    message = jsonObject.get("message").toString();
                }
                log.error("vpc创建失败 error {}", message);
                StatusDTO statusDto = new StatusDTO();
                statusDto.setId(vpcOrder.getId());
                statusDto.setStatus(NetworkStatusEnum.ERROR.getType());
                statusDto.setMessage(JSONObject.toJSONString(response));
                vpcOrderMapper.updateStatus(statusDto);
                // throw new BusinessException(message);
            }

        }
        return CommonResult.success("vpc下发成功");
    }

    @Override
    // vpc回调特别的快，必须先将事务提交，数据插入到db中
    // 这里的逻辑就是先插入db，然后等待kafka回调更新状态，外部是否出问题，都可以插入数据到里面
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public CommonResult<String> createVpcDag(VpcCreateDTO vpcCreateDTO) {
        return createVpc(vpcCreateDTO);
    }

    @Override
    @Transactional
    public CommonResult<VpcSubnetCreateDTO> createSubnet(VpcSubnetDTO subnetCreateDTO, String vpcId) {
//        UserCenterUserDTO userCenter = UserHelper.INSTANCE.getCurrentUser();
        VpcOrder vpcOrder = vpcOrderMapper.selectByIdAndStatus(vpcId);
        Precondition.checkArgument(vpcOrder, "未查找到对应vpc");
        String cidr = subnetCreateDTO.getStartIp() + "/" + subnetCreateDTO.getNetmask();
        if (!IPInSubnetChecker.isSubnetOf(cidr, vpcOrder.getIpv4Cidr())) {
            return CommonResult.failure("子网网段不在vpc网段内");
        }
        VpcSubnetOrder vpcSubnetOrder = new VpcSubnetOrder();
        String subnetId = UuidUtil.getGid(NetworkPrefixEnum.VPC_SUB.getType());
        vpcSubnetOrder.setId(subnetId);
        vpcSubnetOrder.setVpcId(vpcOrder.getId());
        vpcSubnetOrder.setCreatedTime(new Date());
        vpcSubnetOrder.setSubnetName(subnetCreateDTO.getSubnetName());
        String azCode = vpcOrder.getAzCode();
        if (StringUtils.isNotEmpty(subnetCreateDTO.getAzCode())) {
            azCode = subnetCreateDTO.getAzCode();
        }
        vpcSubnetOrder.setAzCode(azCode);
        vpcSubnetOrder.setCidr(cidr);
        vpcSubnetOrder.setStartIp(subnetCreateDTO.getStartIp());

        vpcSubnetOrder.setNetmask(subnetCreateDTO.getNetmask());
        vpcSubnetOrder.setDeleted(1);
        vpcSubnetOrder.setInstanceId(subnetCreateDTO.getInstanceId());
        vpcSubnetOrder.setLevel2InstanceId(subnetCreateDTO.getLevel2InstanceId());
        vpcSubnetOrder.setStatus(NetworkStatusEnum.EXECUTING.getType());
        vpcSubnetOrder.setUuid(subnetCreateDTO.getUuid());
        if (subnetCreateDTO.getAllocationPools() != null) {
            vpcSubnetOrder.setAllocationPools(JSONObject.toJSONString(subnetCreateDTO.getAllocationPools()));
        }
        CreateVpcSubnetReq createVpcSubnetReq = new CreateVpcSubnetReq();
        createVpcSubnetReq.setName(subnetCreateDTO.getSubnetName());
        createVpcSubnetReq.setBillId(vpcOrder.getAccount());
        createVpcSubnetReq.setRegionCode(vpcOrder.getRegionCode());
        createVpcSubnetReq.setCidr(cidr);
        createVpcSubnetReq.setAzCode(azCode);
        createVpcSubnetReq.setVpcId(vpcOrder.getId());
        createVpcSubnetReq.setGlobalId(subnetId);
        createVpcSubnetReq.setOrderId(subnetId);
        createVpcSubnetReq.setSystemSource("NFVO");
        createVpcSubnetReq.setGatewayIp(GatewayCalculatorUtil.getGateway(cidr));
        createVpcSubnetReq.setIpVersion(4);
        createVpcSubnetReq.setAllocationPools(subnetCreateDTO.getAllocationPools());
        subnetCreateDTO.setId(subnetId);
        vpcSubnetOrderMapper.insert(vpcSubnetOrder);
        String requestUrl = resourceHandle.resourceCenterUrl + resourceHandle.getSubnetCreate();
        log.info("vpc子网创建入参：{}", JSONObject.toJSONString(createVpcSubnetReq));
        Map<String, Object> response = HttpClientUtil.post(requestUrl, JSONObject.toJSONString(createVpcSubnetReq), getHeaders());
        log.info("vpc子网创建返回参数 response:{}", response);
        Integer code = (Integer) response.get("code");
        String json = (String) response.get("json");
        String message = "";
        if (code == 200) {
            JSONObject jsonObject = JSONObject.parseObject(json);
            String success = jsonObject.getString("success");
            if (!"1".equals(success)) {
                message = jsonObject.get("message").toString();
                log.error("vpc子网创建失败 error {}", message);
                throw new BusinessException("vpc子网创建失败 : " + message);
            }
        } else {
            message = "网络请求失败";
            if (StringUtils.isNotBlank(json)) {
                JSONObject jsonObject = JSONObject.parseObject(json);
                message = jsonObject.get("message").toString();
            }
            log.error("vpc子网创建失败 {}", message);
            throw new BusinessException("vpc子网创建失败 : " + message);
        }
        return CommonResult.success(message, subnetCreateDTO);

    }


    @Override
    @Lock(prefixKey = "key-consumeSubnetMessage", waitTime = 15000, key = {"#taskVO.orderId"})
    public void consumeSubnetMessage(TaskVO taskVO) {
        if (NetworkStatusEnum.EXECUTING.getType().equals(taskVO.getStatus())) {
            return;
        }
        log.info("consumeSubnetMessage start taskVO:{}", JSONObject.toJSONString(taskVO));
        StatusDTO statusDto = new StatusDTO();
        statusDto.setId(taskVO.getOrderId());
        statusDto.setStatus(taskVO.getStatus());
        statusDto.setCurrentStatus(NetworkStatusEnum.EXECUTING.getType());
        if (NetworkStatusEnum.ERROR.getType().equals(taskVO.getStatus())) {
            statusDto.setMessage(taskVO.getMessage());
        }
        vpcSubnetOrderMapper.updateStatusById(statusDto);
        updateNum(taskVO.getOrderId());
        sendBatchJobMessage(taskVO, false);
    }

    private void sendBatchJobMessage(TaskVO taskVO, boolean isVpc) {
        try {
            // vpc回调太快了，
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            log.warn("sleep error, taskVO: {}, isVpc: {}, error message: {}", taskVO, isVpc, e.getMessage());
        }
        String key;
        if (isVpc) {
            key = redisKeyPrefix + taskVO.getOrderId() + ":all";
        } else {
            VpcSubnetOrder subnetDO = vpcSubnetOrderMapper.selectById(taskVO.getOrderId());
            if (subnetDO == null) {
                log.warn("subnetDO is null, taskVO:{}", taskVO);
                return;
            }
            key = redisKeyPrefix + subnetDO.getVpcId() + ":" + subnetDO.getId();
        }
        RMap<String, String> map = redissonClient.getMap(key);
        if (map != null) {
            String subOrderId = map.get("subOrderId");
            String jobId = map.get("jobExecutionId");
            if (StringUtils.isNotBlank(subOrderId) && StringUtils.isNotBlank(jobId)) {
                BatchRestartModel batchRestartModel = new BatchRestartModel()
                        .setJobExecutionId(Long.parseLong(jobId))
                        .setSubOrderId(subOrderId)
                        .setProductType("vpc")
                        .setOrderType(1);
                if (NetworkStatusEnum.ERROR.getType().equals(taskVO.getStatus())) {
                    batchRestartModel.setOpenStatus((ResOpenEnum.OPEN_FAIL.getCode()));
                    batchRestartModel.setMessage(taskVO.getMessage());
                } else {
                    batchRestartModel.setOpenStatus((ResOpenEnum.OPEN_SUCCESS.getCode()));
                }
                kafkaTemplate.send(BatchRestartConsumer.DAG_BATCH_RESTART, KafkaMessage.of(batchRestartModel));
            } else {
                log.warn("subOrderId is blank or jobId is blank, taskVO:{}, subOrderId:{}, jobId:{}", taskVO, subOrderId, jobId);
            }
        } else {
            log.info("vpcSubnetOrder is null, taskVO:{}", taskVO);
        }

    }

    public void updateNum(String id) {
        VpcSubnetOrder vpcSubnetOrder = vpcSubnetOrderMapper.selectById(id);
        Integer count = vpcSubnetOrderMapper.selectCountByVpcIdSuccess(vpcSubnetOrder.getVpcId());
        vpcOrderMapper.updateSubnetNumById(vpcSubnetOrder.getVpcId(), count);
    }


    @Override
    public CommonResult<String> getVpcStatus(String orderId) {
        StringBuffer sb = new StringBuffer();
        List<VpcOrder> list = vpcOrderMapper.selectVpcByOrderId(orderId);
        if (CollectionUtils.isNotEmpty(list)) {
            List<VpcOrder> vpcList = list.stream().filter(v -> !NetworkStatusEnum.SUCCESS.getType().equals(v.getStatus())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(vpcList)) {
                return CommonResult.success();
            }
            List<String> ids = new ArrayList<>();
            for (VpcOrder vpcOrder : vpcList) {
                TaskVO taskVO = vmMapper.getVpcByOrderId(vpcOrder.getId());
                if (null == taskVO || StringUtils.isBlank(taskVO.getStatus())) {
                    sb.append(vpcOrder.getVpcName()).append(",");
                    continue;
                }
                if (!NetworkStatusEnum.SUCCESS.getType().equals(taskVO.getStatus())) {
                    sb.append(vpcOrder.getVpcName()).append(",");
                } else {
                    ids.add(vpcOrder.getId());
                }
                vpcOrder.setStatus(taskVO.getStatus());
                if (NetworkStatusEnum.ERROR.getType().equals(taskVO.getStatus()) && StringUtils.isNotBlank(taskVO.getMessage())) {
                    vpcOrder.setDescription(taskVO.getMessage());
                }
                vpcOrderMapper.updateStatusById(vpcOrder);
            }
            if (CollectionUtils.isNotEmpty(ids)) {
                ipUpdateStatus(ids);
            }
            if (StringUtils.isNotEmpty(sb.toString())) {
                sb.append("上述VPC未开通成功，是否忽略并继续后续流程");
                return CommonResult.success(sb.toString(), "");
            }
        }
        return CommonResult.success();
    }

    @Override
    @Lock(prefixKey = "key-consumeVpcMessage", waitTime = 15000, key = {"#taskVO.orderId"})
    public void consumeVpcMessage(TaskVO taskVO) {
        StatusDTO statusDto = new StatusDTO();
        statusDto.setCurrentStatus(NetworkStatusEnum.EXECUTING.getType());
        statusDto.setId(taskVO.getOrderId());
        statusDto.setStatus(taskVO.getStatus());
        if (NetworkStatusEnum.ERROR.getType().equals(taskVO.getStatus())) {
            statusDto.setMessage(taskVO.getMessage());
        }
        vpcOrderMapper.updateStatus(statusDto);
        vpcSubnetOrderMapper.updateStatusByVpcId(statusDto);
        if (NetworkStatusEnum.SUCCESS.getType().equals(taskVO.getStatus())) {
            List<String> ids = new ArrayList<>();
            ids.add(taskVO.getOrderId());
            ipUpdateStatus(ids);
        }
        // vpc成功子网就成功了，就可以重启了
        sendBatchJobMessage(taskVO, true);

    }

    private void ipUpdateStatus(List<String> ids) {
        for (String id : ids) {
            List<VpcSubnetOrder> list = vpcSubnetOrderMapper.selectByVpcId(id);
            for (VpcSubnetOrder vpcSubnetOrder : list) {
                if (StringUtils.isBlank(vpcSubnetOrder.getInstanceId())) {
                    continue;
                }
                IpUpdateStatusVO ipUpdateStatusRc = new IpUpdateStatusVO();
                ipUpdateStatusRc.setStatus("1");
                ipUpdateStatusRc.setIpVersion(IpLevelEnum.IP3);
                List<String> instanceIds = new ArrayList<>();
                instanceIds.add(vpcSubnetOrder.getInstanceId());
                ipUpdateStatusRc.setInstanceId(instanceIds);
                networkService.ipUpdateStatus(ipUpdateStatusRc);
            }
        }
    }

    @Override
    public CommonResult<NetworkDetailDTO> getVpcDetail(String id) {
        NetworkDetailDTO networkDetailDTO = vpcOrderMapper.selectDetailById(id);
        List<VpcSubnetOrder> list = vpcSubnetOrderMapper.selectByVpcIdSuccess(id);
        if (CollectionUtils.isNotEmpty(list)) {
            List<String> uuids = list.stream().map(VpcSubnetOrder::getUuid).filter(uuid -> StringUtils.isNotEmpty(uuid)).collect(Collectors.toList());
            networkDetailDTO.setUuids(uuids);
        }
        return CommonResult.success(networkDetailDTO);
    }


    @Override
    public List<ResourceDetailDTO> selectRecentVpc(List<String> businessIds, Integer recoveryType) {
        List<ResourceDetailDO> detailDOS = vpcOrderMapper.selectRecentVpc(businessIds, recoveryType, SourceTypeEnum.STANDARD.getPrefix());
        return resourceDetailManagerConvert.doList2dtoList(detailDOS);
    }


    @Override
    public void vpcRecycle(NetworkConfirmDTO networkConfirmDTO, Long userId) {
        // todo 添加状态
        RecoveryWorkOrderProductDTO recoveryWorkOrderProductDTO = recoveryWorkOrderProductManager.getById(networkConfirmDTO.getId());
        VpcOrder vpcOrder = vpcOrderMapper.selectByIdAndRecoveryStatus(recoveryWorkOrderProductDTO.getResourceDetailId(), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
        if (vpcOrder == null) {
            log.error("vpcRecycle vpc不是待回收状态，不回收 id:{}", networkConfirmDTO.getId());
            return;
        }
        vpcRecycle(vpcOrder, userId);
    }


    @Override
    public void vpcRecycle(String vpcId, Long userId, Integer status) {
        // todo 添加状态
        VpcOrder vpcOrder = vpcOrderMapper.selectByIdAndRecoveryStatus(vpcId, status);
        Precondition.checkArgument(vpcOrder, "pcRecycle vpc不是待回收状态，不回收");
        vpcRecycle(vpcOrder, userId);
    }


    public void vpcRecycle(VpcOrder vpcOrder, Long userId) {
        RegionQuery query = new RegionQuery();
        query.setCode(vpcOrder.getRegionCode());
        List<RegionDO> list = regionDAO.list(query);
        if (CollectionUtils.isNotEmpty(list) && CatalogueDomain.VMWARE.getCode().equals(list.get(0).getDomainCode())) {
            vpcOrder.setUpdatedBy(userId);
            updateRecoveryTime(vpcOrder);
            return;
        }
        vpcOrderMapper.updateRecoveryStatusById(vpcOrder.getId(), RecoveryStatusEnum.RECOVERING.getType(), 1, userId, null);
        recoveryWorkOrderProductManager.updateByResourceDetailId(vpcOrder.getId(), RecoveryStatusEnum.RECOVERING.getType().toString(), null);
        vpcSubnetRecycle(vpcOrderConvert.convert(vpcOrder));
    }


    @Override
    @Lock(prefixKey = "key-recycleVpcMessage", waitTime = 15000, key = {"#taskVO.orderId"})
    public void recycleVpcMessage(TaskVO taskVO) {
        if (NetworkStatusEnum.SUCCESS.getType().equals(taskVO.getStatus())) {
            vpcOrderMapper.updateRecoveryStatusById(taskVO.getOrderId(), RecoveryStatusEnum.RECOVERY_COMPLETE.getType(), 0, null, null);
            recoveryWorkOrderProductManager.updateByResourceDetailId(taskVO.getOrderId(), RecoveryStatusEnum.RECOVERY_COMPLETE.getType().toString(), null);
            dgRecoveryLogic(taskVO, true);
        } else if (NetworkStatusEnum.ERROR.getType().equals(taskVO.getStatus())) {
            vpcOrderMapper.updateRecoveryStatusById(taskVO.getOrderId(), RecoveryStatusEnum.RECLAIM_FAILURE.getType(), 1, null, taskVO.getMessage());
            recoveryWorkOrderProductManager.updateByResourceDetailId(taskVO.getOrderId(), RecoveryStatusEnum.RECLAIM_FAILURE.getType().toString(), taskVO.getMessage());
            dgRecoveryLogic(taskVO, false);
        }

    }

    /**
     * 对公回收逻辑
     * <p> 根据resourceDetailId和recoveryStatus 找到 回收中的 product，更新状态，如果是成功，重启batch
     *
     * @param taskVO
     * @param success
     */
    private void dgRecoveryLogic(TaskVO taskVO, boolean success) {
        log.info("dgRecoveryLogic start, taskVO:{}, success:{}", taskVO, success);
        List<DgRecoveryOrderProductDTO> dgRecoveryOrderProductDTOS =
                dgRecoveryOrderProductManager.listByResourceDetailId(taskVO.getOrderId(), RecoveryStatusEnum.RECOVERING);
        log.info("dgRecoveryOrderProductDTOS:{}", dgRecoveryOrderProductDTOS);
        if (CollectionUtils.isNotEmpty(dgRecoveryOrderProductDTOS)) {
            dgRecoveryOrderProductDTOS.forEach(dgRecoveryOrderProductDTO -> {
                DgRecoveryOrderProductDTO updateDTO = new DgRecoveryOrderProductDTO();
                updateDTO.setId(dgRecoveryOrderProductDTO.getId());
                if (success) {
                    updateDTO.setRecoveryStatus(RecoveryStatusEnum.RECOVERY_COMPLETE.getType());
                } else {
                    updateDTO.setRecoveryStatus(RecoveryStatusEnum.RECLAIM_FAILURE.getType());
                    updateDTO.setMessage(taskVO.getMessage());
                }
                dgRecoveryOrderProductManager.update(updateDTO);
                DgRecoveryOrderDTO dgRecoveryOrderDTO = dgRecoveryOrderManager.getById(dgRecoveryOrderProductDTO.getWorkOrderId());
                if (dgRecoveryOrderDTO == null) {
                    log.warn("dgRecoveryOrderDTO is null, taskVO:{}", taskVO);
                    return;
                }
                if (success) {
                    // 重启Batch
                    kafkaTemplate.send(BatchRestartConsumer.CORPORATE_BATCH_RESTART,
                            dgRecoveryOrderDTO.getId(),
                            KafkaMessage.of(new BatchRestartModel()
                                    .setJobExecutionId(dgRecoveryOrderDTO.getJobExecutionId())
                                    // 已更新product，不需要它更新
                                    .setRestartOnly(true)));
                }
            });
        }
    }

    @Override
    @Lock(prefixKey = "key-vpcSubnetRecycle", waitTime = 15000, key = {"#vpcOrder.id"})
    public void vpcSubnetRecycle(VpcOrderExtDTO vpcOrder) {
        List<VpcSubnetOrder> vpcSubnetOrderList = vpcSubnetOrderMapper.selectByVpcId(vpcOrder.getId());
        if (CollectionUtils.isEmpty(vpcSubnetOrderList)) {
            return;
        }
        for (VpcSubnetOrder vpcSubnetOrder : vpcSubnetOrderList) {
            if (RecoveryStatusEnum.RECOVERING.getType().equals(vpcSubnetOrder.getRecoveryStatus())
                    || RecoveryStatusEnum.RECOVERY_COMPLETE.getType().equals(vpcSubnetOrder.getRecoveryStatus())
                    || RecoveryStatusEnum.RECLAIM_FAILURE.getType().equals(vpcSubnetOrder.getRecoveryStatus())) {
                continue;
            }
            SubnetRcDTO subnetRcDto = new SubnetRcDTO();
            subnetRcDto.setBillId(vpcOrder.getAccount());
            subnetRcDto.setRegionCode(vpcOrder.getRegionCode());
            subnetRcDto.setInstanceId(vpcSubnetOrder.getId());
            subnetRcDto.setId(vpcSubnetOrder.getId());
            subnetRcDto.setVpcId(vpcSubnetOrder.getVpcId());
            subnetRcDto.setOptUuid(UuidUtil.getUUID());
            log.info("子网删除请求参数 vpcSubnetRecycle:{}", JSONObject.toJSONString(subnetRcDto));
            String requestUrl = resourceHandle.resourceCenterUrl + resourceHandle.getSubnetDelete();
            Map<String, Object> response = HttpClientUtil.post(requestUrl, JSONObject.toJSONString(subnetRcDto), getHeaders());
            log.info("子网删除请求参数 vpcSubnetRecycle response:{}", response);
            Integer code = (Integer) response.get("code");
            boolean isError = false;
            String message = "";
            if (code != 200) {
                isError = true;
                message = JSONObject.toJSONString(response);
                log.error("子网删除失败 error {}", response);
            }
            String json = (String) response.get("json");
            JSONObject jsonObject = JSONObject.parseObject(json);
            String success = jsonObject.getString("success");
            if (!"1".equals(success)) {
                isError = true;
                message = jsonObject.get("message").toString();
                log.error("子网删除失败 error {}", jsonObject.get("message").toString());
            }
            if (isError) {
                vpcSubnetOrderMapper.updateRecoveryStatusById(vpcSubnetOrder.getId(), RecoveryStatusEnum.RECLAIM_FAILURE.getType(), 1, message);
                vpcOrderMapper.updateRecoveryStatusById(vpcSubnetOrder.getVpcId(), RecoveryStatusEnum.RECLAIM_FAILURE.getType(), 1, null, message);
                recoveryWorkOrderProductManager.updateByResourceDetailId(vpcSubnetOrder.getVpcId(), RecoveryStatusEnum.RECLAIM_FAILURE.getType().toString(), message);
                return;
            }
            vpcSubnetOrderMapper.updateRecoveryStatusById(vpcSubnetOrder.getId(), RecoveryStatusEnum.RECOVERING.getType(), 0, null);
        }
    }


    @Override
    public List<Long> selectBusinessIdByVpcIds(List<String> goodsIds) {
        List<VpcOrder> vpcOrders = vpcOrderMapper.selectBatchIds(goodsIds);
        List<Long> tenantIds = vpcOrders.stream().map(VpcOrder::getTenantId).collect(Collectors.toList());
        return businessService.selectBusinessSystemByTenantId(tenantIds);
    }

    @Override
    public List<VpcOrderResult> selectByIds(List<String> goodsIdList, Map<String, Long> resourceMap) {
        List<VpcOrderResult> vpcOrderResults = vpcOrderMapper.selectVpcRecoveryList(goodsIdList);
        vpcOrderResults.forEach(vpcOrderResult -> {
            Long storageId = resourceMap.getOrDefault(vpcOrderResult.getId(), 0L);
            vpcOrderResult.setStorageId(storageId);
        });
        return vpcOrderResults;
    }

    private void updateRecoveryTime(VpcOrder vpcOrder) {
        LocalDateTime futureDateTime = LocalDateTime.now().plusHours(180);
        Date date = Date.from(futureDateTime.atZone(ZoneId.systemDefault()).toInstant());
        vpcOrder.setRecoveryTime(date);
        vpcOrder.setRecoveryStatus(RecoveryStatusEnum.RECOVERING.getType());
        vpcOrder.setDeleted(1);
        vpcOrderMapper.updateRecoveryTimeById(vpcOrder);
        recoveryWorkOrderProductManager.updateByResourceDetailId(vpcOrder.getId(), RecoveryStatusEnum.RECOVERING.getType().toString(), null);
    }

    @Override
    @Lock(prefixKey = "key-recycleSubnetMessage", waitTime = 15000, key = {"#taskVO.orderId"})
    public void recycleSubnetMessage(TaskVO taskVO, String vpcId) {
        if (NetworkStatusEnum.SUCCESS.getType().equals(taskVO.getStatus())) {
            vpcSubnetOrderMapper.updateRecoveryStatusById(taskVO.getOrderId(), RecoveryStatusEnum.RECOVERY_COMPLETE.getType(), 0, null);
        } else if (NetworkStatusEnum.ERROR.getType().equals(taskVO.getStatus())) {
            vpcSubnetOrderMapper.updateRecoveryStatusById(taskVO.getOrderId(), RecoveryStatusEnum.RECLAIM_FAILURE.getType(), 1, taskVO.getMessage());
        }
        updateVpcRecoveryStatus(vpcId);
    }

    @Override
    public int selectRecoveryStatusByIds(List<String> vpcIds, Integer recoveryStatus) {
        List<VpcOrder> vpcOrders = vpcOrderMapper.selectByIds(vpcIds, recoveryStatus);
        return CollectionUtil.isNotEmpty(vpcOrders) ? vpcOrders.size() : 0;
    }

    @Override
    public void updateDetail(VpcOrderDTO vpcOrderDTO) {
        if (StringUtils.isBlank(vpcOrderDTO.getDetail())) {
            return;
        }
        VpcOrder vpcOrder = new VpcOrder();
        vpcOrder.setId(vpcOrderDTO.getVpcId());
        vpcOrder.setDetail(vpcOrderDTO.getDetail());
        vpcOrderMapper.updateDetailById(vpcOrder);

    }

    private void updateVpcRecoveryStatus(String vpcId) {
        List<VpcSubnetOrder> list = vpcSubnetOrderMapper.selectByVpcId(vpcId);
        // 1:SUCCESS 2:PENDING 4:EXECUTION 8:ERROR
        int index = 0;
        String message = "";
        for (VpcSubnetOrder vpcSubnet : list) {
            if (RecoveryStatusEnum.RECLAIM_FAILURE.getType().equals(vpcSubnet.getRecoveryStatus())) {
                index = Math.max(index, 8);
                message = vpcSubnet.getMessage();
                break;
            }
            if (RecoveryStatusEnum.RECOVERING.getType().equals(vpcSubnet.getRecoveryStatus())) {
                index = Math.max(index, 4);
                continue;
            }
            if (RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType().equals(vpcSubnet.getRecoveryStatus()) || RecoveryStatusEnum.ORDER_TO_BE_RECOVERED.getType().equals(vpcSubnet.getRecoveryStatus())) {
                index = Math.max(index, 2);
                continue;
            }
            if (RecoveryStatusEnum.RECOVERY_COMPLETE.getType().equals(vpcSubnet.getRecoveryStatus())) {
                index = Math.max(index, 1);
            }
        }
        if (index == 1 || index == 8) {
            Integer status = index == 1 ? RecoveryStatusEnum.RECOVERY_COMPLETE.getType() : RecoveryStatusEnum.RECLAIM_FAILURE.getType();
            vpcOrderMapper.updateSubnetRecoveryStatusById(vpcId, status, message);
            if (index == 1) {
                // 回收VPC
                deleteVpc(vpcId);
            }
        }
    }

    private void deleteVpc(String id) {
        VpcOrder vpcOrder = vpcOrderMapper.selectById(id);
        if (!RecoveryStatusEnum.RECOVERING.getType().equals(vpcOrder.getRecoveryStatus())) {
            log.info("vpc删除，状态不对，不执行 vpcOrder:{}", JSONObject.toJSONString(vpcOrder));
            return;
        }
        Long tenantId = platformService.getOrCreateTenantId(vpcOrder.getAccount(), vpcOrder.getRegionCode());
        DeleteNetworkRcDTO dto = new DeleteNetworkRcDTO();
        dto.setTenantId(tenantId);
        dto.setGlobalId(id);
        dto.setVpcId(id);
        log.info("vpc删除请求参数 DeleteNetworkRcDto:{}", JSONObject.toJSONString(dto));
        String requestUrl = resourceHandle.resourceCenterUrl + resourceHandle.getVpcDelete();
        Map<String, Object> response = HttpClientUtil.post(requestUrl, JSONObject.toJSONString(dto), getHeaders());
        log.info("vpc删除返回参数 DeleteNetworkRcDto response:{}", response);
        Integer code = (Integer) response.get("code");
        if (code != 200) {
            log.error("vpc删除失败 error {}", response);
            vpcOrderMapper.updateRecoveryStatusById(vpcOrder.getId(), RecoveryStatusEnum.RECLAIM_FAILURE.getType(), 1, null, JSONObject.toJSONString(response));
            recoveryWorkOrderProductManager.updateByResourceDetailId(vpcOrder.getId(), RecoveryStatusEnum.RECLAIM_FAILURE.getType().toString(), JSONObject.toJSONString(response));
            return;
        }
        String json = (String) response.get("json");
        JSONObject jsonObject = JSONObject.parseObject(json);
        String success = jsonObject.getString("success");
        if (!"1".equals(success)) {
            vpcOrderMapper.updateRecoveryStatusById(vpcOrder.getId(), RecoveryStatusEnum.RECLAIM_FAILURE.getType(), 1, null, jsonObject.get("message").toString());
            recoveryWorkOrderProductManager.updateByResourceDetailId(vpcOrder.getId(), RecoveryStatusEnum.RECLAIM_FAILURE.getType().toString(), jsonObject.get("message").toString());

            log.error("vpc删除失败 error {}", jsonObject.get("message").toString());
            return;
        }
        vpcOrderMapper.updateRecoveryStatusById(vpcOrder.getId(), RecoveryStatusEnum.NETWORK_RECOVERING.getType(), 1, null, null);
        recoveryWorkOrderProductManager.updateByResourceDetailId(vpcOrder.getId(), RecoveryStatusEnum.RECOVERING.getType().toString(), null);
    }


    private void createList(List<CreateVpcAndSubnetRcDTO.CreateSubnetRcDto> createSubnetDtoList,
                            List<VpcSubnetOrder> vpcSubnetOrderList,
                            VpcOrderDTO vpcOrderDTO, VpcOrder vpcOrder,
                            VpcCreateDTO vpcCreateDTO) {
        for (VpcSubnetDTO vpcSubnetDTO : vpcOrderDTO.getSubnetDTOList()) {
            String cidr = vpcSubnetDTO.getStartIp() + "/" + vpcSubnetDTO.getNetmask();
            vpcSubnetDTO.setCidr(cidr);
            // 组装子网vpc_order参数
            VpcSubnetOrder vpcSubnetOrder = new VpcSubnetOrder();
            String subnetId = UuidUtil.getGid(NetworkPrefixEnum.VPC_SUB.getType());
            vpcSubnetOrder.setId(subnetId);
            vpcSubnetOrder.setVpcId(vpcOrder.getId());
            vpcSubnetOrder.setCreatedTime(new Date());
            vpcSubnetOrder.setSubnetName(vpcSubnetDTO.getSubnetName());
            vpcSubnetOrder.setAzCode(vpcCreateDTO.getAzCode());
            vpcSubnetOrder.setCidr(cidr);
            vpcSubnetOrder.setStartIp(vpcSubnetDTO.getStartIp());
            vpcSubnetOrder.setNetmask(vpcSubnetDTO.getNetmask());
            vpcSubnetOrder.setDeleted(1);
            vpcSubnetOrder.setInstanceId(vpcSubnetDTO.getInstanceId());
            vpcSubnetOrder.setLevel2InstanceId(vpcSubnetDTO.getLevel2InstanceId());
            vpcSubnetOrder.setUuid(vpcSubnetDTO.getUuid());
            vpcSubnetOrder.setStatus(NetworkStatusEnum.SUCCESS.getType());
            // 这里 倒果为因 了，因为现在的特殊处理是只要是vmWare的pass池，就支持创建ipv6的子网
            // 如果前端放开，允许创建子网的时候选择是否支持ipv6，需要修改这里
            // 前端放开？相当远创建多个vpc子网的时候，允许从中选择一个作为ipv6？这种需求的目的是为啥呢？
            vpcSubnetOrder.setIpv6Enable(vpcOrder.getIpv6Enable());
            if (vpcSubnetDTO.getAllocationPools() != null) {
                vpcSubnetOrder.setAllocationPools(JSONObject.toJSONString(vpcSubnetDTO.getAllocationPools()));
            }

            vpcSubnetOrderList.add(vpcSubnetOrder);
            // 组装子网开通入参
            CreateVpcAndSubnetRcDTO.CreateSubnetRcDto subnetRcDto = new CreateVpcAndSubnetRcDTO.CreateSubnetRcDto();
            subnetRcDto.setCidr(vpcSubnetDTO.getCidr());
            subnetRcDto.setRegionCode(vpcCreateDTO.getRegionCode());
            subnetRcDto.setName(vpcSubnetDTO.getSubnetName());
            subnetRcDto.setAzCode(vpcCreateDTO.getAzCode());
            subnetRcDto.setTenantId(vpcCreateDTO.getBottomTenantId());
            subnetRcDto.setVpcId(vpcOrder.getId());
            subnetRcDto.setOrderId(subnetId);
            subnetRcDto.setIpv6Enable(vpcSubnetOrder.getIpv6Enable());
            subnetRcDto.setAllocationPools(vpcSubnetDTO.getAllocationPools());
            createSubnetDtoList.add(subnetRcDto);
        }

    }


    private CreateVpcAndSubnetRcDTO getCreateVpc(VpcOrder vpcOrder, VpcOrderDTO vpcOrderDTO, VpcCreateDTO vpcCreateDTO) {
        CreateVpcAndSubnetRcDTO createVpcDto = new CreateVpcAndSubnetRcDTO();
        createVpcDto.setTenantId(vpcCreateDTO.getBottomTenantId());
        createVpcDto.setRegionCode(vpcCreateDTO.getRegionCode());
        createVpcDto.setName(vpcOrderDTO.getVpcName());
        createVpcDto.setIpv4Cidr(vpcOrderDTO.getCidr());
        createVpcDto.setIpv6Cidr(vpcOrderDTO.getIpv6Cidr());
        createVpcDto.setOrderId(vpcOrder.getId());
        createVpcDto.setGId(vpcOrder.getId());
        return createVpcDto;
    }


    private VpcOrder getVpcOrder(VpcOrderDTO vpcOrderDTO, VpcCreateDTO vpcCreateDTO) {
        VpcOrder vpcOrder = new VpcOrder();
        String vpcId = UuidUtil.getGid(NetworkPrefixEnum.VPC.getType());
        vpcOrder.setId(vpcId);
        vpcOrder.setCreatedBy(vpcCreateDTO.getUserId());
        vpcOrder.setCreatedTime(new Date());
        vpcOrder.setVpcName(vpcOrderDTO.getVpcName());
        vpcOrder.setOrderId(vpcCreateDTO.getOrderId());
        vpcOrder.setOrderCode(vpcCreateDTO.getOrderCode());
        vpcOrder.setSubnetNum(vpcOrderDTO.getSubnetDTOList().size());
        vpcOrder.setTenantId(vpcCreateDTO.getTenantId());
        vpcOrder.setAccount(vpcCreateDTO.getBillId());
        vpcOrder.setRegionCode(vpcCreateDTO.getRegionCode());
        vpcOrder.setIpv4Cidr(vpcOrderDTO.getCidr());
        vpcOrder.setIpv6Cidr(vpcOrderDTO.getIpv6Cidr());
        vpcOrder.setDeleted(1);
        vpcOrder.setGid(vpcId);
        vpcOrder.setFunctionalModule(vpcCreateDTO.getFunctionalModule());
        vpcOrder.setAzCode(vpcCreateDTO.getAzCode());
        vpcOrder.setPlane(vpcOrderDTO.getPlane());
        vpcOrder.setStatus(NetworkStatusEnum.EXECUTING.getType());
        vpcOrder.setDetail(vpcOrderDTO.getDetail());
        vpcOrder.setApplyUserId(vpcCreateDTO.getApplyUserId());
        vpcOrder.setApplyUserName(vpcCreateDTO.getApplyUserName());
        vpcOrder.setBusinessSysId(vpcCreateDTO.getBusinessSysId());
        vpcOrder.setBusinessSysName(vpcCreateDTO.getBusinessSysName());
        vpcOrder.setTenantName(vpcCreateDTO.getTenantName());
        vpcOrder.setSourceType(vpcCreateDTO.getSourceType());
        vpcOrder.setModuleId(vpcCreateDTO.getModuleId());
        vpcOrder.setModuleName(vpcCreateDTO.getModuleName());
        vpcOrder.setDomainCode(vpcCreateDTO.getDomainCode());
        vpcOrder.setDomainName(vpcCreateDTO.getDomainName());
        vpcOrder.setCatalogueDomainCode(vpcCreateDTO.getCatalogueDomainCode());
        vpcOrder.setCatalogueDomainName(vpcCreateDTO.getCatalogueDomainName());
        RegionDTO regionDTO = regionManager.getByCode(vpcCreateDTO.getRegionCode());
        // 特殊处理，vmware技术栈的pass类型资源池，默认支持开通ipv6的eip，其他都不支持
        // 这个属性底层是子网上的，主要用于ipv6的eip创建，如果vpc的子网有一个支持ipv6，vpc本身也意味着支持
        if ("VMWARE".equals(regionDTO.getType()) && "paas".equals(regionDTO.getRealmType())) {
            vpcOrder.setIpv6Enable(true);
        } else {
            vpcOrder.setIpv6Enable(false);
        }
        return vpcOrder;
    }


    private String handleVpcVerifyRequest(Map<String, String> header, NetOpenParamVerify paramVerify, VpcVerifyEnum vpcVerifyEnum) {
        Map<String, String> requestMap = new HashMap<>();
        String requestUrl = "";
        switch (vpcVerifyEnum) {
            case NAME_VERIFY:
                requestMap.put("billId", paramVerify.getBillId());
                requestMap.put("name", paramVerify.getName());
                requestMap.put("regionCode", paramVerify.getRegionCode());
                requestUrl = resourceHandle.resourceCenterUrl + resourceHandle.getResourceVpcNameVerify();
                break;
            case NET_VERIFY:
                requestMap.put("billId", paramVerify.getBillId());
                requestMap.put("vpcCidr", paramVerify.getVpcCidr());
                requestMap.put("regionCode", paramVerify.getRegionCode());
                requestUrl = resourceHandle.resourceCenterUrl + resourceHandle.getResourceVpcCidrVerify();
                break;
            case SUB_NET_VERIFY:
                requestMap.put("vpcCidr", paramVerify.getVpcCidr());
                requestMap.put("subnetCidr", paramVerify.getSubnetCidr());
                requestUrl = resourceHandle.resourceCenterUrl + resourceHandle.getResourceVpcSubnetCidrVerify();
                break;
            default:
        }

        Map<String, Object> response = HttpClientUtil.get(requestUrl, requestMap, header);
        Integer code = (Integer) response.get("code");
        if (code != 200) {
            CommonResult result = JSON.parseObject(JSON.toJSONString(response.get("json")), CommonResult.class);
            return result.getMessage();
        }

        return "";
    }

    private Map<String, String> getHeaders() {
        Map<String, String> header = new HashMap<>();
        header.put("RemoteUser", "BusinessCenter");
        header.put("Content-Type", "application/json");
        return header;
    }

}

