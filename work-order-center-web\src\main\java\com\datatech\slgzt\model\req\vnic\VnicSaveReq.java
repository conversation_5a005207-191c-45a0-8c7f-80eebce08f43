package com.datatech.slgzt.model.req.vnic;

import lombok.Data;

/**
 * 虚拟网卡保存请求
 */
@Data
public class VnicSaveReq {

    /**
     * 虚拟网卡主键id，变更是必传
     */
    private String id;
    
    /**
     * 虚拟网卡ID
     */
    private String vnicId;
    
    /**
     * 虚拟网卡名称
     */
    private String vnicName;
    
    /**
     * 业务系统名称
     */
    private String businessSystemName;
    
    /**
     * 业务系统Id
     */
    private String businessSystemId;
    
    /**
     * 云类型名称
     */
    private String catalogueDomainName;
    
    /**
     * 云类型编码
     */
    private String catalogueDomainCode;
    
    /**
     * 云平台名称
     */
    private String domainName;
    
    /**
     * 云平台编码
     */
    private String domainCode;
    
    /**
     * 资源池名称
     */
    private String regionName;
    
    /**
     * 资源池ID
     */
    private String regionId;

    /**
     * 资源池code
     */
    private String regionCode;
    
    /**
     * 可用区名称
     */
    private String azName;
    
    /**
     * 可用区ID
     */
    private String azId;

    /**
     * 可用区code
     */
    private String azCode;
    
    /**
     * VPC名称
     */
    private String vpcName;
    
    /**
     * VPC ID
     */
    private String vpcId;
    
    /**
     * 子网名称
     */
    private String subnetName;
    
    /**
     * 子网ID
     */
    private String subnetId;
    
    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * ipv6子网ID
     */
    private String ipv6SubnetId;

    /**
     * ipv6子网名称
     */
    private String ipv6SubnetName;
    
    /**
     * 云主机名称
     */
    private String vmName;
    
    /**
     * 云主机ID
     */
    private String vmId;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 网卡类型
     */
    private String type;
    
    /**
     * 管理网段
     */
    private String netName;

    /**
     * 来源
     */
    private String sourceType;
} 