package com.datatech.slgzt.impl.service;

import com.datatech.slgzt.impl.manager.StandardWorkOrderProductManagerImpl;
import com.datatech.slgzt.impl.manager.ChangeWorkOrderProductManagerImpl;
import com.datatech.slgzt.impl.manager.RecoveryWorkOrderProductManagerImpl;
import com.datatech.slgzt.impl.service.serial.SerialTaskLockServiceImpl;
import com.datatech.slgzt.impl.service.serial.UnifiedSerialProcessingTask;
import com.datatech.slgzt.model.dto.StandardWorkOrderProductDTO;
import com.datatech.slgzt.model.dto.ChangeWorkOrderProductDTO;
import com.datatech.slgzt.model.dto.RecoveryWorkOrderProductDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * RegionCode数据一致性测试类
 * 验证从数据库到业务逻辑的完整数据链路中regionCode的正确使用
 * 
 * <AUTHOR>
 * @date 2025年08月05日
 */
@ExtendWith(MockitoExtension.class)
public class RegionCodeDataConsistencyTest {

    private static final Logger log = LoggerFactory.getLogger(RegionCodeDataConsistencyTest.class);

    @Mock
    private StandardWorkOrderProductManagerImpl standardProductManager;

    @Mock
    private ChangeWorkOrderProductManagerImpl changeWorkOrderProductManager;

    @Mock
    private RecoveryWorkOrderProductManagerImpl recoveryWorkOrderProductManager;

    @Mock
    private SerialTaskLockServiceImpl serialTaskLockService;

    @InjectMocks
    private UnifiedSerialProcessingTask unifiedSerialProcessingTask;

    @Test
    void testRegionCodeDataConsistency_标准工单() {
        log.info("开始测试标准工单regionCode数据一致性");
        
        // Given: 创建测试数据，确保regionCode值的一致性
        Map<String, StandardWorkOrderProductDTO> standardTasks = new HashMap<>();
        StandardWorkOrderProductDTO standardTask = createStandardTask("region-beijing");
        standardTasks.put("region-beijing", standardTask);

        when(standardProductManager.listNextOpenByRegionCode()).thenReturn(standardTasks);
        when(changeWorkOrderProductManager.listNextSerialChangeByRegionCode()).thenReturn(new HashMap<>());
        when(recoveryWorkOrderProductManager.listNextSerialRecoveryByRegionCode()).thenReturn(new HashMap<>());
        when(serialTaskLockService.tryLock("region-beijing")).thenReturn(true);

        // When: 执行串行处理任务
        unifiedSerialProcessingTask.unifiedSerialProcessing();

        // Then: 验证regionCode值的正确传递
        verify(serialTaskLockService).tryLock("region-beijing");
        verify(serialTaskLockService).unlock("region-beijing");
        
        // 验证返回的DTO中regionCode值正确
        assertEquals("region-beijing", standardTask.getRegionCode());
        
        log.info("标准工单regionCode数据一致性测试通过");
    }

    @Test
    void testRegionCodeDataConsistency_变更工单() {
        log.info("开始测试变更工单regionCode数据一致性");
        
        // Given: 创建变更工单测试数据
        Map<String, ChangeWorkOrderProductDTO> changeTasks = new HashMap<>();
        ChangeWorkOrderProductDTO changeTask = createChangeTask("region-shanghai");
        changeTasks.put("region-shanghai", changeTask);

        when(standardProductManager.listNextOpenByRegionCode()).thenReturn(new HashMap<>());
        when(changeWorkOrderProductManager.listNextSerialChangeByRegionCode()).thenReturn(changeTasks);
        when(recoveryWorkOrderProductManager.listNextSerialRecoveryByRegionCode()).thenReturn(new HashMap<>());
        when(serialTaskLockService.tryLock("region-shanghai")).thenReturn(true);

        // When: 执行串行处理任务
        unifiedSerialProcessingTask.unifiedSerialProcessing();

        // Then: 验证regionCode值的正确传递
        verify(serialTaskLockService).tryLock("region-shanghai");
        verify(serialTaskLockService).unlock("region-shanghai");
        
        // 验证返回的DTO中regionCode值正确
        assertEquals("region-shanghai", changeTask.getRegionCode());
        
        log.info("变更工单regionCode数据一致性测试通过");
    }

    @Test
    void testRegionCodeDataConsistency_回收工单() {
        log.info("开始测试回收工单regionCode数据一致性");
        
        // Given: 创建回收工单测试数据
        Map<String, RecoveryWorkOrderProductDTO> recoveryTasks = new HashMap<>();
        RecoveryWorkOrderProductDTO recoveryTask = createRecoveryTask("region-guangzhou");
        recoveryTasks.put("region-guangzhou", recoveryTask);

        when(standardProductManager.listNextOpenByRegionCode()).thenReturn(new HashMap<>());
        when(changeWorkOrderProductManager.listNextSerialChangeByRegionCode()).thenReturn(new HashMap<>());
        when(recoveryWorkOrderProductManager.listNextSerialRecoveryByRegionCode()).thenReturn(recoveryTasks);
        when(serialTaskLockService.tryLock("region-guangzhou")).thenReturn(true);

        // When: 执行串行处理任务
        unifiedSerialProcessingTask.unifiedSerialProcessing();

        // Then: 验证regionCode值的正确传递
        verify(serialTaskLockService).tryLock("region-guangzhou");
        verify(serialTaskLockService).unlock("region-guangzhou");
        
        // 验证返回的DTO中regionCode值正确
        assertEquals("region-guangzhou", recoveryTask.getRegionCode());
        
        log.info("回收工单regionCode数据一致性测试通过");
    }

//    @Test
//    void testRegionCodeDataConsistency_分布式锁Key构建() {
//        log.info("开始测试分布式锁Key构建的regionCode一致性");
//
//        // Given: 创建SerialTaskLockService实例进行实际测试
//        SerialTaskLockServiceImpl lockService = new SerialTaskLockServiceImpl();
//
//        // When & Then: 验证不同regionCode值的锁Key构建
//        String[] regionCodes = {"region-beijing", "region-shanghai", "region-guangzhou", null};
//
//        for (String regionCode : regionCodes) {
//            // 这里我们无法直接测试私有方法buildLockKey，但可以通过其他方法间接验证
//            // 实际项目中可以将buildLockKey方法改为protected以便测试
//            boolean result = lockService.isLocked(regionCode);
//            // 验证方法调用不会抛出异常，说明regionCode处理正确
//            assertNotNull(result);
//        }
//
//        log.info("分布式锁Key构建regionCode一致性测试通过");
//    }

    @Test
    void testRegionCodeDataConsistency_多资源池混合场景() {
        log.info("开始测试多资源池混合场景的regionCode数据一致性");
        
        // Given: 创建多种类型工单的混合场景
        Map<String, StandardWorkOrderProductDTO> standardTasks = new HashMap<>();
        standardTasks.put("region-beijing", createStandardTask("region-beijing"));
        
        Map<String, ChangeWorkOrderProductDTO> changeTasks = new HashMap<>();
        changeTasks.put("region-shanghai", createChangeTask("region-shanghai"));
        
        Map<String, RecoveryWorkOrderProductDTO> recoveryTasks = new HashMap<>();
        recoveryTasks.put("region-guangzhou", createRecoveryTask("region-guangzhou"));

        when(standardProductManager.listNextOpenByRegionCode()).thenReturn(standardTasks);
        when(changeWorkOrderProductManager.listNextSerialChangeByRegionCode()).thenReturn(changeTasks);
        when(recoveryWorkOrderProductManager.listNextSerialRecoveryByRegionCode()).thenReturn(recoveryTasks);
        when(serialTaskLockService.tryLock(anyString())).thenReturn(true);

        // When: 执行串行处理任务
        unifiedSerialProcessingTask.unifiedSerialProcessing();

        // Then: 验证所有regionCode值的正确传递
        verify(serialTaskLockService).tryLock("region-beijing");
        verify(serialTaskLockService).tryLock("region-shanghai");
        verify(serialTaskLockService).tryLock("region-guangzhou");
        
        verify(serialTaskLockService).unlock("region-beijing");
        verify(serialTaskLockService).unlock("region-shanghai");
        verify(serialTaskLockService).unlock("region-guangzhou");
        
        log.info("多资源池混合场景regionCode数据一致性测试通过");
    }

    /**
     * 创建标准工单任务
     */
    private StandardWorkOrderProductDTO createStandardTask(String regionCode) {
        StandardWorkOrderProductDTO task = new StandardWorkOrderProductDTO();
        task.setId(1L);
        task.setRegionCode(regionCode);
        task.setSerialOpenStatus(2); // 待开通状态
        task.setProductType("ECS");
        return task;
    }

    /**
     * 创建变更工单任务
     */
    private ChangeWorkOrderProductDTO createChangeTask(String regionCode) {
        ChangeWorkOrderProductDTO task = new ChangeWorkOrderProductDTO();
        task.setId(2L);
        task.setRegionCode(regionCode);
        task.setProductType("ECS");
        return task;
    }

    /**
     * 创建回收工单任务
     */
    private RecoveryWorkOrderProductDTO createRecoveryTask(String regionCode) {
        RecoveryWorkOrderProductDTO task = new RecoveryWorkOrderProductDTO();
        task.setId(3L);
        task.setRegionCode(regionCode);
        task.setProductType("ECS");
        return task;
    }
}
