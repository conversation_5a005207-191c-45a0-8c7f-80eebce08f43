package com.datatech.slgzt.dao.model.network;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("WOC_NETWORK_SUBNET_ORDER")
public class NetworkSubnetOrder implements Serializable {
    private static final long serialVersionUID = 599388800911149645L;

    @TableId(value = "ID", type = IdType.INPUT)
    private String id;

    @TableField("CREATED_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdTime;

    @TableField("UPDATED_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedTime;

    @TableField("SUBNET_NAME")
    private String subnetName;

    @TableField("NETWORK_ID")
    private String networkId;

    @TableField("INSTANCE_ID")
    private String instanceId;

    @TableField("IP_VERSION")
    private String ipVersion;

    @TableField("RESOURCE_ID")
    private String resourceId;

    @TableField("CIDR")
    private String cidr;

    @TableField("GATEWAY")
    private String gateway;

    @TableField("DELETED")
    private Integer deleted;

    @TableField("NETMASK")
    private String netmask;

    @TableField("MESSAGE")
    private String message;

    @TableField("DESCRIPTION")
    private String description;

    @TableField("STATUS")
    private String status;

    @TableField("RECOVERY_STATUS")
    private Integer recoveryStatus;

    @TableField("LEVEL2_INSTANCE_ID")
    private String level2InstanceId;
    @TableField("UUID")
    private String uuid;

    @TableField("ALLOCATION_POOLS")
    private String allocationPools;
}
