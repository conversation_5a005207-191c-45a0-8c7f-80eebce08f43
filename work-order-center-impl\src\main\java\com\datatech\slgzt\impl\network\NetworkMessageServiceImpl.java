package com.datatech.slgzt.impl.network;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.datatech.slgzt.annotation.Lock;
import com.datatech.slgzt.consumer.BatchRestartConsumer;
import com.datatech.slgzt.convert.NetworkSubnetOrderManagerConvert;
import com.datatech.slgzt.dao.RegionDAO;
import com.datatech.slgzt.dao.mapper.network.NetworkOrderMapper;
import com.datatech.slgzt.dao.mapper.network.NetworkSubnetOrderMapper;
import com.datatech.slgzt.dao.model.RegionDO;
import com.datatech.slgzt.dao.model.network.NetworkOrder;
import com.datatech.slgzt.dao.model.network.NetworkSubnetOrder;
import com.datatech.slgzt.enums.*;
import com.datatech.slgzt.enums.domain.CatalogueDomain;
import com.datatech.slgzt.enums.ip.IpLevelEnum;
import com.datatech.slgzt.enums.network.NetworkPrefixEnum;
import com.datatech.slgzt.enums.network.NetworkStatusEnum;
import com.datatech.slgzt.exception.BusinessException;
import com.datatech.slgzt.handle.ResourceHandle;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.CmdbIP3StatusManager;
import com.datatech.slgzt.manager.RecoveryWorkOrderProductManager;
import com.datatech.slgzt.manager.TenantManager;
import com.datatech.slgzt.model.AllocationPool;
import com.datatech.slgzt.model.BatchRestartModel;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.KafkaMessage;
import com.datatech.slgzt.model.business.CmpAppDTO;
import com.datatech.slgzt.model.callback.YunshuCallback;
import com.datatech.slgzt.model.dto.OrderCommonDTO;
import com.datatech.slgzt.model.dto.RecoveryWorkOrderProductDTO;
import com.datatech.slgzt.model.dto.TenantDTO;
import com.datatech.slgzt.model.dto.business.BusinessService;
import com.datatech.slgzt.model.dto.network.*;
import com.datatech.slgzt.model.dto.vpc.CreateVpcAndSubnetRcDTO;
import com.datatech.slgzt.model.query.RegionQuery;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.model.vo.callback.TaskVO;
import com.datatech.slgzt.model.vo.network.NetworkOrderResult;
import com.datatech.slgzt.model.vo.network.NetworkTableVo;
import com.datatech.slgzt.model.vo.network.ip.IpUpdateStatusVO;
import com.datatech.slgzt.model.vo.vpc.NetworkSubnetOrderResult;
import com.datatech.slgzt.service.OrderDataProvideService;
import com.datatech.slgzt.service.PlatformService;
import com.datatech.slgzt.service.cmdb.CmdbReportService;
import com.datatech.slgzt.service.network.NetworkCollectService;
import com.datatech.slgzt.service.network.NetworkMessageService;
import com.datatech.slgzt.service.network.VpcMessageService;
import com.datatech.slgzt.service.yunshu.YunshuReportService;
import com.datatech.slgzt.utils.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: suxin
 * @Date: 2024/11/18
 * @Description: vpc校验服务
 */
@Slf4j
@Service
public class NetworkMessageServiceImpl implements NetworkMessageService {

    private static final String redisKeyPrefix = "dag:network:";

    @Resource
    private ResourceHandle resourceHandle;

    @Resource
    private PlatformService platformService;

    @Resource
    private NetworkOrderMapper networkOrderMapper;
    @Resource
    private NetworkSubnetOrderMapper networkSubnetOrderMapper;

    @Resource
    private NetworkCollectService networkService;
    @Resource
    private OrderDataProvideService orderDataProvideService;

    @Resource
    private YunshuReportService yunshuReportService;
    @Resource
    private RegionDAO regionDAO;

    @Resource
    private VpcMessageService vpcMessageService;
    @Resource
    private CmdbReportService cmdbReportService;
//    @Resource
//    private SMSMessageUtil smsMessageUtil;

    @Resource
    private BusinessService businessService;

    @Resource
    private RecoveryWorkOrderProductManager recoveryWorkOrderProductManager;

    @Resource
    private TenantManager tenantManager;

    @Resource
    private NetworkSubnetOrderManagerConvert networkSubnetOrderManagerConvert;
    @Resource
    private RedissonClient redissonClient;

    @Resource
    private NetworkSubnetOrderMapper subnetMapper;

    @Resource
    private KafkaTemplate<String, Object> kafkaTemplate;

    @Resource
    private CmdbIP3StatusManager cmdbIP3StatusManager;


    @Override
    public CommonResult<List<NetworkOrderResult>> selectNetworkList(NetworkTableVo networkTableVo) {
        OrderCommonDTO oacOrder = orderDataProvideService.getOrderCommon(networkTableVo.getOrderId());
        networkTableVo.setTenantId(oacOrder.getTenantId());
        RegionQuery query = new RegionQuery();
        query.setCode(networkTableVo.getRegionCode());

        List<RegionDO> regionDOListlist = regionDAO.list(query);
        Precondition.checkArgument(regionDOListlist, "未查找到资源池");
        RegionDO regionDO = regionDOListlist.get(0);
        if (CatalogueDomain.INNOVATION.getCode().equals(regionDO.getDomainCode())|| CatalogueDomain.PLATFORM_CHILD.getCode().equals(regionDO.getDomainCode())) {
            networkTableVo.setAzCode(null);
        } else {
            Precondition.checkArgument(networkTableVo.getAzCode(), "可用区编号不能为空");
        }

        List<NetworkOrderResult> list = networkOrderMapper.selectNetworkWithSubnetList(networkTableVo);
        /*for (NetworkOrderResult networkOrderResult : list) {
            List<NetworkSubnetOrderDTO> subList = networkOrderResult.getSubnetOrderList();
            subList.removeIf(subnet -> !NetworkStatusEnum.SUCCESS.getType().equals(subnet.getStatus()));
        }
        list.removeIf(result -> CollectionUtils.isEmpty(result.getSubnetOrderList()));*/
        return CommonResult.success(list);
    }

    @Override
    @Transactional
    public CommonResult<String> networkCreate(NetworkCreateDTO networkCreateDTO) {
        Long userId = UserHelper.INSTANCE.getCurrentUserId();
        OrderCommonDTO oacOrder = orderDataProvideService.getOrderCommon(networkCreateDTO.getOrderId());
        /*if (networkOrderMapper.selectNatWorkName(networkOrderDTO.getName(), oacOrder.getTenantId()) > 0) {
            throw new BusinessException("网络名称已存在");
        }*/
        // t设置租户id
        Long bottomTenantId = platformService.getOrCreateTenantId(oacOrder.getBillId(), networkCreateDTO.getRegionCode());
        networkCreateDTO.setBottomTenantId(bottomTenantId);
        networkCreateDTO.setTenantId(oacOrder.getTenantId());
        TenantDTO tenantDTO = tenantManager.getById(oacOrder.getTenantId());
        networkCreateDTO.setBusinessSysId(oacOrder.getBusinessSysId());
        CmpAppDTO cmpAppDTO = businessService.getById(oacOrder.getBusinessSysId());
        networkCreateDTO.setBusinessSysName(cmpAppDTO.getSystemName());
        networkCreateDTO.setTenantName(tenantDTO.getName());
        networkCreateDTO.setApplyUserId(oacOrder.getUserId());
        networkCreateDTO.setApplyUserName(oacOrder.getUserName());
        networkCreateDTO.setSourceType(networkCreateDTO.getSourceType());
        networkCreateDTO.setBillId(oacOrder.getBillId());
        networkCreateDTO.setModuleId(oacOrder.getModuleId());
        networkCreateDTO.setModuleName(oacOrder.getModuleName());
        networkCreateDTO.setUserId(userId);
        networkCreateDTO.setBillId(oacOrder.getBillId());
        networkCreateDTO.setOrderCode(oacOrder.getOrderCode());
        return createNetwork(networkCreateDTO);
    }

    @Override
    @Transactional
    public CommonResult<String> networkCreateBatch(NetworkCreateDTO networkCreateDTO) {
        UserCenterUserDTO userCenterDTO = UserHelper.INSTANCE.getCurrentUser();
        TenantDTO tenantDTO = tenantManager.getById(networkCreateDTO.getTenantId());
        // t设置租户id
        Long bottomTenantId = platformService.getResourceTenantId(tenantDTO.getBillId(), networkCreateDTO.getRegionCode());
        Precondition.checkArgument(bottomTenantId, "当前业务系统和资源池下不存在创建中或已完成工单，暂时无法开通网络");
        networkCreateDTO.setBottomTenantId(bottomTenantId);
        CmpAppDTO cmpAppDTO = businessService.getById(networkCreateDTO.getBusinessSysId());
        networkCreateDTO.setBusinessSysName(cmpAppDTO.getSystemName());
        networkCreateDTO.setTenantName(tenantDTO.getName());
        networkCreateDTO.setApplyUserId(userCenterDTO.getId());
        networkCreateDTO.setApplyUserName(userCenterDTO.getUserName());
        networkCreateDTO.setSourceType(SourceTypeEnum.STANDARD.getPrefix());
        networkCreateDTO.setBillId(tenantDTO.getBillId());
        networkCreateDTO.setUserId(userCenterDTO.getId());
        networkCreateDTO.setOrderId("0");
        return createNetwork(networkCreateDTO);
    }

    private CommonResult<String> createNetwork(NetworkCreateDTO networkCreateDTO) {
        // 组装vpc_order参数
        for (NetworkOrderDTO networkOrderDTO : networkCreateDTO.getNetworks()) {
            NetworkOrder networkOrder = getNetworkOrder(networkOrderDTO, networkCreateDTO);
            List<NetworkSubnetOrder> list = getNetworkSubnetOrder(networkOrder, networkOrderDTO);
            if ("normal".equals(networkOrderDTO.getType())) {
                CreateNetworkRcDTO createNetworkRcDto = getCreateNetworkRcDto(networkOrder, networkOrderDTO, networkCreateDTO);
                log.info("network创建请求参数 param:{}", JSONObject.toJSONString(createNetworkRcDto));
                String requestUrl = resourceHandle.resourceCenterUrl + resourceHandle.getNetworkCreate();
                Map<String, Object> response = HttpClientUtil.post(requestUrl, JSONObject.toJSONString(createNetworkRcDto), getHeaders());
                log.info("network创建返回参数 response:{}", response);
                Integer code = (Integer) response.get("code");
                if (code != 200) {
                    networkOrder.setStatus(NetworkStatusEnum.ERROR.getType());
                    networkOrder.setOverallStatus(NetworkStatusEnum.ERROR.getType());
                    networkOrder.setMessage(JSONObject.toJSONString(response));
                    log.error("network创建失败 error {}", JSONObject.toJSONString(response));
                } else {
                    String json = (String) response.get("json");
                    JSONObject jsonObject = JSONObject.parseObject(json);
                    String success = jsonObject.getString("success");
                    if (!"1".equals(success)) {
                        String message = jsonObject.get("message").toString();
                        networkOrder.setStatus(NetworkStatusEnum.ERROR.getType());
                        networkOrder.setOverallStatus(NetworkStatusEnum.ERROR.getType());
                        networkOrder.setMessage(message);
                        log.error("network创建失败 error {}", message);
                    } else if (jsonObject.containsKey("entity")) {
                        JSONObject entity = jsonObject.getJSONObject("entity");
                        String resourceId = entity.getString("resourceId");
                        networkOrder.setResourceId(resourceId);
                    }
                }
                networkOrderMapper.insert(networkOrder);
                networkSubnetOrderMapper.batchInsertNetworkSubnet(list);
            } else if ("classic".equals(networkOrderDTO.getType())) {
                CreateVpcAndSubnetRcDTO createVpcDto = getCreateVpc(networkOrder, networkOrderDTO, networkCreateDTO);
                List<CreateVpcAndSubnetRcDTO.CreateSubnetRcDto> createSubnetDtoList = new ArrayList<>();
                createVpcDto.setSubnetDTOList(createSubnetDtoList);
                createList(createSubnetDtoList, networkOrderDTO, networkOrder, networkCreateDTO, list.get(0));
                log.info("vmware-经典网络创建请求参数 createVpcDto:{}", JSONObject.toJSONString(createVpcDto));
                String requestUrl = resourceHandle.resourceCenterUrl + resourceHandle.getVpcCreateVpcAndSubVerify();
                Map<String, Object> response = HttpClientUtil.post(requestUrl, JSONObject.toJSONString(createVpcDto), HeadersUtil.getHeaders());
                log.info("vmware-经典网络创建返回参数 response:{}", response);
                Integer code = (Integer) response.get("code");
                String json = (String) response.get("json");

                if (code != 200) {
                    String message = "vmware-经典网络创建失败";
                    if (StringUtils.isNotEmpty(json)) {
                        JSONObject jsonObject = JSONObject.parseObject(json);
                        message = jsonObject.get("message").toString();
                    }
                    networkOrder.setStatus(NetworkStatusEnum.ERROR.getType());
                    networkOrder.setMessage(JSONObject.toJSONString(response));
                    log.error("vmware-经典网络创建失败 error {}", message);
                    throw new BusinessException(message);
                } else {
                    JSONObject jsonObject = JSONObject.parseObject(json);
                    String success = jsonObject.getString("success");
                    if (!"1".equals(success)) {
                        String message = jsonObject.get("message").toString();
                        networkOrder.setStatus(NetworkStatusEnum.ERROR.getType());
                        networkOrder.setOverallStatus(NetworkStatusEnum.ERROR.getType());
                        networkOrder.setMessage(message);
                        log.error("vmware-经典网络创建失败 error {}", message);
                    } else if (jsonObject.containsKey("entity")) {
                        JSONObject entity = jsonObject.getJSONObject("entity");
                        String resourceId = entity.getString("resourceId");
                        networkOrder.setResourceId(resourceId);
                    }
                }
                networkOrderMapper.insert(networkOrder);
                list.forEach(subnet -> subnet.setStatus(NetworkStatusEnum.EXECUTING.getType()));
                networkSubnetOrderMapper.batchInsertNetworkSubnet(list);
            }

            return CommonResult.success("网络创建下发成功");
        }
        return null;
    }

    private CreateVpcAndSubnetRcDTO getCreateVpc(NetworkOrder networkOrder, NetworkOrderDTO networkOrderDTO, NetworkCreateDTO networkCreateDTO) {
        CreateVpcAndSubnetRcDTO createVpcDto = new CreateVpcAndSubnetRcDTO();
        createVpcDto.setTenantId(networkCreateDTO.getBottomTenantId());
        createVpcDto.setRegionCode(networkOrder.getRegionCode());
        createVpcDto.setName(networkOrderDTO.getName());
        List<SubnetDTO> subnets = networkOrderDTO.getSubnets();
        SubnetDTO subnetDTO = subnets.get(0);
        if ("IPv4".equals(subnetDTO.getIpVersion())) {
            createVpcDto.setIpv4Cidr(subnetDTO.getCidr());
        } else if ("IPv6".equals(subnetDTO.getIpVersion())) {
            createVpcDto.setIpv6Cidr(subnetDTO.getCidr());
        }
        createVpcDto.setOrderId(networkOrder.getId());
        createVpcDto.setGId(networkOrder.getId());
        return createVpcDto;
    }

    private void createList(List<CreateVpcAndSubnetRcDTO.CreateSubnetRcDto> createSubnetDtoList,
                            NetworkOrderDTO networkOrderDTO,
                            NetworkOrder networkOrder,
                            NetworkCreateDTO networkCreateDTO,
                            NetworkSubnetOrder networkSubnetOrder) {
        for (SubnetDTO subnetDTO : networkOrderDTO.getSubnets()) {
            subnetDTO.setCidr(subnetDTO.getCidr());
            // 组装子网开通入参
            CreateVpcAndSubnetRcDTO.CreateSubnetRcDto subnetRcDto = new CreateVpcAndSubnetRcDTO.CreateSubnetRcDto();
            subnetRcDto.setCidr(subnetDTO.getCidr());
            subnetRcDto.setRegionCode(networkOrderDTO.getRegionCode());
            subnetRcDto.setName(networkSubnetOrder.getSubnetName());
            subnetRcDto.setAzCode(networkOrder.getAzCode());
            subnetRcDto.setTenantId(networkCreateDTO.getBottomTenantId());
            subnetRcDto.setVpcId(networkOrder.getId());
            subnetRcDto.setOrderId(networkSubnetOrder.getId());
            subnetRcDto.setVlanId(Integer.valueOf(networkOrder.getVlanId()));
            createSubnetDtoList.add(subnetRcDto);
        }

    }

    @Override
    public CommonResult<String> createSubnetWork(NetworkOrderDTO networkOrderDTO) {
        NetworkOrder networkOrder = networkOrderMapper.selectById(networkOrderDTO.getNetworkId());
        List<NetworkSubnetOrder> list = getNetworkSubnetOrder(networkOrder, networkOrderDTO);
        networkSubnetOrderMapper.batchInsertNetworkSubnet(list);

        for (NetworkSubnetOrder subnetOrder : list) {
            CreateNetworkSubnetRcDTO createNetworkSubnetRcDTO = networkSubnetOrderManagerConvert.do2RcDTO(subnetOrder);
            createNetworkSubnetRcDTO.setSystemSource(networkOrder.getSystemSource());
            createNetworkSubnetRcDTO.setBillId(networkOrder.getAccount());
            createNetworkSubnetRcDTO.setRegionCode(networkOrder.getRegionCode());
            if (subnetOrder.getAllocationPools() != null) {
                createNetworkSubnetRcDTO.setAllocationPools(
                        JSONObject.parseArray(subnetOrder.getAllocationPools(), AllocationPool.class)
                );
            }
            createSubnet(createNetworkSubnetRcDTO);
        }
        if (StringUtils.isNotBlank(networkOrderDTO.getDetail())) {
            networkOrderMapper.updateDetailById(networkOrderDTO);
        }

        return CommonResult.success("子网下发成功");
    }

    @Override
    @Lock(prefixKey = "key-consumeNetworkMessage", waitTime = 15000, key = {"#taskVO.orderId"})
    public void consumeNetworkMessage(TaskVO taskVO) {
        if (NetworkStatusEnum.EXECUTING.getType().equals(taskVO.getStatus())) {
            return;
        }
        StatusDTO statusDto = new StatusDTO();
        statusDto.setCurrentStatus(NetworkStatusEnum.EXECUTING.getType());
        statusDto.setId(taskVO.getOrderId());
        statusDto.setStatus(taskVO.getStatus());
        if (NetworkStatusEnum.ERROR.getType().equals(taskVO.getStatus())) {
            statusDto.setMessage(taskVO.getMessage());
        }
        networkOrderMapper.updateStatusById(statusDto);
        NetworkOrder networkOrder = networkOrderMapper.selectById(taskVO.getOrderId());
        if (NetworkStatusEnum.SUCCESS.getType().equals(taskVO.getStatus())) {
            List<NetworkSubnetOrder> networkSubnetOrders = networkSubnetOrderMapper.selectByNetworkId(taskVO.getOrderId());
            for (NetworkSubnetOrder networkSubnetOrder : networkSubnetOrders) {
                CreateNetworkSubnetRcDTO createNetworkSubnetRcDTO = networkSubnetOrderManagerConvert.do2RcDTO(networkSubnetOrder);
                createNetworkSubnetRcDTO.setSystemSource(networkOrder.getSystemSource());
                createNetworkSubnetRcDTO.setBillId(networkOrder.getAccount());
                createNetworkSubnetRcDTO.setRegionCode(networkOrder.getRegionCode());
                if (networkSubnetOrder.getAllocationPools() != null) {
                    createNetworkSubnetRcDTO.setAllocationPools(
                            JSONObject.parseArray(networkSubnetOrder.getAllocationPools(), AllocationPool.class)
                    );
                }
                createSubnet(createNetworkSubnetRcDTO);
            }
        }
    }

    @SneakyThrows
    @Override
    public void consumeClassicNetworkMessage(TaskVO taskVO) {
        if (NetworkStatusEnum.EXECUTING.getType().equals(taskVO.getStatus())) {
            return;
        }
        //休眠
        Thread.sleep(10000);
        List<NetworkSubnetOrder> networkSubnetOrders = networkSubnetOrderMapper.selectByNetworkId(taskVO.getOrderId());
        NetworkSubnetOrder networkSubnetOrder = networkSubnetOrders.get(0);
        StatusDTO statusDto = new StatusDTO();
        statusDto.setCurrentStatus(NetworkStatusEnum.EXECUTING.getType());
        statusDto.setId(taskVO.getOrderId());
        statusDto.setStatus(taskVO.getStatus());
        if (NetworkStatusEnum.ERROR.getType().equals(taskVO.getStatus())) {
            statusDto.setMessage(taskVO.getMessage());
        }
        networkOrderMapper.updateStatusById(statusDto);
        networkSubnetOrderMapper.updateStatusByNetworkId(statusDto);
        //updateOverallStatus(taskVO.getOrderId());
        //子网下发到云枢
        if (NetworkStatusEnum.SUCCESS.getType().equals(taskVO.getStatus())) {
            IpUpdateStatusVO ipUpdateStatusRc = new IpUpdateStatusVO();
            ipUpdateStatusRc.setStatus("1");
            ipUpdateStatusRc.setIpVersion(IpLevelEnum.IP3);
            List<String> ids = new ArrayList<>();
            ids.add(networkSubnetOrder.getInstanceId());
            ipUpdateStatusRc.setInstanceId(ids);
            networkService.ipUpdateStatus(ipUpdateStatusRc);
            yunshuReportService.issue(networkSubnetOrder.getNetworkId(), 0);
        }
    }

    @Override
    public CommonResult<String> getNetworkStatus(String orderId) {
        updateOverallStatusByOrderId(orderId);
        StringBuffer sb = new StringBuffer();
        List<NetworkOrder> list = networkOrderMapper.selectNetworkByOrderId(orderId);
        if (CollectionUtils.isNotEmpty(list)) {
            List<NetworkOrder> networkList = list.stream().filter(v -> !NetworkStatusEnum.SUCCESS.getType().equals(v.getOverallStatus())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(networkList)) {
                return CommonResult.success();
            }
            for (NetworkOrder networkOrder : networkList) {
                if (!NetworkStatusEnum.SUCCESS.getType().equals(networkOrder.getOverallStatus())) {
                    String message = "";
                    if (NetworkStatusEnum.EXECUTING.getType().equals(networkOrder.getOverallStatus())) {
                        message = "：开通中<br />";
                    }
                    if (NetworkStatusEnum.ERROR.getType().equals(networkOrder.getOverallStatus())) {
                        message = "：开通失败，联系管理员处理<br />";
                    }
                    if (NetworkStatusEnum.CALLBACK.getType().equals(networkOrder.getOverallStatus())) {
                        message = "：网络组在线下开通中<br />";
                    }
                    sb.append(networkOrder.getName()).append(message);
                }
            }
            if (StringUtils.isNotEmpty(sb.toString())) {
                return CommonResult.success(sb.toString(), "");
            }
        }
        return CommonResult.success();
    }

    @Override
    public CommonResult<NetworkDetailDTO> getNetworkDetail(String networkId) {
        NetworkDetailDTO networkDetail = networkOrderMapper.selectDetailById(networkId);
        List<NetworkSubnetOrder> list = networkSubnetOrderMapper.selectByNetworkIdSuccess(networkId);
        if (CollectionUtils.isNotEmpty(list)) {
            List<String> uuids = list.stream().map(NetworkSubnetOrder::getUuid).filter(uuid -> StringUtils.isNotEmpty(uuid)).collect(Collectors.toList());
            networkDetail.setUuids(uuids);
        }

        return CommonResult.success(networkDetail);
    }


    @Override
    public CommonResult<String> callback(YunshuCallback yunshuCallback) {
        log.info("network callback yunshuCallback: {}", JSONObject.toJSONString(yunshuCallback));
        if (0 == yunshuCallback.getType()) {
            String status = 0 == yunshuCallback.getStatus() ? NetworkStatusEnum.SUCCESS.getType() : NetworkStatusEnum.ERROR.getType();
            StatusDTO statusDto = new StatusDTO();
            if (1 == yunshuCallback.getStatus()) {
                statusDto.setMessage("云枢开通网络失败");
            }
            statusDto.setId(yunshuCallback.getOrderId());
            statusDto.setOverallStatus(status);
            networkOrderMapper.updateOverallStatusById(statusDto);
            if (0 == yunshuCallback.getStatus()) {
//                smsMessageUtil.sendMessageToRule(AuthorityCodeEnum.OPERATION_GROUP.code(), SMSMessageTemplateEnum.NETWORK.message());
            }
        } else if (1 == yunshuCallback.getType()) {
            if (0 == yunshuCallback.getStatus()) {
                networkOrderMapper.updateRecoveryStatusById(yunshuCallback.getOrderId(), RecoveryStatusEnum.RECOVERY_COMPLETE.getType(), 1, null, null);
                recoveryWorkOrderProductManager.updateByResourceDetailId(yunshuCallback.getOrderId(), RecoveryStatusEnum.RECOVERY_COMPLETE.getType().toString(), null);

            }
        }

        return CommonResult.success("");
    }

    @Override
    public void networkRecycle(List<NetworkConfirmDTO> list) {
        Long userId = UserHelper.INSTANCE.getCurrentUserId();
        log.info("networkRecycle list:{}", JSONObject.toJSONString(list));
        for (NetworkConfirmDTO networkConfirmDTO : list) {
            networkConfirmDTO.setUpdatedBy(userId);
            if (networkConfirmDTO.getType().equalsIgnoreCase(GoodsTypeEnum.VPC.getCode())) {
                vpcMessageService.vpcRecycle(networkConfirmDTO, userId);
            } else if (networkConfirmDTO.getType().equalsIgnoreCase(GoodsTypeEnum.NETWORK.getCode())) {
                networkRecycle(networkConfirmDTO, userId);
            } else {
                throw new BusinessException(BusinessExceptionEnum.FAILED.code, String.format("暂时不支持类型为：【%s】网络数据的确认", networkConfirmDTO.getType()));
            }
        }
    }

    private void networkRecycle(NetworkConfirmDTO networkConfirmDTO, Long userId) {
        RecoveryWorkOrderProductDTO recoveryWorkOrderProductDTO = recoveryWorkOrderProductManager.getById(networkConfirmDTO.getId());
        NetworkOrder networkOrder = networkOrderMapper.selectByIdAndRecoveryStatus(recoveryWorkOrderProductDTO.getResourceDetailId(),
                RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
        if (networkOrder == null) {
            log.error("networkRecycle network不是待回收状态，不回收 id:{}", networkConfirmDTO.getId());
            return;
        }
        networkOrderMapper.updateRecoveryStatusById(networkOrder.getId(), RecoveryStatusEnum.RECOVERING.getType(), 1, userId, null);
        recoveryWorkOrderProductManager.updateByResourceDetailId(networkOrder.getId(), RecoveryStatusEnum.RECOVERING.getType().toString(), null);

        OrderCommonDTO oacOrder = orderDataProvideService.getOrderCommon(networkOrder.getOrderId());
        List<NetworkSubnetOrder> networkSubnetOrderList = networkSubnetOrderMapper.selectByNetworkId(networkOrder.getId());
        for (NetworkSubnetOrder subnetOrder : networkSubnetOrderList) {
            SubnetRcDTO subnetRcDto = new SubnetRcDTO();
            subnetRcDto.setBillId(oacOrder.getBillId());
            subnetRcDto.setRegionCode(networkOrder.getRegionCode());
            subnetRcDto.setInstanceId(subnetOrder.getId());
            subnetRcDto.setId(subnetOrder.getId());
            subnetRcDto.setVpcId(subnetOrder.getNetworkId());
            subnetRcDto.setOptUuid(UuidUtil.getUUID());
            log.info("子网删除请求参数 networkRecycle:{}", JSONObject.toJSONString(subnetRcDto));
            String requestUrl = resourceHandle.resourceCenterUrl + resourceHandle.getSubnetDelete();
            Map<String, Object> response = HttpClientUtil.post(requestUrl, JSONObject.toJSONString(subnetRcDto), getHeaders());
            log.info("子网删除请求参数 networkRecycle response:{}", response);
            boolean isError = false;
            String message = "";
            Integer code = (Integer) response.get("code");
            if (code != 200) {
                isError = true;
                message = JSONObject.toJSONString(response);
                log.error("子网删除失败 error {}", response);
            }
            String json = (String) response.get("json");
            JSONObject jsonObject = JSONObject.parseObject(json);
            String success = jsonObject.getString("success");
            if (!"1".equals(success)) {
                isError = true;
                message = jsonObject.get("message").toString();
                log.error("子网删除失败 error {}", message);
            }
            if (isError) {
                networkSubnetOrderMapper.updateRecoveryStatusById(subnetOrder.getId(), RecoveryStatusEnum.RECLAIM_FAILURE.getType(), 1, message);
                networkOrderMapper.updateRecoveryStatusById(subnetOrder.getNetworkId(), RecoveryStatusEnum.RECLAIM_FAILURE.getType(), 1, null, message);
                recoveryWorkOrderProductManager.updateByResourceDetailId(networkOrder.getId(), RecoveryStatusEnum.RECLAIM_FAILURE.getType().toString(), message);
                return;
            }

            networkSubnetOrderMapper.updateRecoveryStatusById(subnetOrder.getId(), RecoveryStatusEnum.RECOVERING.getType(), 0, null);
        }
    }

    @Override
    @Lock(prefixKey = "key-recycleNetworkMessage", waitTime = 15000, key = {"#taskVO.orderId"})
    public void recycleNetworkMessage(TaskVO taskVO) {
        if (NetworkStatusEnum.SUCCESS.getType().equals(taskVO.getStatus())) {
            networkOrderMapper.updateRecoveryStatusById(taskVO.getOrderId(), RecoveryStatusEnum.RECOVERY_COMPLETE.getType(), 0, null, null);
            recoveryWorkOrderProductManager.updateByResourceDetailId(taskVO.getOrderId(), RecoveryStatusEnum.RECOVERY_COMPLETE.getType().toString(), null);

            cmdbReportService.deleteIp(taskVO.getOrderId());
            yunshuReportService.issue(taskVO.getOrderId(), 1);
        } else if (NetworkStatusEnum.ERROR.getType().equals(taskVO.getStatus())) {
            networkOrderMapper.updateRecoveryStatusById(taskVO.getOrderId(), RecoveryStatusEnum.RECLAIM_FAILURE.getType(), 1, null, taskVO.getMessage());
            recoveryWorkOrderProductManager.updateByResourceDetailId(taskVO.getOrderId(), RecoveryStatusEnum.RECLAIM_FAILURE.getType().toString(), taskVO.getMessage());
        }
    }

    @Override
    @Lock(prefixKey = "key-recycleSubnetMessage", waitTime = 15000, key = {"#taskVO.orderId"})
    public void recycleSubnetMessage(TaskVO taskVO, String id) {
        if (NetworkStatusEnum.SUCCESS.getType().equals(taskVO.getStatus())) {
            networkSubnetOrderMapper.updateRecoveryStatusById(taskVO.getOrderId(), RecoveryStatusEnum.RECOVERY_COMPLETE.getType(), 0, null);
        } else if (NetworkStatusEnum.ERROR.getType().equals(taskVO.getStatus())) {
            networkSubnetOrderMapper.updateRecoveryStatusById(taskVO.getOrderId(), RecoveryStatusEnum.RECLAIM_FAILURE.getType(), 1, taskVO.getMessage());
        }
        updateNetworkRecoveryStatus(id);
    }

    @Override
    public List<Long> selectBusinessIdByNetworkIds(List<String> goodsIds) {
        QueryWrapper<NetworkOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("DELETED", StatusEnum.NORMAL.code());
        queryWrapper.in("ID", goodsIds);
        List<NetworkOrder> networkOrders = networkOrderMapper.selectList(queryWrapper);
        List<Long> tenantIds = networkOrders.stream().map(NetworkOrder::getTenantId).collect(Collectors.toList());
        return businessService.selectBusinessSystemByTenantId(tenantIds);
    }

    @Override
    public List<NetworkOrderResult> selectByIds(List<String> goodsIdList, Map<String, Long> resourceMap) {
        List<NetworkOrderResult> networkOrderResults = networkOrderMapper.selectNetworkRecoveryList(goodsIdList);
        networkOrderResults.forEach(networkOrderResult -> {
            Long storageId = resourceMap.getOrDefault(networkOrderResult.getId(), 0L);
            networkOrderResult.setStorageId(storageId);
        });
        return networkOrderResults;
    }

    private void updateNetworkRecoveryStatus(String id) {
        List<NetworkSubnetOrder> list = networkSubnetOrderMapper.selectByNetworkId(id);
        // 1:SUCCESS 2:PENDING 4:EXECUTION 8:ERROR
        int index = 0;
        String message = "";
        for (NetworkSubnetOrder subnet : list) {
            if (RecoveryStatusEnum.RECLAIM_FAILURE.getType().equals(subnet.getRecoveryStatus())) {
                index = Math.max(index, 8);
                message = subnet.getMessage();
                break;
            }
            if (RecoveryStatusEnum.RECOVERING.getType().equals(subnet.getRecoveryStatus())) {
                index = Math.max(index, 4);
                continue;
            }
            if (RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType().equals(subnet.getRecoveryStatus())
                    || RecoveryStatusEnum.ORDER_TO_BE_RECOVERED.getType().equals(subnet.getRecoveryStatus())) {
                index = Math.max(index, 2);
            }
            if (RecoveryStatusEnum.RECOVERY_COMPLETE.getType().equals(subnet.getRecoveryStatus())) {
                index = Math.max(index, 1);
            }
        }
        if (index == 1 || index == 8) {
            Integer status = index == 1 ? RecoveryStatusEnum.RECOVERY_COMPLETE.getType() : RecoveryStatusEnum.RECLAIM_FAILURE.getType();
            networkOrderMapper.updateSubnetRecoveryStatusById(id, status, message);
            if (index == 1) {
                // todo 回收网络
                deleteNetwork(id);
            }
        }
    }

    @Override
    public void deleteNetwork(String id) {
        NetworkOrder networkOrder = networkOrderMapper.selectById(id);
        if (!RecoveryStatusEnum.RECOVERING.getType().equals(networkOrder.getRecoveryStatus()) && !RecoveryStatusEnum.RECOVERY_COMPLETE.getType().equals(networkOrder.getSubnetRecoveryStatus())) {
            log.info("网络删除，网络状态不对，不执行 id:{}", id);
        }
        Long tenantId = platformService.getOrCreateTenantId(networkOrder.getAccount(), networkOrder.getRegionCode());
        DeleteNetworkRcDTO dto = new DeleteNetworkRcDTO();
        dto.setTenantId(tenantId);
        dto.setGlobalId(id);
        log.info("网络删除请求参数 DeleteNetworkRcDto:{}", JSONObject.toJSONString(dto));
        String requestUrl = resourceHandle.resourceCenterUrl + resourceHandle.getNetworkDelete();
        Map<String, Object> response = HttpClientUtil.post(requestUrl, JSONObject.toJSONString(dto), getHeaders());
        log.info("网络删除返回参数 DeleteNetworkRcDto response:{}", response);
        Integer code = (Integer) response.get("code");
        if (code != 200) {
            log.error("网络删除失败 error {}", response);
            networkOrderMapper.updateRecoveryStatusById(networkOrder.getId(), RecoveryStatusEnum.RECLAIM_FAILURE.getType(), 1, null, JSONObject.toJSONString(response));
            recoveryWorkOrderProductManager.updateByResourceDetailId(networkOrder.getId(), RecoveryStatusEnum.RECLAIM_FAILURE.getType().toString(), JSONObject.toJSONString(response));
            return;
        }
        String json = (String) response.get("json");
        JSONObject jsonObject = JSONObject.parseObject(json);
        String success = jsonObject.getString("success");
        if (!"1".equals(success)) {
            String message = jsonObject.get("message").toString();
            log.error("网络删除失败 error {}", message);
            networkOrderMapper.updateRecoveryStatusById(networkOrder.getId(), RecoveryStatusEnum.RECLAIM_FAILURE.getType(), 1, null, message);
            recoveryWorkOrderProductManager.updateByResourceDetailId(networkOrder.getId(), RecoveryStatusEnum.RECLAIM_FAILURE.getType().toString(), message);

            return;
        }
        networkOrderMapper.updateRecoveryStatusById(networkOrder.getId(), RecoveryStatusEnum.RECOVERING.getType(), 1, null, null);
        recoveryWorkOrderProductManager.updateByResourceDetailId(networkOrder.getId(), RecoveryStatusEnum.RECOVERING.getType().toString(), null);

    }

    @Override
    public int selectRecoveryStatusByIds(List<String> networkIds, Integer recoveryStatus) {
        List<NetworkOrder> networkOrders = networkOrderMapper.selectByIds(networkIds, recoveryStatus, StatusEnum.NORMAL.code());
        return CollectionUtil.isNotEmpty(networkOrders) ? networkOrders.size() : 0;
    }

    @Override
    public List<NetworkSubnetOrderResult> selectSubnetsByNetworkId(String networkId) {
        List<NetworkSubnetOrder> networkSubnetOrders = networkSubnetOrderMapper.selectByNetworkId(networkId);
        return networkSubnetOrders.stream()
                .map(subnetOrder -> {
                    NetworkSubnetOrderResult result = new NetworkSubnetOrderResult();
                    BeanUtils.copyProperties(subnetOrder, result);
                    return result;
                })
                .collect(Collectors.toList());
    }

    private void createSubnet(CreateNetworkSubnetRcDTO rcDto) {
        String status = NetworkStatusEnum.EXECUTING.getType();
        String message = "";
        String resourceId = "";
        rcDto.setIpVersion(getIpVersion(rcDto.getIpVersionStr()));
        Map<String, String> header = getHeaders();
        String requestUrl = resourceHandle.resourceCenterUrl + resourceHandle.getSubnetCreate();
        log.info("network子网创建入参：{}", JSONObject.toJSONString(rcDto));
        Map<String, Object> response = HttpClientUtil.post(requestUrl, JSONObject.toJSONString(rcDto), header);
        log.info("network子网创建返回参数 response:{}", response);
        Integer code = (Integer) response.get("code");
        String json = (String) response.get("json");
        if (code == 200) {
            JSONObject jsonObject = JSONObject.parseObject(json);
            String success = jsonObject.getString("success");
            if (!"1".equals(success)) {
                status = NetworkStatusEnum.ERROR.getType();
                message = jsonObject.get("message").toString();
                log.error("network子网创建失败 error {}", message);
            } else if (jsonObject.containsKey("entity")) {
                JSONObject entity = jsonObject.getJSONObject("entity");
                resourceId = entity.getString("resourceId");
            }
        } else {
            status = NetworkStatusEnum.ERROR.getType();
            message = "网络请求失败";
            if (StringUtils.isNotBlank(json)) {
                JSONObject jsonObject = JSONObject.parseObject(json);
                message = jsonObject.get("message").toString();
            }
            log.error("network子网创建失败 {}", message);
        }
        StatusDTO statusDto = new StatusDTO();
        statusDto.setId(rcDto.getGlobalId());
        statusDto.setStatus(status);
        statusDto.setMessage(message);
        statusDto.setCurrentStatus(NetworkStatusEnum.PENDING.getType());
        statusDto.setResourceId(resourceId);
        networkSubnetOrderMapper.updateStatusById(statusDto);
    }


    @Override
    @Lock(prefixKey = "key-consumeSubnetMessage", waitTime = 15000, key = {"#taskVO.orderId"})
    public void consumeSubnetMessage(TaskVO taskVO) {
        if (NetworkStatusEnum.EXECUTING.getType().equals(taskVO.getStatus())) {
            return;
        }
        log.info("consumeSubnetMessage start taskVO:{}", JSONObject.toJSONString(taskVO));
        NetworkSubnetOrder networkSubnetOrder = networkSubnetOrderMapper.selectById(taskVO.getOrderId());
        StatusDTO statusDto = new StatusDTO();
        statusDto.setId(taskVO.getOrderId());
        statusDto.setStatus(taskVO.getStatus());
        statusDto.setCurrentStatus(NetworkStatusEnum.EXECUTING.getType());
        if (NetworkStatusEnum.ERROR.getType().equals(taskVO.getStatus())) {
            statusDto.setMessage(taskVO.getMessage());
        }
        networkSubnetOrderMapper.updateStatusById(statusDto);
        updateOverallStatus(taskVO.getOrderId());
        if (NetworkStatusEnum.SUCCESS.getType().equals(taskVO.getStatus())) {
            IpUpdateStatusVO ipUpdateStatusRc = new IpUpdateStatusVO();
            ipUpdateStatusRc.setStatus("1");
            ipUpdateStatusRc.setIpVersion(IpLevelEnum.IP3);
            List<String> ids = new ArrayList<>();
            ids.add(networkSubnetOrder.getInstanceId());
            ipUpdateStatusRc.setInstanceId(ids);
            networkService.ipUpdateStatus(ipUpdateStatusRc);
            yunshuReportService.issue(networkSubnetOrder.getNetworkId(), 0);
            cmdbIP3StatusManager.updateSubnetOpenStatus(networkSubnetOrder.getInstanceId(), 1, null);
        } else {
            // 原状态是0的，才更新成-1（任何一个成功就不应该删除）
            cmdbIP3StatusManager.updateSubnetOpenStatus(networkSubnetOrder.getInstanceId(), -1, 0);
        }
        sendBatchJobMessage(taskVO);
    }
    private void sendBatchJobMessage(TaskVO taskVO) {
        NetworkSubnetOrder subnetDO = subnetMapper.selectById(taskVO.getOrderId());
        if (subnetDO == null) {
            log.warn("subnetDO is null, taskVO:{}", taskVO);
            return;
        }
        RMap<String, String> map = redissonClient.getMap(redisKeyPrefix + subnetDO.getNetworkId() + ":" + subnetDO.getId());
        if (map != null) {
            String subOrderId = map.get("subOrderId");
            String jobExecutionId = map.get("jobExecutionId");
            if (StringUtils.isNotBlank(subOrderId) && StringUtils.isNotBlank(jobExecutionId)) {
                BatchRestartModel batchRestartModel = new BatchRestartModel()
                        .setJobExecutionId(Long.parseLong(jobExecutionId))
                        .setSubOrderId(subOrderId)
                        .setProductType("network")
                        .setOrderType(1);
                if (NetworkStatusEnum.ERROR.getType().equals(taskVO.getStatus())) {
                    batchRestartModel.setOpenStatus((ResOpenEnum.OPEN_FAIL.getCode()));
                    batchRestartModel.setMessage(taskVO.getMessage());
                } else {
                    batchRestartModel.setOpenStatus((ResOpenEnum.OPEN_SUCCESS.getCode()));
                }
                kafkaTemplate.send(BatchRestartConsumer.DAG_BATCH_RESTART, KafkaMessage.of(batchRestartModel));
            } else {
                log.warn("subOrderId is blank or jobExecutionId is blank, taskVO:{}, subOrderId:{}, jobExecutionId:{}", taskVO, subOrderId, jobExecutionId);
            }
        } else {
            log.info("subnetDO is null, taskVO:{}", taskVO);
        }

    }

    @Async
    public void updateOverallStatus(String id) {
        NetworkSubnetOrder networkSubnetOrder = networkSubnetOrderMapper.selectById(id);
        for (int i = 0; i < 10; i++) {
            NetworkOrder networkOrder = networkOrderMapper.selectById(networkSubnetOrder.getNetworkId());
            boolean status = updateOverallStatusById(networkOrder);
            if (status) {
                break;
            }
            try {
                Thread.sleep(20000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        Integer num = networkSubnetOrderMapper.selectCountByNetworkIdSuccess(networkSubnetOrder.getNetworkId());
        networkOrderMapper.updateSubnetNumById(networkSubnetOrder.getNetworkId(), num);
    }


    private void updateOverallStatusByOrderId(String orderId) {
        List<NetworkOrder> networkOrderList = networkOrderMapper.selectNetworkByOrderId(orderId);
        for (NetworkOrder networkOrder : networkOrderList) {
            if (NetworkStatusEnum.SUCCESS.getType().equals(networkOrder.getOverallStatus())) {
                continue;
            }
            updateOverallStatusById(networkOrder);
        }
    }


    private boolean updateOverallStatusById(NetworkOrder networkOrder) {
        List<NetworkSubnetOrder> list = networkSubnetOrderMapper.selectByNetworkId(networkOrder.getId());
        // 1:SUCCESS 2:PENDING 4:EXECUTION 8:ERROR
        int index = 0;
        for (NetworkSubnetOrder subnet : list) {
            if (NetworkStatusEnum.ERROR.getType().equals(subnet.getStatus())) {
                index = Math.max(index, 8);
                continue;
            }
            if (NetworkStatusEnum.EXECUTING.getType().equals(subnet.getStatus())) {
                index = Math.max(index, 4);
                continue;
            }
            if (NetworkStatusEnum.PENDING.getType().equals(subnet.getStatus())) {
                index = Math.max(index, 2);
                continue;
            }
            if (NetworkStatusEnum.SUCCESS.getType().equals(subnet.getStatus())) {
                index = Math.max(index, 1);
            }
        }
        if (index == 1 || index == 8) {
            String status = index == 1 ? NetworkStatusEnum.SUCCESS.getType() : NetworkStatusEnum.ERROR.getType();
            if (NetworkStatusEnum.SUCCESS.getType().equals(status)
                    && CatalogueDomain.PLATFORM_CHILD.getCode().equals(networkOrder.getDomainCode())
                    && StringUtils.isNotEmpty(networkOrder.getVlan())) {
                status = NetworkStatusEnum.CALLBACK.getType();
            }
            StatusDTO statusDto = new StatusDTO();
            statusDto.setCurrentStatus(NetworkStatusEnum.EXECUTING.getType());
            statusDto.setId(networkOrder.getId());
            statusDto.setOverallStatus(status);
            networkOrderMapper.updateOverallStatusById(statusDto);
            return true;
        }
        return false;
    }

    private NetworkOrder getNetworkOrder(NetworkOrderDTO networkOrderDTO, NetworkCreateDTO networkCreateDTO) {
        NetworkOrder networkOrder = new NetworkOrder();
        String id = UuidUtil.getGid(NetworkPrefixEnum.NET.getType());
        networkOrder.setId(id);
        networkOrder.setCreatedBy(networkCreateDTO.getUserId());
        networkOrder.setCreatedTime(new Date());
        networkOrder.setName(networkOrderDTO.getName());
        networkOrder.setOrderId(networkCreateDTO.getOrderId());
        networkOrder.setOrderCode(networkCreateDTO.getOrderCode());
        networkOrder.setOrderId(networkCreateDTO.getOrderId());
        networkOrder.setSubnetNum(networkOrderDTO.getSubnets().size());
        networkOrder.setTenantId(networkCreateDTO.getTenantId());
        networkOrder.setAccount(networkCreateDTO.getBillId());
        networkOrder.setPlane(networkOrderDTO.getPlane());
        networkOrder.setInstanceId(networkOrderDTO.getInstanceId());
        networkOrder.setVlan(networkOrderDTO.getVlan());
        networkOrder.setVlanId(networkOrderDTO.getVlanId());
        networkOrder.setNetworkType(networkOrderDTO.getNetworkType());
        networkOrder.setRegionCode(networkCreateDTO.getRegionCode());
        networkOrder.setAzCode(networkCreateDTO.getAzCode());
        networkOrder.setCatalogueDomainCode(networkCreateDTO.getCatalogueDomainCode());
        networkOrder.setCatalogueDomainName(networkCreateDTO.getCatalogueDomainName());
        networkOrder.setDomainCode(networkCreateDTO.getDomainCode());
        networkOrder.setDomainName(networkCreateDTO.getDomainName());
        networkOrder.setFunctionalModule(networkCreateDTO.getFunctionalModule());
        networkOrder.setDeleted(1);
        networkOrder.setGid(id);
        networkOrder.setDetail(networkOrderDTO.getDetail());
        networkOrder.setStatus(NetworkStatusEnum.EXECUTING.getType());
        networkOrder.setOverallStatus(NetworkStatusEnum.EXECUTING.getType());
        networkOrder.setSystemSource(networkCreateDTO.getSystemSource());
        networkOrder.setApplyUserId(networkCreateDTO.getApplyUserId());
        networkOrder.setApplyUserName(networkCreateDTO.getApplyUserName());
        networkOrder.setBusinessSysId(networkCreateDTO.getBusinessSysId());
        networkOrder.setBusinessSysName(networkCreateDTO.getBusinessSysName());
        networkOrder.setTenantName(networkCreateDTO.getTenantName());
        networkOrder.setSourceType(networkCreateDTO.getSourceType());
        networkOrder.setModuleId(networkCreateDTO.getModuleId());
        networkOrder.setModuleName(networkCreateDTO.getModuleName());
        networkOrder.setExternalNetworkId(networkOrderDTO.getExternalNetworkId());
        networkOrder.setExternalNetworkName(networkOrderDTO.getExternalNetworkName());
        return networkOrder;
    }

    private List<NetworkSubnetOrder> getNetworkSubnetOrder(NetworkOrder networkOrder, NetworkOrderDTO networkOrderDTO) {
        List<NetworkSubnetOrder> list = new ArrayList<>();
        for (SubnetDTO subnetDTO : networkOrderDTO.getSubnets()) {
            NetworkSubnetOrder networkSubnetOrder = new NetworkSubnetOrder();
            String id = UuidUtil.getGid(NetworkPrefixEnum.SUB.getType());
            networkSubnetOrder.setId(id);
            networkSubnetOrder.setInstanceId(subnetDTO.getInstanceId());
            networkSubnetOrder.setLevel2InstanceId(subnetDTO.getLevel2InstanceId());
            networkSubnetOrder.setNetworkId(networkOrder.getId());
            networkSubnetOrder.setCreatedTime(new Date());
            networkSubnetOrder.setSubnetName(networkOrder.getName() + "_" + subnetDTO.getIpVersion());
            networkSubnetOrder.setCidr(subnetDTO.getCidr());
            networkSubnetOrder.setGateway(GatewayCalculatorUtil.getGateway(subnetDTO.getCidr()));
            networkSubnetOrder.setIpVersion(subnetDTO.getIpVersion());
            networkSubnetOrder.setDeleted(1);
            networkSubnetOrder.setStatus(NetworkStatusEnum.PENDING.getType());
            networkSubnetOrder.setDescription(networkOrderDTO.getDescription());
            networkSubnetOrder.setUuid(subnetDTO.getUuid());
            if (subnetDTO.getAllocationPools() != null) {
                networkSubnetOrder.setAllocationPools(JSONObject.toJSONString(subnetDTO.getAllocationPools()));
            }
            list.add(networkSubnetOrder);
        }
        return list;
    }

    private CreateNetworkRcDTO getCreateNetworkRcDto(NetworkOrder networkOrder, NetworkOrderDTO networkOrderDTO, NetworkCreateDTO networkCreateDTO) {
        CreateNetworkRcDTO createNetworkRcDto = new CreateNetworkRcDTO();
        createNetworkRcDto.setBillId(networkOrder.getAccount());
        createNetworkRcDto.setOrderId(networkOrder.getId());
        createNetworkRcDto.setGlobalId(networkOrder.getId());
        createNetworkRcDto.setRegionCode(networkOrder.getRegionCode());
        createNetworkRcDto.setExternalNetworkId(networkOrderDTO.getExternalNetworkId());
        createNetworkRcDto.setName(networkOrder.getName());
        createNetworkRcDto.setNetworkType(networkOrderDTO.getNetworkType());
        if (StringUtils.isNotEmpty(networkOrderDTO.getVlan())) {
            createNetworkRcDto.setSegmentationId(networkOrderDTO.getVlan());
        }
        createNetworkRcDto.setShared(false);
        createNetworkRcDto.setExternal(false);
        createNetworkRcDto.setAdminStateUp(true);
        createNetworkRcDto.setDescription(networkOrderDTO.getDescription());
        List<String> aliasZoneIds = new ArrayList<>();
        aliasZoneIds.add(networkCreateDTO.getAzCode());
        createNetworkRcDto.setAliasZoneIds(aliasZoneIds);
        return createNetworkRcDto;
    }

    private Integer getIpVersion(String ipVersionStr) {
        if ("IPv4".equals(ipVersionStr)) {
            return 4;
        } else {
            return 6;
        }
    }

    private Map<String, String> getHeaders() {
        Map<String, String> header = new HashMap<>();
        header.put("RemoteUser", "BusinessCenter");
        header.put("Content-Type", "application/json");
        return header;
    }

}

