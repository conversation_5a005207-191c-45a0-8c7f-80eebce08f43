package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.CmdbIP3StatusDO;
import com.datatech.slgzt.model.dto.CmdbIP3StatusDTO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CmdbIP3StatusManagerConvert {

    CmdbIP3StatusDTO do2dto(CmdbIP3StatusDO cmdbIP3StatusDO);

    CmdbIP3StatusDO dto2do(CmdbIP3StatusDTO cmdbIP3StatusDTO);

    List<CmdbIP3StatusDTO> dos2DTOs(List<CmdbIP3StatusDO> cmdbIP3StatusDOS);

    List<CmdbIP3StatusDO> dtoList2DOs(List<CmdbIP3StatusDTO> cmdbIP3StatusDTOS);
} 