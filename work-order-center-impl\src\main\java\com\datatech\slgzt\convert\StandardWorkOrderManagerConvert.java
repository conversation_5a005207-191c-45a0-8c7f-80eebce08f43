package com.datatech.slgzt.convert;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.dao.model.order.StandardWorkOrderDO;
import com.datatech.slgzt.model.dto.SaveSchemaAuditLogDTO;
import com.datatech.slgzt.model.dto.StandardWorkOrderDTO;
import com.datatech.slgzt.model.dto.order.StandardWorkOrderOpenReqDTO;
import com.datatech.slgzt.model.file.UploadFileModel;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.Collections;
import java.util.List;

@Mapper(componentModel = "spring")
public interface StandardWorkOrderManagerConvert {

    @Mapping(target = "cloudEcsResourceList", source = "cloudEcsResourceList", qualifiedByName = "cloudEcsResourceList")
    @Mapping(target = "cpuEcsResourceList", source = "cpuEcsResourceList", qualifiedByName = "cpuEcsResourceList")
    @Mapping(target = "mysqlModelList", source = "mysqlModelList", qualifiedByName = "mysqlModelList")
    @Mapping(target = "postgreSqlModelList", source = "postgreSqlModelList", qualifiedByName = "postgreSqlModelList")
    @Mapping(target = "redisModelList", source = "redisModelList", qualifiedByName = "redisModelList")
    @Mapping(target = "evsModelList", source = "evsModelList", qualifiedByName = "evsModelList")
    @Mapping(target = "slbModelList", source = "slbModelList", qualifiedByName = "slbModelList")
    @Mapping(target = "natGatwayModelList", source = "natGatwayModelList", qualifiedByName = "natGatwayModelList")
    @Mapping(target = "obsModelList", source = "obsModelList", qualifiedByName = "obsModelList")
    @Mapping(target = "resourceApplyFile", source = "resourceApplyFile", qualifiedByName = "resourceApplyFile")
    @Mapping(target = "auditLogList", source = "auditLogList", qualifiedByName = "auditLogList")
    @Mapping(target = "eipModelList", source = "eipModelList", qualifiedByName = "eipModelList")
    StandardWorkOrderDTO do2DTO(StandardWorkOrderDO orderDO);

    @Mapping(target = "cloudEcsResourceList", source = "cloudEcsResourceList", qualifiedByName = "cloudEcsResourceList")
    @Mapping(target = "cpuEcsResourceList", source = "cpuEcsResourceList", qualifiedByName = "cpuEcsResourceList")
    @Mapping(target = "mysqlModelList", source = "mysqlModelList", qualifiedByName = "mysqlModelList")
    @Mapping(target = "postgreSqlModelList", source = "postgreSqlModelList", qualifiedByName = "postgreSqlModelList")
    @Mapping(target = "redisModelList", source = "redisModelList", qualifiedByName = "redisModelList")
    @Mapping(target = "evsModelList", source = "evsModelList", qualifiedByName = "evsModelList")
    @Mapping(target = "slbModelList", source = "slbModelList", qualifiedByName = "slbModelList")
    @Mapping(target = "natGatwayModelList", source = "natGatwayModelList", qualifiedByName = "natGatwayModelList")
    @Mapping(target = "obsModelList", source = "obsModelList", qualifiedByName = "obsModelList")
    @Mapping(target = "resourceApplyFile", source = "resourceApplyFile", qualifiedByName = "resourceApplyFile")
    @Mapping(target = "auditLogList", source = "auditLogList", qualifiedByName = "auditLogList")
    @Mapping(target = "eipModelList", source = "eipModelList", qualifiedByName = "eipModelList")
    StandardWorkOrderDO dto2DO(StandardWorkOrderDTO orderDTO);

    StandardWorkOrderDO reqDto2DO(StandardWorkOrderOpenReqDTO reqDTO);

    @Named("mysqlModelList")
    default String mysqlModelList(List<EcsModel> mysqlModelList) {
        if (ObjNullUtils.isNull(mysqlModelList)) {
            return null;
        }
        return JSON.toJSONString(mysqlModelList);
    }

    @Named("mysqlModelList")
    default List<EcsModel> mysqlModelList(String mysqlModelList) {
        if (ObjNullUtils.isNull(mysqlModelList)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(mysqlModelList, EcsModel.class);
    }

    @Named("postgreSqlModelList")
    default String postgreSqlModelList(List<EcsModel> postgreSqlModelList) {
        if (ObjNullUtils.isNull(postgreSqlModelList)) {
            return null;
        }
        return JSON.toJSONString(postgreSqlModelList);
    }

    @Named("postgreSqlModelList")
    default List<EcsModel> postgreSqlModelList(String postgreSqlModelList) {
        if (ObjNullUtils.isNull(postgreSqlModelList)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(postgreSqlModelList, EcsModel.class);
    }

    @Named("redisModelList")
    default String redisModelList(List<EcsModel> redisModelList) {
        if (ObjNullUtils.isNull(redisModelList)) {
            return null;
        }
        return JSON.toJSONString(redisModelList);
    }

    @Named("redisModelList")
    default List<EcsModel> redisModelList(String redisModelList) {
        if (ObjNullUtils.isNull(redisModelList)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(redisModelList, EcsModel.class);
    }

    @Named("cloudEcsResourceList")
    default String ecsResourceList(List<CloudEcsResourceModel> ecsResourceList) {
        if (ObjNullUtils.isNull(ecsResourceList)) {
            return null;
        }
        return JSON.toJSONString(ecsResourceList);
    }

    @Named("cloudEcsResourceList")
    default List<CloudEcsResourceModel> ecsResourceList(String ecsResourceList) {
        if (ObjNullUtils.isNull(ecsResourceList)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(ecsResourceList, CloudEcsResourceModel.class);
    }

    @Named("cpuEcsResourceList")
    default String gcsResourceList(List<CpuEcsResourceModel> gcsResourceList) {
        if (ObjNullUtils.isNull(gcsResourceList)) {
            return null;
        }
        return JSON.toJSONString(gcsResourceList);
    }

    @Named("cpuEcsResourceList")
    default List<CpuEcsResourceModel> gcsResourceList(String gcsResourceList) {
        if (ObjNullUtils.isNull(gcsResourceList)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(gcsResourceList, CpuEcsResourceModel.class);
    }

    @Named("evsModelList")
    default String evsResourceList(List<EvsModel> evsResourceList) {
        if (ObjNullUtils.isNull(evsResourceList)) {
            return null;
        }
        return JSON.toJSONString(evsResourceList);
    }

    @Named("evsModelList")
    default List<EvsModel> evsResourceList(String evsResourceList) {
        if (ObjNullUtils.isNull(evsResourceList)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(evsResourceList, EvsModel.class);
    }

    @Named("eipModelList")
    default String eipResourceList(List<EipModel> evsResourceList) {
        if (ObjNullUtils.isNull(evsResourceList)) {
            return null;
        }
        return JSON.toJSONString(evsResourceList);
    }

    @Named("eipModelList")
    default List<EipModel> eipResourceList(String eipResourceList) {
        if (ObjNullUtils.isNull(eipResourceList)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(eipResourceList, EipModel.class);
    }

    @Named("slbModelList")
    default String slbResourceList(List<SlbModel> slbResourceList) {
        if (ObjNullUtils.isNull(slbResourceList)) {
            return null;
        }
        return JSON.toJSONString(slbResourceList);
    }

    @Named("slbModelList")
    default List<SlbModel> slbResourceList(String slbResourceList) {
        if (ObjNullUtils.isNull(slbResourceList)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(slbResourceList, SlbModel.class);
    }

    @Named("natGatwayModelList")
    default String natResourceList(List<NatGatwayModel> natResourceList) {
        if (ObjNullUtils.isNull(natResourceList)) {
            return null;
        }
        return JSON.toJSONString(natResourceList);
    }

    @Named("natGatwayModelList")
    default List<NatGatwayModel> natResourceList(String natResourceList) {
        if (ObjNullUtils.isNull(natResourceList)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(natResourceList, NatGatwayModel.class);
    }

    @Named("obsModelList")
    default String obsResourceList(List<ObsModel> obsResourceList) {
        if (ObjNullUtils.isNull(obsResourceList)) {
            return null;
        }
        return JSON.toJSONString(obsResourceList);
    }

    @Named("obsModelList")
    default List<ObsModel> obsResourceList(String obsResourceList) {
        if (ObjNullUtils.isNull(obsResourceList)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(obsResourceList, ObsModel.class);
    }

    @Named("resourceApplyFile")
    default String resourceApplyFile(List<UploadFileModel> fileModels) {
        if (ObjNullUtils.isNull(fileModels)) {
            return null;
        }
        return JSON.toJSONString(fileModels);
    }

    @Named("resourceApplyFile")
    default List<UploadFileModel> resourceApplyFile(String fileModels) {
        if (ObjNullUtils.isNull(fileModels)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(fileModels, UploadFileModel.class);
    }

    @Named("auditLogList")
    default String auditLogList(List<SaveSchemaAuditLogDTO> auditLogDTOS) {
        if (ObjNullUtils.isNull(auditLogDTOS)) {
            return null;
        }
        return JSON.toJSONString(auditLogDTOS);
    }

    @Named("auditLogList")
    default List<SaveSchemaAuditLogDTO> auditLogList(String auditLogDTOS) {
        if (ObjNullUtils.isNull(auditLogDTOS)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(auditLogDTOS, SaveSchemaAuditLogDTO.class);
    }

    @Named("cloudMysqlResourceList")
    default String mysqlResourceList(List<EcsModel> mysqlResourceList) {
        if (ObjNullUtils.isNull(mysqlResourceList)) {
            return null;
        }
        return JSON.toJSONString(mysqlResourceList);
    }

    @Named("cloudMysqlResourceList")
    default List<EcsModel> mysqlResourceList(String mysqlResourceList) {
        if (ObjNullUtils.isNull(mysqlResourceList)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(mysqlResourceList, EcsModel.class);
    }

    @Named("cloudRedisResourceList")
    default String redisResourceList(List<EcsModel> redisResourceList) {
        if (ObjNullUtils.isNull(redisResourceList)) {
            return null;
        }
        return JSON.toJSONString(redisResourceList);
    }

    @Named("cloudRedisResourceList")
    default List<EcsModel> redisResourceList(String redisResourceList) {
        if (ObjNullUtils.isNull(redisResourceList)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(redisResourceList, EcsModel.class);
    }
}
