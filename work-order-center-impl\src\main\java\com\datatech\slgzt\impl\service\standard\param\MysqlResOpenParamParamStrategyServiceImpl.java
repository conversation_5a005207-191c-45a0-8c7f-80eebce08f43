package com.datatech.slgzt.impl.service.standard.param;

import cn.hutool.core.util.IdUtil;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.model.layout.ResOpenReqModel;
import com.datatech.slgzt.model.nostander.MysqlV2Model;
import com.datatech.slgzt.model.nostander.PlaneNetworkModel;
import com.datatech.slgzt.model.opm.ResOpenOpm;
import com.datatech.slgzt.service.standard.ResOpenParamStrategyService;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 联合开通ecs mysql部分
 *
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月07日 17:14:29
 */
@Service
public class MysqlResOpenParamParamStrategyServiceImpl implements ResOpenParamStrategyService {




    /**
     * 请求报文：
     * {
     *     "regionCode": "region.edge_prov_zj_vmware.qz",
     *     "azCode": "az.edge_prov_zj_vmware.qz",
     *     "orderId": "a5fb21a6a40a4af3943c595526f060602",
     *     "billId": "zyfceshi0213",
     *     "vpcId": "4171f301b23543a9b8163cb06e9bb85f",
     *     "subnetId": "6e05fff1120a4990b6ce00b90147c9ee",
     *     "gId": "ccl_test0606_rds_02",
     *     "rdsName": "ccl_test0606_rds_02",
     *     "flavorCode": "mysql.c1.medium.2.1",
     *     "engineVersion": "8.0",
     *     "deployType": "ALONE",
     *     "storageType": "SAS",
     *     "storageSize": 10,
     *     "rdsLoginName": "ccl_test0606_rds_02",
     *     "rdsPwd": "11qq!!QQ",
     *     "timeZone": "+8",
     *     "tableIsLower": 0,
     *     "dbEngine": 1
     * }
     */


    /**
     * 组装参数
     */
    @Override
    public List<ResOpenReqModel.ProductOrder> assembleParam(ResOpenOpm opm) {
        //获取租户id
        Long tenantId = opm.getTenantId();
        ArrayList<ResOpenReqModel.ProductOrder> productOrders = Lists.newArrayList();
        //如果有挂载数据盘
        //取出挂载数据盘的数据 每个都要创建
        MysqlV2Model mysqlModel = opm.getMysqlModel();
        List<PlaneNetworkModel> planeNetworkModelList = mysqlModel.getPlaneNetworkModel();
        PlaneNetworkModel planeNetworkModel = planeNetworkModelList.get(0);
        ResOpenReqModel.ProductOrder mysqlProductOrder = new ResOpenReqModel.ProductOrder();
        mysqlProductOrder.setGId(mysqlModel.getId().toString());
        mysqlProductOrder.setProductOrderId(mysqlModel.getId().toString());
        mysqlProductOrder.setProductOrderType("RDS_CREATE");
        mysqlProductOrder.setProductType("rds");
        mysqlProductOrder.setSubOrderId(opm.getSubOrderId());
        ResOpenReqModel.Attrs mysqlAttrs = new ResOpenReqModel.Attrs();
        mysqlAttrs.setGId(opm.getGId());
        mysqlAttrs.setRegionCode(mysqlModel.getRegionCode());
        mysqlAttrs.setAzCode(mysqlModel.getAzCode());
        mysqlAttrs.setBillId(mysqlModel.getBillId());
        mysqlAttrs.setVpcId(planeNetworkModel.getId());
        mysqlAttrs.setSubnetId(planeNetworkModel.getSubnets().get(0).getSubnetId());
        mysqlAttrs.setRdsName(mysqlModel.getMysqlName());
        // 250808:
        // 1.由于flavor表中的deleted设置成0(规格出现双份的问题)，导致对公rdsMysql下发失败
        // 2.之前跟资源中心说过，都是用flavor_id，不使用code
        // 此问题资源中心跟着一起改，暂时约定，参数名不变，使用code字段传id
        mysqlAttrs.setFlavorCode(mysqlModel.getFlavorId());
        mysqlAttrs.setEngineVersion(mysqlModel.getEngineVersion());  
        mysqlAttrs.setDeployType(mysqlModel.getDeployType());
        mysqlAttrs.setStorageType(mysqlModel.getSysDiskType());
        mysqlAttrs.setStorageSize(mysqlModel.getSysDiskSize().longValue());
        mysqlAttrs.setRdsLoginName(mysqlModel.getUserName());
        mysqlAttrs.setRdsPwd(mysqlModel.getPassword());
        mysqlAttrs.setTimeZone(mysqlModel.getTimeZone());
        mysqlAttrs.setTableIsLower(mysqlModel.getTableIsLower());
        mysqlAttrs.setDbEngine(mysqlModel.getDbEngine());
        mysqlAttrs.setTenantId(tenantId);
        mysqlProductOrder.setAttrs(mysqlAttrs);
        productOrders.add(mysqlProductOrder);
        return productOrders;


}


/**
 * 注册strategy
 */
@Override
public ProductTypeEnum register() {
    return ProductTypeEnum.RDS_MYSQL;
}
}
