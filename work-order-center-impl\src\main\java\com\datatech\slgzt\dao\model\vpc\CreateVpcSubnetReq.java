package com.datatech.slgzt.dao.model.vpc;

import com.datatech.slgzt.model.AllocationPool;
import lombok.Data;

import java.util.List;

@Data
public class CreateVpcSubnetReq {

    private String regionCode;
    private String azCode;

    private String billId;
    public String name;
    private String cidr;
    public Integer ipVersion = 4;
    private String vpcId;
    private String orderId;
    public String globalId;
    public String gatewayIp;
    public String netType;
    private String systemSource;

    private List<AllocationPool> allocationPools;
}