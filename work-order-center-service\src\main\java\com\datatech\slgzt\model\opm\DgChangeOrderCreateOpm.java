package com.datatech.slgzt.model.opm;

import com.datatech.slgzt.model.change.ChangeReqModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月31日 15:18:05
 */
@Data
public class DgChangeOrderCreateOpm {
    // 工单id，重新提交时必传
    private String id;


    private Long tenantId;

    private String tenantName;

    private String businessSystemCode;

    private String billId;

    private String customNo;


    //--------更变设备部分字段--------------------------
    private List<ChangeReqModel> ecsPropertyList;

    private List<ChangeReqModel> evsPropertyList;

    private List<ChangeReqModel> gcsPropertyList;

    private List<ChangeReqModel> mysqlPropertyList;

    private List<ChangeReqModel> postgreSqlPropertyList;

    private List<ChangeReqModel> redisPropertyList;

    private List<ChangeReqModel> obsPropertyList;

    private List<ChangeReqModel> natPropertyList;

    private List<ChangeReqModel> slbPropertyList;

    private List<ChangeReqModel> eipPropertyList;

    private List<ChangeReqModel> rdsMysqlPropertyList;

    private Integer variablesValue;

    //创建人
    private String creator;

    //创建人id
    private Long creatorId;

}
