package com.datatech.slgzt.impl.task;

import com.datatech.slgzt.config.ScheduledTaskProperties;
import com.datatech.slgzt.model.dto.CmdbIP3StatusDTO;
import com.datatech.slgzt.model.query.NetworkCmdbQuery;
import com.datatech.slgzt.service.CmdbIP3StatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;


@Slf4j
@Component
public class CmdbIP3DeleteTask {

    @Resource
    private CmdbIP3StatusService service;

    @Resource
    private ScheduledTaskProperties scheduledTaskProperties;

    /**
     * 定时删除三级网络
     */
    @Scheduled(cron = "#{@scheduledTaskProperties.cmdbIP3DeleteCron}")
    public void cmdbIP3Delete() {
        log.info("cmdbIP3Delete start");
        LocalDateTime now = LocalDateTime.now();
        NetworkCmdbQuery query = new NetworkCmdbQuery()
                // 所有非成功的
                .setSubnetOpenStatusNotEquals(1)
                .setCreateTimeEnd(now.minusMinutes(scheduledTaskProperties.getCmdbIP3DeleteThreshold()));
        List<CmdbIP3StatusDTO> cmdbIP3StatusDTOS = service.selectList(query);
        log.info("需要处理的三级网络数量: {}", cmdbIP3StatusDTOS.size());
        for (CmdbIP3StatusDTO dto : cmdbIP3StatusDTOS) {
            service.deleteByInstanceId(dto.getInstanceId());
        }
        log.info("cmdbIP3Delete end");
    }


}
