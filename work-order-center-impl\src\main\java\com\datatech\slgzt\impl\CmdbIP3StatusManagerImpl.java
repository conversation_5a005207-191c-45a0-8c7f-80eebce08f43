package com.datatech.slgzt.impl;

import com.datatech.slgzt.convert.CmdbIP3StatusManagerConvert;
import com.datatech.slgzt.dao.CmdbIP3StatusDAO;
import com.datatech.slgzt.dao.model.CmdbIP3StatusDO;
import com.datatech.slgzt.manager.CmdbIP3StatusManager;
import com.datatech.slgzt.model.dto.CmdbIP3StatusDTO;
import com.datatech.slgzt.model.query.NetworkCmdbQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class CmdbIP3StatusManagerImpl implements CmdbIP3StatusManager {
    @Autowired
    private CmdbIP3StatusDAO dao;

    @Autowired
    private CmdbIP3StatusManagerConvert convert;

    @Override
    public void insert(String instanceId) {
        CmdbIP3StatusDO ip3 = new CmdbIP3StatusDO();
        ip3.setInstanceId(instanceId);
        dao.insert(ip3);
    }

    @Override
    public void updateSubnetOpenStatus(String instanceId, Integer subnetOpenStatus, Integer oldSubnetOpenStatus) {
        dao.updateSubnetOpenStatus(instanceId, subnetOpenStatus, oldSubnetOpenStatus);
    }

    @Override
    public CmdbIP3StatusDTO selectByInstanceId(String instanceId) {
        CmdbIP3StatusDO ip3 = dao.selectById(instanceId);
        return convert.do2dto(ip3);
    }

    @Override
    public void deleteByInstanceId(String instanceId) {
        dao.deleteById(instanceId);
    }

    @Override
    public void cancelByInstanceId(String instanceId) {
        dao.cancelById(instanceId);
    }

    @Override
    public List<CmdbIP3StatusDTO> selectList(NetworkCmdbQuery query) {
        List<CmdbIP3StatusDO> list = dao.selectList(query);
        return list.stream()
                .map(convert::do2dto)
                .collect(Collectors.toList());
    }
} 