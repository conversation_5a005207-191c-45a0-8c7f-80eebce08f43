package com.datatech.slgzt.impl.manager;

import com.datatech.slgzt.convert.DeviceGpuInfoConvert;
import com.datatech.slgzt.dao.DeviceGpuInfoDAO;
import com.datatech.slgzt.dao.model.DeviceGpuInfoDO;
import com.datatech.slgzt.manager.DeviceGpuInfoManager;
import com.datatech.slgzt.model.dto.DeviceGpuInfoDTO;
import com.datatech.slgzt.model.query.DeviceInfoQuery;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.StreamUtils;
import com.datatech.slgzt.warpper.PageWarppers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

@Service
public class DevicePhysicalCardInfoManagerImpl implements DeviceGpuInfoManager {

    @Resource
    private DeviceGpuInfoDAO devicePhysicalCardInfoDAO;
    @Resource
    private DeviceGpuInfoConvert convert;


    @Override
    public void create(DeviceGpuInfoDTO dto) {
        DeviceGpuInfoDO deviceGpuInfoDO = convert.dto2do(dto);
        deviceGpuInfoDO.setCreatedAt(LocalDateTime.now());
        devicePhysicalCardInfoDAO.insert(deviceGpuInfoDO);
    }

    @Override
    public List<String> groupModelName() {
        return devicePhysicalCardInfoDAO.groupModelName();
    }

    @Override
    public List<String> groupDeptName() {
        return devicePhysicalCardInfoDAO.groupDeptName();
    }

    @Override
    public List<String> groupBusinessSystemName() {
        return devicePhysicalCardInfoDAO.groupBusinessSystemName();
    }

    @Override
    public void update(DeviceGpuInfoDTO dto) {
        DeviceGpuInfoDO deviceGpuInfoDO = convert.dto2do(dto);
        devicePhysicalCardInfoDAO.updateById(deviceGpuInfoDO);
    }

    @Override
    public void delete(Long id) {
        devicePhysicalCardInfoDAO.deleteById(id);
    }

    @Override
    public List<DeviceGpuInfoDTO> selectDeviceGpuInfoList(DeviceInfoQuery query) {
        List<DeviceGpuInfoDO> deviceGpuInfoDOS = devicePhysicalCardInfoDAO.selectList(query);
        return StreamUtils.mapArray(deviceGpuInfoDOS, convert::do2Dto);
    }

    @Override
    public PageResult<DeviceGpuInfoDTO> queryDeviceGupInfoPage(DeviceInfoQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<DeviceGpuInfoDO> list = devicePhysicalCardInfoDAO.selectList(query);
        return PageWarppers.box(new PageInfo<>(list), convert::do2Dto);
    }

    @Override
    public DeviceGpuInfoDTO getByDeviceId(String deviceId) {
        return convert.do2Dto(devicePhysicalCardInfoDAO.getByDeviceId(deviceId));
    }

    @Override
    public void updateLastByDeviceId(DeviceGpuInfoDTO dto) {
        devicePhysicalCardInfoDAO.updateLastByDeviceId(convert.dto2do(dto));
    }
}
