<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datatech.slgzt.dao.mapper.network.VpcOrderMapper">

    <resultMap type="com.datatech.slgzt.dao.model.vpc.VpcOrder" id="BaseResultMap">
        <result property="id" column="ID"/>
        <result property="createdBy" column="CREATED_BY"/>
        <result property="createdTime" column="CREATED_TIME"/>
        <result property="updatedBy" column="UPDATED_BY"/>
        <result property="updatedTime" column="UPDATED_TIME"/>
        <result property="vpcName" column="VPC_NAME"/>
        <result property="orderId" column="ORDER_ID"/>
        <result property="subnetNum" column="SUBNET_NUM"/>
        <result property="tenantId" column="TENANT_ID"/>
        <result property="account" column="ACCOUNT"/>
        <result property="regionCode" column="REGION_CODE"/>
        <result property="ipv4Cidr" column="IPV4_CIDR"/>
        <result property="ipv6Cidr" column="IPV6_CIDR"/>
        <result property="deleted" column="DELETED"/>
        <result property="gid" column="GID"/>
        <result property="status" column="STATUS"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="recoveryStatus" column="RECOVERY_STATUS"/>
        <result property="confirmOrNot" column="CONFIRM_OR_NOT"/>
        <result property="detail" column="DETAIL"/>
        <result property="recoveryTime" column="RECOVERY_TIME"/>
        <result property="message" column="MESSAGE"/>
        <result property="plane" column="PLANE"/>
        <result property="azCode" column="AZ_CODE"/>
        <result property="functionalModule" column="FUNCTIONAL_MODULE"/>
        <result property="tenantName" column="TENANT_NAME"/>
        <result property="businessSysId" column="BUSINESS_SYS_ID"/>
        <result property="businessSysName" column="BUSINESS_SYS_NAME"/>
        <result property="applyUserId" column="APPLY_USER_ID"/>
        <result property="applyUserName" column="APPLY_USER_NAME"/>
        <result property="sourceType" column="SOURCE_TYPE"/>
        <result property="moduleId" column="MODULE_ID"/>
        <result property="moduleName" column="MODULE_NAME"/>
        <result property="orderCode" column="ORDER_CODE"/>
        <result property="catalogueDomainCode" column="CATALOGUE_DOMAIN_CODE"/>
        <result property="catalogueDomainName" column="CATALOGUE_DOMAIN_NAME"/>
        <result property="domainCode" column="DOMAIN_CODE"/>
        <result property="domainName" column="DOMAIN_NAME"/>
    </resultMap>

    <resultMap id="VpcWithSubnetMap" type="com.datatech.slgzt.model.vo.vpc.VpcOrderResult">
        <!-- 映射 User 表字段 -->
        <id property="id" column="VPC_ID"/>
        <result property="vpcName" column="VPC_NAME"/>
        <result property="orderId" column="ORDER_ID"/>
        <collection property="vpcSubnetOrderList" ofType="com.datatech.slgzt.model.vo.vpc.VpcSubnetOrderResult">
            <id property="id" column="ID"/>
            <result property="subnetName" column="SUBNET_NAME"/>
            <result property="vpcId" column="VPC_ID"/>
            <result property="azCode" column="AZ_CODE"/>
            <result property="cidr" column="CIDR"/>
            <result property="startIp" column="START_IP"/>
            <result property="netmask" column="NETMASK"/>
            <result property="status" column="STATUS"/>
            <result property="deleted" column="DELETED"/>
        </collection>
    </resultMap>

    <select id="selectVpcWithSubnetList" resultMap="VpcWithSubnetMap">
        SELECT v.ID as VPC_ID, v.CREATED_TIME,v.VPC_NAME,v.ORDER_ID,
        s.ID,s.SUBNET_NAME,s.AZ_CODE,s.CIDR,s.START_IP,s.NETMASK,s.STATUS,s.DELETED
        FROM WOC_VPC_ORDER v
        LEFT JOIN WOC_VPC_SUBNET_ORDER s
        ON v.ID = s.VPC_ID
        where v.STATUS = 'SUCCESS' and v.DELETED = 1 and s.STATUS = 'SUCCESS' and s.DELETED = 1
        and v.RECOVERY_STATUS = 0
        <if test="sourceType != null and sourceType != ''">
            and SOURCE_TYPE = #{sourceType}
        </if>
        AND v.TENANT_ID = #{tenantId}
          AND v.REGION_CODE = #{regionCode}
          AND s.AZ_CODE = #{azCode}
        <if test="plane != null and plane != ''">
            AND v.PLANE = #{plane}
        </if>

    </select>

    <select id="selectVpcWithSubnetListEx" resultMap="VpcWithSubnetMap">
        SELECT v.ID as VPC_ID, v.CREATED_TIME,v.VPC_NAME,v.ORDER_ID,v.VPC_TYPE,
        s.ID,s.SUBNET_NAME,s.AZ_CODE,s.CIDR,s.START_IP,s.NETMASK,s.STATUS,s.DELETED
        FROM WOC_VPC_ORDER v
        LEFT JOIN WOC_VPC_SUBNET_ORDER s
        ON v.ID = s.VPC_ID
        where v.STATUS = 'SUCCESS' and v.DELETED = 1
        and v.RECOVERY_STATUS = 0 and SOURCE_TYPE = #{sourceType}
        AND v.TENANT_ID = #{tenantId}
        AND v.REGION_CODE = #{regionCode}
        <if test="id != null and id != ''">
            AND v.ID = #{id}
        </if>
        <if test="azCode != null and azCode != ''">
        AND s.AZ_CODE = #{azCode}
        </if>
        <if test="plane != null and plane != ''">
            AND v.PLANE = #{plane}
        </if>

    </select>

    <select id="selectVpcName" resultType="int">
        SELECT count(1)
        FROM WOC_VPC_ORDER v
        where  v.DELETED = 1
        and v.VPC_NAME = #{vpcName}
        and v.TENANT_ID = #{tenantId}
        and v.REGION_CODE = #{regionCode}
        and v.IPV4_CIDR = #{cidr}
    </select>


    <select id="selectVpcOrderList" resultType="com.datatech.slgzt.model.vo.vpc.VpcOrderResult">
        select v.ID,v.VPC_NAME, v.SUBNET_NUM,v.ORDER_ID,v.ORDER_CODE,v.ACCOUNT as billId,v.STATUS,v.CREATED_TIME,
        v.APPLY_USER_NAME as userName,r.NAME as poolName,v.TENANT_NAME as tenantName,v.RECOVERY_STATUS,
        v.CONFIRM_OR_NOT,v.MESSAGE,v.DOMAIN_CODE,v.DOMAIN_NAME,v.CATALOGUE_DOMAIN_CODE,v.CATALOGUE_DOMAIN_NAME,
        v.MODULE_NAME,v.MODULE_ID,v.BUSINESS_SYS_ID,v.BUSINESS_SYS_NAME,v.AZ_CODE,v.VPC_TYPE,v.SOURCE_TYPE,v.DESCRIPTION,
        CASE
        WHEN v.IPV4_CIDR IS NOT NULL THEN v.IPV4_CIDR
        ELSE v.IPV6_CIDR
        END AS CIDR
        from WOC_VPC_ORDER v
        LEFT JOIN MC_REGION_T r on r.CODE = v.REGION_CODE
        WHERE
        v.STATUS = 'SUCCESS' and v.DELETED = 1
        <if test="vpcTableVo.sourceType != null and vpcTableVo.sourceType != ''">
            and v.SOURCE_TYPE = #{vpcTableVo.sourceType}
        </if>
        <if test="vpcTableVo.ids != null and vpcTableVo.ids.size > 0">
            and v.ID in
            <foreach collection="vpcTableVo.ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="vpcTableVo.sourceTypeList != null and vpcTableVo.sourceTypeList.size > 0">
            and v.SOURCE_TYPE in
            <foreach collection="vpcTableVo.sourceTypeList" item="sourceType" open="(" separator="," close=")">
                #{sourceType}
            </foreach>
        </if>
        <if test="vpcTableVo.createdBy != null and vpcTableVo.createdBy != ''">
            AND v.CREATED_BY = #{vpcTableVo.createdBy}
        </if>
        <if test="vpcTableVo.businessSysId != null and vpcTableVo.businessSysId != 0">
            AND v.BUSINESS_SYS_ID = #{vpcTableVo.businessSysId}
        </if>
        <if test="vpcTableVo.vpcName != null and vpcTableVo.vpcName != ''">
            AND v.VPC_NAME LIKE CONCAT(CONCAT('%',#{vpcTableVo.vpcName}),'%')
        </if>
        <if test="vpcTableVo.poolName != null and vpcTableVo.poolName != ''">
            AND r.NAME LIKE CONCAT(CONCAT('%',#{vpcTableVo.poolName}),'%')
        </if>
        <if test="vpcTableVo.resourcePoolId != null and vpcTableVo.resourcePoolId != ''">
            AND r.ID = #{vpcTableVo.resourcePoolId}
        </if>
        <if test="vpcTableVo.tenantId != null and vpcTableVo.tenantId != 0">
            AND v.TENANT_ID = #{vpcTableVo.tenantId}
        </if>
        <if test="vpcTableVo.tenantName != null and vpcTableVo.tenantName != ''">
            AND v.TENANT_NAME LIKE CONCAT(CONCAT('%',#{vpcTableVo.tenantName}),'%')
        </if>
        <if test="vpcTableVo.userName != null and vpcTableVo.userName != ''">
            AND v.APPLY_USER_NAME LIKE CONCAT(CONCAT('%',#{vpcTableVo.userName}),'%')
        </if>
        <if test="vpcTableVo.subnetNum != null">
            AND v.SUBNET_NUM = #{vpcTableVo.subnetNum}
        </if>
        <if test="vpcTableVo.orderCode != null and vpcTableVo.orderCode != ''">
            AND v.ORDER_CODE LIKE CONCAT(CONCAT('%',#{vpcTableVo.orderCode}),'%')
        </if>
        <if test="vpcTableVo.status != null and vpcTableVo.status != ''">
            AND v.STATUS = #{vpcTableVo.status}
        </if>
        <if test="vpcTableVo.azCode != null and vpcTableVo.azCode != ''">
            AND v.AZ_CODE = #{vpcTableVo.azCode}
        </if>
        <if test="vpcTableVo.cidr != null and vpcTableVo.cidr != ''">
            AND (v.IPV4_CIDR LIKE CONCAT(CONCAT('%',#{vpcTableVo.cidr}),'%')
            or v.IPV6_CIDR LIKE CONCAT(CONCAT('%',#{vpcTableVo.cidr}),'%'))
        </if>
        <if test="vpcTableVo.createdTimeStart != null and vpcTableVo.createdTimeStart != '' and vpcTableVo.createdTimeEnd != null and vpcTableVo.createdTimeEnd != ''">
            AND v.CREATED_TIME BETWEEN TO_DATE(#{vpcTableVo.createdTimeStart}, 'YYYY-MM-DD HH24:MI:SS') AND
            TO_DATE(#{vpcTableVo.createdTimeEnd}, 'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="vpcTableVo.tenantIds != null and vpcTableVo.tenantIds.size > 0">
            and v.TENANT_ID in
            <foreach collection="vpcTableVo.tenantIds" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <!--<if test="vpcTableVo.recoveryStatus != null and vpcTableVo.recoveryStatus == 3">
            AND v.RECOVERY_STATUS != #{vpcTableVo.recoveryStatus}
        </if>-->
        order by v.CREATED_TIME desc
    </select>

    <select id="selectVpcByOrderId" resultType="com.datatech.slgzt.dao.model.vpc.VpcOrder">
        select vpc.*
        from WOC_VPC_ORDER vpc
        where vpc.ORDER_ID = #{orderId}
    </select>
    <select id="selectByIds" resultType="com.datatech.slgzt.dao.model.vpc.VpcOrder">
        select *
        from WOC_VPC_ORDER
        where
          DELETED = 1
          <if test="recoveryStatus != null">
            <if test="recoveryStatus == 0">
                and (RECOVERY_STATUS is null or RECOVERY_STATUS = #{recoveryStatus} )
            </if>
              <if test="recoveryStatus != 0">
                  and RECOVERY_STATUS = #{recoveryStatus}
              </if>
          </if>
          and ID in
          <foreach collection="vpcIds" item="vpcId" open="(" separator="," close=")">
              #{vpcId}
          </foreach>
    </select>
    <select id="selectByOrderIds" resultType="com.datatech.slgzt.dao.model.vpc.VpcOrder">
        select *
        from
            WOC_VPC_ORDER
        where
            STATUS = 'SUCCESS'
          and DELETED = 1
          and RECOVERY_STATUS = #{recoveryStatus}
          and ORDER_ID in
          <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
              #{orderId}
          </foreach>
    </select>
    <select id="selectConfirmVpcs" resultType="com.datatech.slgzt.dao.model.vpc.VpcOrder">
        select *
        from WOC_VPC_ORDER
        where
            CONFIRM_OR_NOT = 1
            and ID IN
            <foreach collection="networkIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </select>

    <update id="updateStatusById" parameterType="com.datatech.slgzt.dao.model.vpc.VpcOrder">
        UPDATE WOC_VPC_ORDER
        SET STATUS = #{status}
        <if test="description != null and description != ''">
            , DESCRIPTION = #{description}
        </if>
        WHERE ID = #{id}
    </update>

    <update id="updateStatus" parameterType="com.datatech.slgzt.model.dto.network.StatusDTO">
        UPDATE WOC_VPC_ORDER
        SET STATUS = #{status}
        <if test="message != null and message != ''">
            , DESCRIPTION = #{message}
        </if>
        WHERE ID = #{id}
        <if test="currentStatus != null and currentStatus != ''">
            AND STATUS = #{currentStatus}
        </if>

    </update>
    <update id="updateRecoveryStatus">
        update
            WOC_VPC_ORDER
        set
            UPDATED_BY = #{updateBy},
            UPDATED_TIME = SYSDATE,
            RECOVERY_STATUS = #{recoveryStatus}
        WHERE
            ID in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </update>
    <update id="updateConfirmStatusByIds">
        update
            WOC_VPC_ORDER
        set
            UPDATED_BY = #{updatedBy},
            UPDATED_TIME = SYSDATE,
            CONFIRM_OR_NOT = #{confirmOrNot}
        WHERE
            ID in
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </update>

    <update id="updateRecoveryStatusById">
        update
        WOC_VPC_ORDER
        set
        UPDATED_TIME = SYSDATE,
        RECOVERY_STATUS = #{recoveryStatus},
        DELETED = #{deleted}
        <if test="updatedBy != null and updatedBy != 0">
            , UPDATED_BY = #{updatedBy}
        </if>
        <if test="message != null and message != ''">
            , MESSAGE = #{message}
        </if>
        WHERE
        ID = #{id}
    </update>

    <update id="updateSubnetRecoveryStatusById">
        update
        WOC_VPC_ORDER
        set
        SUBNET_RECOVERY_STATUS = #{subnetRecoveryStatus}
        <if test="message != null and message != ''">
            , MESSAGE = #{message}
        </if>
        WHERE
        ID = #{id}
    </update>

    <update id="updateRecoveryTimeById" parameterType="com.datatech.slgzt.dao.model.vpc.VpcOrder">
        update
        WOC_VPC_ORDER
        set
        RECOVERY_TIME = #{recoveryTime},
        UPDATED_TIME = SYSDATE,
        RECOVERY_STATUS = #{recoveryStatus},
        DELETED = #{deleted}
        <if test="updatedBy != null and updatedBy != 0">
            , UPDATED_BY = #{updatedBy}
        </if>
        WHERE
        ID = #{id}
    </update>

    <select id="selectByRecoveryTime" resultType="com.datatech.slgzt.dao.model.vpc.VpcOrder">
        select * from WOC_VPC_ORDER where RECOVERY_TIME &lt; CURRENT_TIMESTAMP
        and STATUS = 'SUCCESS'
        and  RECOVERY_STATUS = 2
        and  DELETED = 1
    </select>

    <select id="selectByIdAndRecoveryStatus" resultType="com.datatech.slgzt.dao.model.vpc.VpcOrder">
        select *
        from WOC_VPC_ORDER
        where STATUS = 'SUCCESS'
        and DELETED = 1
        <if test="recoveryStatus != null">
            and RECOVERY_STATUS = #{recoveryStatus}
        </if>
        and ID = #{id}
    </select>

    <select id="selectDetailById" resultType="com.datatech.slgzt.model.dto.network.NetworkDetailDTO">
        select v.VPC_NAME as name,v.DETAIL,v.FUNCTIONAL_MODULE,v.REGION_CODE,r.NAME as regionName,
        v.AZ_CODE,a.NAME as azName ,v.PLANE,v.TENANT_NAME as tenantName,v.TENANT_ID,
        v.DOMAIN_CODE,v.DOMAIN_NAME,v.CATALOGUE_DOMAIN_CODE,v.CATALOGUE_DOMAIN_NAME,
        v.MODULE_NAME,v.MODULE_ID,v.BUSINESS_SYS_ID,v.BUSINESS_SYS_NAME
        from WOC_VPC_ORDER v
        left join MC_REGION_T r on v.REGION_CODE = r.CODE
        left join MC_AZ_T a on a.CODE = v.AZ_CODE
        where v.ID = #{id}
    </select>
    <select id="selectRecentVpc" resultType="com.datatech.slgzt.dao.model.ResourceDetailDO">
        SELECT
            ovo.ORDER_ID,
            'vpc' AS TYPE,
            oo.CREATE_TIME,
            TO_CHAR(oo.CREATE_TIME, 'MM') AS month
        FROM
            WOC_STANDARD_WORK_ORDER oo
            JOIN WOC_VPC_ORDER ovo ON oo.ID = ovo.ORDER_ID
        WHERE
            ovo.STATUS = 'SUCCESS' and SOURCE_TYPE = #{sourceType}
            AND oo.CREATE_TIME &gt;= TRUNC(ADD_MONTHS(SYSDATE, -1), 'MM')
            AND oo.CREATE_TIME &lt; TRUNC(ADD_MONTHS(SYSDATE, 1), 'MM')
            <if test="recoveryType != null">
                AND ovo.RECOVERY_STATUS != #{recoveryType}
                and ovo.RECOVERY_STATUS != null
            </if>
            <if test="businessIds != null and businessIds.size > 0">
                and oo.BUSI_SYSTEM_ID in
                <foreach collection="businessIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
    </select>

    <select id="selectVpcRecoveryList" resultType="com.datatech.slgzt.model.vo.vpc.VpcOrderResult">
        select v.ID,v.VPC_NAME,v.ACCOUNT as billId, r.NAME as poolName, v.TENANT_NAME as tenantName,v.MESSAGE,v.REGION_CODE,
        v.CATALOGUE_DOMAIN_CODE,v.CATALOGUE_DOMAIN_NAME,v.DOMAIN_CODE,v.DOMAIN_NAME,v.ORDER_CODE,v.APPLY_USER_NAME as userName,v.CREATED_TIME,v.IPV4_CIDR as cidr,
        v.BUSINESS_SYS_ID, v.BUSINESS_SYS_NAME,v.TENANT_ID
        from WOC_VPC_ORDER v
        LEFT JOIN MC_REGION_T r on r.CODE = v.REGION_CODE
        WHERE
        v.STATUS = 'SUCCESS'
            and v.ID in
            <foreach collection="list" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        order by v.CREATED_TIME desc
    </select>


    <select id="selectByIdAndStatus" resultType="com.datatech.slgzt.dao.model.vpc.VpcOrder">
        select *
        from
        WOC_VPC_ORDER
        where
        id = #{id}
        and STATUS = 'SUCCESS'
        and DELETED = 1
    </select>

    <update id="updateDetailById" parameterType="com.datatech.slgzt.dao.model.vpc.VpcOrder">
        update
        WOC_VPC_ORDER
        set
        DETAIL = #{detail}
        WHERE ID = #{id}
    </update>

    <update id="updateSubnetNumById" parameterType="com.datatech.slgzt.dao.model.vpc.VpcOrder">
        update WOC_VPC_ORDER
        set SUBNET_NUM = #{subnetNum}
        WHERE ID = #{id}
    </update>

    <select id="selectNotUserModule" resultType="integer">
        select count(*)
        from WOC_VPC_ORDER
        where BUSINESS_SYS_ID = #{businessSysId}
        and MODULE_ID = #{moduleId}
        and REGION_CODE = #{regionCode}
        and DELETED = 1
    </select>
</mapper>