package com.datatech.slgzt.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 定时任务配置
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "schedule")
public class ScheduledTaskProperties {

    // 默认每天凌晨0点执行一次
    private String dgRecoveryCron = "0 0 0 * * ?";

    // 默认1小时一次
    private String dgShutdownCron = "0 0 0/1 * * ?";

    // 串行任务定时任务，默认2分钟一次
    private String serialTaskCron = "0 0/2 * * * ?";

    /**
     * 三级网段删除任务，默认5分钟一次
     */
    private String cmdbIP3DeleteCron = "0 0/5 * * * ?";

    /**
     * 三级网段存活时间，默认1小时
     */
    private int cmdbIP3DeleteThreshold = 60;

    // 是否开启串行执行，默认开启
    private boolean enableSerialTask = true;

    // 是否开启标准、回收、变更归于一类，只执行一个
    private boolean enableAllSerial = true;
}
