package com.datatech.slgzt.impl.manager;

import com.datatech.slgzt.convert.ActiveStatisticsManagerConvert;
import com.datatech.slgzt.dao.ActiveStatisticsDAO;
import com.datatech.slgzt.dao.model.ActiveStatisticsDO;
import com.datatech.slgzt.manager.ActiveStatisticsManager;
import com.datatech.slgzt.model.dto.ActiveStatisticsAggDTO;
import com.datatech.slgzt.model.dto.ActiveStatisticsDTO;
import com.datatech.slgzt.model.query.ActiveStatisticsAggQuery;
import com.datatech.slgzt.model.query.ActiveStatisticsQuery;
import com.datatech.slgzt.utils.BitMapUtil;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.PageResult;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.roaringbitmap.longlong.Roaring64NavigableMap;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 活跃统计Manager
 */
@Component
public class ActiveStatisticsManagerImpl implements ActiveStatisticsManager {

    @Resource
    private ActiveStatisticsDAO activeStatisticsDAO;

    @Resource
    private ActiveStatisticsManagerConvert activeStatisticsManagerConvert;

    /**
     * 保存统计数据
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(ActiveStatisticsDTO dto) {
        ActiveStatisticsDO dataDO = activeStatisticsManagerConvert.convertToDO(dto);
        activeStatisticsDAO.insert(dataDO);
    }

    /**
     * 删除统计数据
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(String id) {
        activeStatisticsDAO.delete(id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByStatTime(String statTime) {
        activeStatisticsDAO.deleteByStatTime(statTime);
    }

    /**
     * 根据ID查询
     */
    @Override
    public ActiveStatisticsDTO getById(String id) {
        ActiveStatisticsDO dataDO = activeStatisticsDAO.getById(id);
        return activeStatisticsManagerConvert.convert(dataDO);
    }

    /**
     * 分页查询 - 使用PageHelper实现分页
     */
    @Override
    public PageResult<ActiveStatisticsDTO> page(ActiveStatisticsQuery query) {
        Page<Object> pageInfo = PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<ActiveStatisticsDO> list = activeStatisticsDAO.list(query);
        List<ActiveStatisticsDTO> records = activeStatisticsManagerConvert.convert(list);

        return new PageResult<>(records, pageInfo.getTotal(), pageInfo.getPageNum(), pageInfo.getPageSize());
    }

    /**
     * 按日期范围查询
     */
    @Override
    public List<ActiveStatisticsDTO> listByDateRange(LocalDate startDate, LocalDate endDate) {
        ActiveStatisticsQuery query = new ActiveStatisticsQuery();
        query.setStartDate(startDate);
        query.setEndDate(endDate);

        List<ActiveStatisticsDO> list = activeStatisticsDAO.list(query);
        return activeStatisticsManagerConvert.convert(list);
    }

    /**
     * 根据日期查询
     */
    @Override
    public ActiveStatisticsDTO getByDate(LocalDate date) {
        ActiveStatisticsDO dataDO = activeStatisticsDAO.getByDate(date);
        return activeStatisticsManagerConvert.convert(dataDO);
    }

    @Override
    public List<ActiveStatisticsAggDTO> aggregateByTimeRange(ActiveStatisticsAggQuery query) {
        // 参数校验
        if (query.getStartDate().isAfter(query.getEndDate())) {
            throw new IllegalArgumentException("开始日期不能晚于结束日期");
        }

        // 获取时间范围内的数据
        List<ActiveStatisticsDO> dataList = activeStatisticsDAO.listByDateRange(query.getStartDate(), query.getEndDate());

        // 根据聚合类型进行分组统计
        Map<String, List<ActiveStatisticsDO>> groupedData;
        List<String> allTimePoints; // 所有可能的时间点

        switch (query.getAggType().toUpperCase()) {
            case "DAY":
                // 按小时分组（0-23）
                groupedData = dataList.stream()
                    .collect(Collectors.groupingBy(data -> data.getStatTime().substring(8, 10)));
                // 生成所有小时点
                allTimePoints = generateTimePoints(0, 23);
                break;
            case "WEEK":
                // 按周几分组（1-7，1代表周一）
                groupedData = dataList.stream()
                    .collect(Collectors.groupingBy(data -> {
                        int dayOfWeek = data.getStatDate().getDayOfWeek().getValue(); // 获取周几（1-7）
                        return String.format("%d", dayOfWeek);
                    }));
                // 生成周一到周日（1-7）
                allTimePoints = generateTimePoints(1, 7, false);
                break;
            case "MONTH":
                // 按日分组（1-31）
                groupedData = dataList.stream()
                    .collect(Collectors.groupingBy(data -> String.format("%02d", data.getStatDate().getDayOfMonth())));
                // 生成所有日期点（1-31）
                allTimePoints = generateTimePoints(1, 31);
                break;
            case "YEAR":
                // 按月分组（1-12）
                groupedData = dataList.stream()
                    .collect(Collectors.groupingBy(data -> String.format("%02d", data.getStatDate().getMonthValue())));
                // 生成所有月份点（1-12）
                allTimePoints = generateTimePoints(1, 12);
                break;
            default:
                throw new IllegalArgumentException("不支持的聚合类型：" + query.getAggType());
        }

        // 计算聚合结果并补齐缺失的时间点
        return allTimePoints.stream()
            .map(timePoint -> {
                ActiveStatisticsAggDTO aggDTO = new ActiveStatisticsAggDTO();
                aggDTO.setTimePoint(timePoint);

                List<ActiveStatisticsDO> group = groupedData.getOrDefault(timePoint, Collections.emptyList());
                if (group.isEmpty()) {
                    // 如果该时间点没有数据，设置默认值0
                    aggDTO.setActiveUserCount(0L);
                    aggDTO.setUserLoginCount(0L);
                    aggDTO.setClickCount(0L);
                    aggDTO.setApiAccessCount(0L);
                } else {
                    // 计算各指标的总和
                    //计算用户活跃数最新版本
                    Roaring64NavigableMap activeUserBitmap = new Roaring64NavigableMap();
                    for (ActiveStatisticsDO activeStatisticsDO : group) {
                           if(ObjNullUtils.isNotNull(activeStatisticsDO.getActiveUserCountV())){
                               //转成bitmap
                               Roaring64NavigableMap roaring64NavigableMap = BitMapUtil.base64ToRoaring(activeStatisticsDO.getActiveUserCountV());
                               activeUserBitmap.or(roaring64NavigableMap);
                           }
                    }
                    long newActiveUserCount = activeUserBitmap.getIntCardinality();
                    aggDTO.setActiveUserCount(newActiveUserCount);
                    aggDTO.setUserLoginCount(group.stream().map(ActiveStatisticsDO::getUserLoginCount).max(Long::compare).orElse(0L));
                    aggDTO.setClickCount(group.stream().mapToLong(ActiveStatisticsDO::getClickCount).sum());
                    aggDTO.setApiAccessCount(group.stream().mapToLong(ActiveStatisticsDO::getApiAccessCount).sum());
                }

                return aggDTO;
            })
            .sorted(Comparator.comparing(ActiveStatisticsAggDTO::getTimePoint))
            .collect(Collectors.toList());
    }

    /**
     * 生成指定范围内的所有时间点
     * @param start 开始数字（包含）
     * @param end 结束数字（包含）
     * @return 格式化的时间点列表（两位数字格式）
     */
    private List<String> generateTimePoints(int start, int end) {
        return generateTimePoints(start, end, true);
    }

    /**
     * 生成指定范围内的所有时间点
     * @param start 开始数字（包含）
     * @param end 结束数字（包含）
     * @param padZero 是否需要补零
     * @return 格式化的时间点列表
     */
    private List<String> generateTimePoints(int start, int end, boolean padZero) {
        return java.util.stream.IntStream.rangeClosed(start, end)
            .mapToObj(i -> padZero ? String.format("%02d", i) : String.format("%d", i))
            .collect(Collectors.toList());
    }
}