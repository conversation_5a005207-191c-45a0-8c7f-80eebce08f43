package com.datatech.slgzt.model.dto.vpc;

import com.datatech.slgzt.model.AllocationPool;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class VpcSubnetDTO implements Serializable {

    //子网id，绑定时必传
    private String subnetId;
    private String id;
    /**
     * 子网网段
     */
    private String cidr;

    /**
     * 子网可用区编码
     */
    private String azCode;

    /**
     * 子网ip
     */
    private String gatewayIp;

    /**
     * 子网名称
     */
    private String subnetName;

    /**
     * 是否创建IPv6子网
     * false（默认）：不创建
     * true：创建
     */
    private Boolean ipv6Enable = false;

    /**
     * 子网是否开启dhcp功能false（默认）不开启 true开启
     */
    private Boolean dhcpEnable = false;

    /**
     * 子网描述  非必传
     */
    private String description;

    /**
     * 子网dns服务器地址1
     */
    private String primaryDns;

    /**
     * 子网dns服务器地址2
     */
    private String secondaryDns;

    /**
     * 开始ip
     */
    private String startIp;

    /**
     * 子网掩码
     */
    private String netmask;

    /**
     * Array<String> 非必传		dns服务器地址，推荐最多支持2个IP
     */
    private List<String> dnsNameServices;

    private String level2InstanceId;
    private String instanceId;
    private String uuid;

    private List<AllocationPool> allocationPools;

}
